
FROM 192.168.1.51:8089/library/golang:1.21 as builder

WORKDIR /app/src/base
COPY --from=basesrc fancy-common/go.mod fancy-common/go.mod
COPY --from=basesrc fancy-common/go.sum fancy-common/go.sum
COPY --from=basesrc fancy-common/pkg fancy-common/pkg
COPY --from=basesrc frameworks frameworks

WORKDIR /go
COPY --from=pkg . pkg/

# Set destination for COPY
WORKDIR /app/src/fancygame/assetsrv

# Download Go modules
COPY . .
RUN go mod tidy

# Copy the source code. Note the slash at the end, as explained in
# https://docs.docker.com/reference/dockerfile/#copy

# Build
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o assetsrv ./cmd/main.go

FROM 192.168.1.51:8089/library/alpine

WORKDIR /app/fancygame/bin/assetsrv

COPY --from=builder /app/src/fancygame/assetsrv/assetsrv .
COPY --from=builder /app/src/fancygame/assetsrv/cmd/config.yml .

CMD ["./assetsrv"]