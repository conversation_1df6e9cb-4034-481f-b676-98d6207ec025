# frameworks

框架说明
* 提供标准化通用框架
* 封装工具类函数等
* 统一各类标准库

## 1. 基础库
- dict（标准词典）
- kit（封装组件）
  - dlm 分布式锁
  - driver 服务标准基础类
  - env 环境
  - mysql mysql操作标准库
  - network 网络库
  - nsq 消息队列
  - rds redis操作标准库
  - redisfactory redis工厂
  - rpc grpc封装库
  - safego 安全goroutine
  - transport 传输，交互
  - version 版本工具
- lib（基础库）

## 2.config.yaml配置说明
#### log相关配置
    # 日志级别
    log_level: trace
    # 写日志文件路径，只有配置log_write生效，后续才有用
    log_write: true
    log_dir: .././log/loginsrv/
    # 统一定位Json输出，配置为true则输出为格式化的Json
    log_json: true

### 服务端口配置
    # server name
    rpc_server_name: loginsrv
    # 服务IP地址（可选），默认读localip
    rpc_addr: 127.0.0.1 
    rpc_port: 10004
    http_port: 10003
    # gin调试模式，默认release （release debug test）
    env_mode = debug

### 其它服务配置
    consul_addr：************:8500
    nsqd_addr: 127.0.0.1:4150
    nsqd_http_addr: 127.0.0.1:4151
    # 控制nsqd将多少消息同时发送给消费者, 默认是1
    nsq_max_inflight：20
    nsqlookupd_addrs:
    - 127.0.0.1:4161

## 3.微服务标准
### 3.1 服务文件结构
  - cmd
    - config.yml
    - main.go
  - config 配置相关
    - const 常量定义
    - var redis的Key format函数等
    - config相关处理
  - internal
    - dao 数据库操作相关
    - logic 逻辑封装实现
    - model 实体类，数据库及其它结构体定义
      - t_player_master.go
      - player_bean.go
    - proc 消息处理handler，回调函数更多的是实现调用，其它放到logic或者repo
    - repo 一些业务封装处理
    - pubsub 发布订阅
    - server 服务实体，更偏向网络传输相关
      - rpc
      - http
      - udp
    - services 业务服务实体，可以理解是server提供服务的具体实现
      - login_visitor.go
      - login_fb.go
      - login_wechat.go
      - login_quick_sdk.go
    - jobwork 作业服务
      - 调度任务处理等
## 4. 服务框架约定
### 4.1 环境
env.Cluster ： 集群环境。如果配置了集群名，如下功能会添加前缀
- nsq订阅channel，开发环境的时候区分个人环境
- redis分布式Key，有些key需要区分集群