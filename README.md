# activitysrv

活动服务，负责处理游戏中的各种活动系统。

## 功能特性

- 接收事件更新玩家活动数据
- 支持多种活动类型的抽象处理
- 活动进度跟踪和奖励发放
- 活动状态管理

## 架构设计

```mermaid
mindmap
root((activitysrv))
    事件处理
        登录事件
        游戏行为事件
        活动相关事件
    活动管理
        活动类型抽象
        活动进度更新
        活动状态管理
    奖励发放
        活动奖励
        进度奖励
        特殊奖励
    数据管理
        缓存层(Redis)
        持久化层(MySQL)
        数据一致性
```

## 目录结构

- `cmd/` - 应用程序入口
- `config/` - 配置相关
- `internal/` - 核心应用逻辑
  - `dao/` - 数据访问层
  - `logic/` - 业务逻辑层
  - `model/` - 数据模型
  - `services/` - 服务层
  - `pubsub/` - 消息订阅
  - `rpc/` - RPC服务实现 