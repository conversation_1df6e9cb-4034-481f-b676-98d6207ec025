# 钓鱼通用依赖库

用途
* 提供标准化
* 游戏内各种PKG函数
* 提供给其它服务的业务依赖，耦合（例如通用的RPC服务封装）

## 基础库
- configs 配置文件submodule
- docs 文档
- pkg
  - cmodel 配置文件桩代码
  - cpb 生成PB文件
  - dictionary 字典
  - intranetrpc 内部通信RPC
  - model 通用模型
  - pubsub 订阅发布
  - repo 存储代码
  - rpc_biz 业务RPC封装
  - utility 工具类
- protocols proto子模块
- scripts 脚本

## proto约定
git submodule add http://xxxxxx.xxxx.git

## fancy-common工程初始化子快
    git submodule init
    git submodule update
