#!/bin/bash

# 设置目标目录
TARGET_DIR="/Users/<USER>/go/src/fancygame"

# 遍历目标目录下所有以srv结尾的目录
for srv_dir in $(find "$TARGET_DIR" -type d -name "*srv"); do
    echo "处理服务: $srv_dir"

    # 进入服务目录
    cd "$srv_dir"

    # 检查是否是git仓库
    if [ -d ".git" ]; then
        echo "这是一个Git仓库，执行Git操作..."

        # 检查并切换到develop分支
        git checkout develop
        if [ $? -ne 0 ]; then
            echo "切换到develop分支失败，跳过此仓库"
            continue
        fi

        # 拉取最新代码
        git pull
        if [ $? -ne 0 ]; then
            echo "拉取代码失败，跳过此仓库"
            continue
        fi

        # 创建并切换到新的audit分支
        git checkout -b audit
        if [ $? -ne 0 ]; then
            echo "创建audit分支失败，跳过此仓库"
            continue
        fi

        # 推送新分支到远程
        git push -u origin audit
        if [ $? -ne 0 ]; then
            echo "推送audit分支失败"
        else
            echo "成功处理仓库: $srv_dir"
        fi
    else
        echo "不是Git仓库，跳过: $srv_dir"
    fi

    echo "------------------------"
done

echo "所有操作完成！"
