概述
产品文档：【系统功能】-爆护之路
爆护之路是游戏的中线追求系统，以玩家累计钓获鱼的重量为核心目标。

---
 业务流程

## 1. 入护成功事件处理
当玩家入护成功时，系统收到WeightUpdate事件，处理流程如下：
1. **配置过滤**：首先通过本地缓存快速检查活动是否开启，活动时间是否有效
2. **周期管理**：检查当前周期是否已结束，如果结束则自动创建新周期，确保数据写入正确的周期
3. **指标更新**：使用Redis事务原子性更新玩家在当前周期的指标数据，支持累加和最大值两种更新方式
4. **数据持久化**：将更新后的完整用户数据JSON序列化后存储到Redis，设置合理的过期时间

## 2. 玩家查询活动进度
当玩家打开爆护之路界面时：
1. **获取周期信息**：读取当前活动周期的开始时间、结束时间和周期ID
2. **读取用户数据**：从Redis获取玩家在当前周期的完整活动数据，包括所有指标值和已领取记录
3. **数据组装**：将周期信息和用户数据组合，返回给客户端用于界面渲染
4. **兼容处理**：如果用户数据不存在，返回初始化的空数据结构

## 3. 玩家领取奖励
玩家点击领取奖励时：
1. **权限校验**：验证活动是否在有效期内，周期是否已结束
2. **条件检查**：检查玩家当前指标是否满足该阶段的领取要求
3. **重复检查**：通过已领取记录map检查该阶段是否已经领取过
4. **原子领取**：使用Redis WATCH事务原子性记录领取时间和奖励配置
5. **发放奖励**：调用大厅服务的发奖接口，实际发放奖励到玩家背包
6. **异常处理**：如果发奖失败，记录失败日志用于后续补偿

## 4. 周期自动切换
系统通过事件驱动方式自动管理周期切换：
1. **被动检测**：每次事件触发时检查当前周期是否已过期
2. **自动创建**：如果周期已结束，自动创建下一个周期，周期ID递增
3. **历史保留**：将结束的周期信息保存到历史记录中，用于数据查询和兼容性处理
4. **延迟兼容**：支持处理埋点服务延迟推送的事件，将数据写入正确的历史周期

## 5. 客户端红点判断
在登录和旅行结束时触发红点逻辑：
1. **数据获取**：获取玩家当前周期的所有指标数据
2. **条件对比**：遍历所有阶段配置，检查哪些阶段满足领取条件但尚未领取
3. **红点状态**：如果存在可领取的奖励，返回红点提示
4. **缓存优化**：红点状态可以缓存一段时间，减少频繁计算

## 6. 数据一致性保障
1. **事务操作**：所有关键数据更新使用Redis WATCH事务保证原子性
2. **重试机制**：对于并发冲突，实现指数退避重试策略
3. **容错降级**：Redis连接失败时的降级处理和错误恢复
4. **监控告警**：关键操作的成功率监控和异常告警机制


---
配置表设计
activity.xlsx 表
1. ActivityConst – 活动定义
暂时无法在飞书文档外展示此内容
2. Activity – 活动详情
暂时无法在飞书文档外展示此内容
3. Stages – 阶段列表
暂时无法在飞书文档外展示此内容
4. StageRewards – 阶段奖励
暂时无法在飞书文档外展示此内容

---

 Redis数据模型设计

## 1. 数据结构定义

### 1.1 活动周期数据结构
活动周期信息采用JSON格式存储，包含以下字段：
- **cycle_id**: 周期ID，从1开始递增
- **start_time**: 周期开始时间戳
- **end_time**: 周期结束时间戳
- **status**: 周期状态，1表示活跃，2表示结束
- **created_at**: 创建时间戳

### 1.2 玩家活动数据结构
玩家活动数据统一存储在一个JSON对象中，包含：
- **metrics**: map[int32]int64，存储累加类型的指标值，key为指标类型，value为数值
- **metrics_max**: map[int32]int64，存储最大值类型的指标，key为指标类型，value为最大值
- **metrics_extra**: map[int32]string，扩展字段，key为指标类型，value为JSON字符串
- **claimed_stages**: map[int32]int64，已领取阶段记录，key为阶段ID，value为领取时间戳
- **reward_configs**: map[int32]string，奖励配置记录，key为阶段ID，value为奖励配置JSON
- **updated_at**: 最后更新时间戳
- **version**: 数据版本号，用于兼容性管理

### 1.3 指标操作类型
- **累加操作(ADD)**: 新值累加到现有值上，用于鱼重量、数量等累计指标
- **最大值操作(MAX)**: 保留历史最大值，用于单次最大鱼重等指标
- **直接设置(SET)**: 直接覆盖现有值，用于状态类指标

## 2. Redis Key设计规范

### 2.1 命名空间前缀
所有Redis key统一使用前缀：`activity:explosive_protection:`

### 2.2 Key规范定义

#### A. 活动当前周期Key
- **格式**: `activity:explosive_protection:{activity_id}:current_cycle`
- **类型**: String
- **内容**: ActivityCycle结构的JSON字符串
- **TTL**: 周期时长+1天
- **示例**: `activity:explosive_protection:1001:current_cycle`

#### B. 玩家活动数据Key
- **格式**: `activity:explosive_protection:{activity_id}:user:{user_id}:data:{cycle_id}`
- **类型**: String  
- **内容**: UserActivityData结构的JSON字符串
- **TTL**: 30天（指标数据）/ 90天（奖励记录）
- **示例**: `activity:explosive_protection:1001:user:12345:data:3`

#### C. 活动周期历史Key
- **格式**: `activity:explosive_protection:{activity_id}:cycle_history`
- **类型**: ZSet
- **Score**: 周期ID
- **Member**: ActivityCycle结构的JSON字符串
- **TTL**: 永久保留
- **示例**: `activity:explosive_protection:1001:cycle_history`

#### D. 全局统计Key
- **格式**: `activity:explosive_protection:{activity_id}:stats:{cycle_id}`
- **类型**: ZSet
- **Score**: 领取时间戳
- **Member**: "{user_id}:{stage_id}"格式的字符串
- **TTL**: 90天
- **示例**: `activity:explosive_protection:1001:stats:3`

#### E. 数据版本管理Key
- **格式**: `activity:explosive_protection:schema_version`
- **类型**: String
- **内容**: 版本号字符串（如"v2.0"）
- **TTL**: 永久保留

## 3. 数据操作策略

### 3.1 周期管理策略
1. **被动检测**: 每次事件触发时检查当前周期是否过期
2. **自动创建**: 过期时自动创建新周期，周期ID从历史记录中获取最大值+1
3. **原子操作**: 使用Redis Pipeline保证周期创建和历史记录的原子性
4. **过期设置**: 当前周期key设置为周期时长+1天的TTL

### 3.2 用户数据更新策略
1. **事务保证**: 使用Redis WATCH机制保证并发更新的原子性
2. **完整读写**: 每次读取完整的用户数据JSON，更新后整体写回
3. **版本兼容**: 读取数据时进行版本检查和自动升级
4. **过期管理**: 根据数据类型设置不同的TTL（指标30天，奖励90天）

### 3.3 领奖记录策略
1. **重复检查**: 通过claimed_stages map检查是否已领取
2. **原子领取**: 使用Redis事务同时更新用户数据和统计数据
3. **配置保存**: 将奖励配置JSON保存到reward_configs中，便于问题排查
4. **统计记录**: 同时写入全局统计ZSet，便于数据分析

#### F. 热点数据缓存Key（可选）
- **格式**: `activity:explosive_protection:hot:{activity_id}:cycle_cache`
- **类型**: String
- **内容**: 当前周期信息的缓存
- **TTL**: 1小时

#### G. 用户会话缓存Key（可选）
- **格式**: `activity:explosive_protection:session:{user_id}:cache`
- **类型**: Hash
- **字段**: activity_id -> 简化的用户数据
- **TTL**: 30秒

#### H. 分布式锁Key
- **格式**: `activity:explosive_protection:lock:{activity_id}:cycle_create`
- **类型**: String
- **内容**: 锁持有者标识
- **TTL**: 30秒

## 3. 数据操作策略

### 3.1 周期管理策略
1. **被动检测**: 每次事件触发时检查当前周期是否过期
2. **自动创建**: 过期时自动创建新周期，周期ID从历史记录中获取最大值+1
3. **原子操作**: 使用Redis Pipeline保证周期创建和历史记录的原子性
4. **过期设置**: 当前周期key设置为周期时长+1天的TTL
5. **并发保护**: 使用分布式锁防止并发创建多个周期

### 3.2 用户数据更新策略
1. **事务保证**: 使用Redis WATCH机制保证并发更新的原子性
2. **完整读写**: 每次读取完整的用户数据JSON，更新后整体写回
3. **版本兼容**: 读取数据时进行版本检查和自动升级
4. **过期管理**: 根据数据类型设置不同的TTL（指标30天，奖励90天）
5. **重试机制**: 并发冲突时实现指数退避重试，最多重试3次

### 3.3 领奖记录策略
1. **重复检查**: 通过claimed_stages map检查是否已领取
2. **原子领取**: 使用Redis事务同时更新用户数据和统计数据
3. **配置保存**: 将奖励配置JSON保存到reward_configs中，便于问题排查
4. **统计记录**: 同时写入全局统计ZSet，便于数据分析
5. **事务ID**: 生成唯一的事务ID用于操作追踪

### 3.4 性能优化策略
1. **批量操作**: 使用Pipeline进行批量Redis操作，减少网络往返
2. **连接池**: 合理配置Redis连接池，避免连接资源耗尽
3. **数据压缩**: 对大JSON数据考虑压缩存储
4. **分片设计**: 按用户ID hash进行数据分片，避免热点key

## 4. 详细业务逻辑设计

### 4.1 周期管理详细流程
#### 4.1.1 周期检查逻辑
1. **获取当前周期**: 从Redis读取`current_cycle` key
2. **时间比较**: 将当前时间与周期结束时间比较
3. **状态判断**: 检查周期状态是否为活跃
4. **数据有效性**: 验证周期数据的完整性

#### 4.1.2 新周期创建流程
1. **获取分布式锁**: 防止并发创建周期
2. **历史周期查询**: 从`cycle_history` ZSet获取最大周期ID
3. **新周期计算**: 计算新周期的开始时间、结束时间
4. **原子创建**: 使用Pipeline同时创建当前周期和历史记录
5. **释放锁**: 完成后释放分布式锁

#### 4.1.3 周期切换兼容性
1. **延迟事件处理**: 支持事件时间戳回溯匹配历史周期
2. **数据补偿**: 周期切换时可能丢失的数据补偿机制
3. **状态同步**: 确保所有节点的周期状态一致

### 4.2 指标更新详细流程
#### 4.2.1 数据读取和验证
1. **获取用户数据**: 从Redis读取完整的UserActivityData
2. **数据初始化**: 如果数据不存在，创建初始化结构
3. **版本检查**: 检查数据版本，必要时进行结构升级
4. **数据校验**: 验证数据格式的正确性

#### 4.2.2 指标计算逻辑
1. **累加计算**: 对于累加类型指标，将新值加到现有值上
2. **最大值计算**: 对于最大值类型指标，比较并保留较大值
3. **扩展字段**: 处理特殊的扩展字段更新
4. **边界检查**: 防止数值溢出或异常值

#### 4.2.3 原子更新机制
1. **乐观锁**: 使用WATCH命令监视key的变化
2. **事务执行**: 在MULTI/EXEC事务中执行更新
3. **冲突处理**: 事务失败时的重试逻辑
4. **性能优化**: 减少不必要的序列化操作

### 4.3 奖励领取详细流程
#### 4.3.1 前置条件检查
1. **活动状态**: 验证活动是否在有效期内
2. **周期状态**: 确认当前周期未结束
3. **用户权限**: 验证用户身份和权限
4. **阶段配置**: 检查阶段配置的有效性

#### 4.3.2 领取条件验证
1. **指标检查**: 验证用户指标是否满足领取要求
2. **重复检查**: 确认该阶段未被领取过
3. **依赖检查**: 如果有前置阶段要求，进行验证
4. **时间窗口**: 检查是否在允许的领取时间内

#### 4.3.3 奖励发放流程
1. **记录领取**: 原子性更新领取记录和时间戳
2. **统计更新**: 同时更新全局统计数据
3. **奖励调用**: 调用外部奖励服务发放实际奖励
4. **异常处理**: 发奖失败时的回滚和补偿机制

## 5. 兼容性设计

### 5.1 数据版本控制
#### 5.1.1 版本管理机制
- 每个用户数据结构包含version字段，当前版本为1
- 全局schema_version key记录系统整体版本
- 支持向前兼容，新版本可以处理旧版本数据
- 提供数据迁移工具和脚本

#### 5.1.2 版本升级流程
1. **版本检测**: 读取数据时检查版本号
2. **自动升级**: 对旧版本数据进行结构升级
3. **字段补全**: 为缺失字段设置默认值
4. **验证测试**: 升级后进行数据完整性验证

### 5.2 延迟事件处理
#### 5.2.1 历史周期查询
1. **时间匹配**: 根据事件时间戳查找对应的历史周期
2. **数据查询**: 从cycle_history ZSet中查询历史周期信息
3. **范围搜索**: 支持时间范围内的周期查询
4. **缓存优化**: 对常用的历史周期信息进行缓存

#### 5.2.2 延迟数据写入
1. **周期验证**: 确认目标周期的有效性
2. **数据合并**: 将延迟数据与现有数据合并
3. **完整性检查**: 确保数据更新的一致性
4. **审计记录**: 记录延迟事件的处理日志

### 5.3 数据迁移策略
#### 5.3.1 结构兼容性
1. **字段检查**: 检查必要字段是否存在
2. **类型转换**: 处理字段类型的变化
3. **默认值**: 为新增字段设置合理的默认值
4. **清理机制**: 清理废弃的字段和数据

#### 5.3.2 迁移工具
1. **批量迁移**: 支持大批量数据的迁移
2. **增量更新**: 支持增量数据的迁移
3. **回滚机制**: 迁移失败时的数据回滚
4. **进度监控**: 迁移过程的进度监控和报告


---
常量定义
// 活动类型
enum ACTIVITY_TYPE {
    AT_UNKNOWN        = 0;  // 未知
    AT_WEIGHT_PURSUIT = 1;  // 爆护之路（重量追求）
}

// 活动指标类型
enum ACTIVITY_METRIC_TYPE {
    AMT_UNKNOWN     = 0;  // 未知
    AMT_FISH_WEIGHT = 1;  // 鱼重量指标
    AMT_FISH_COUNT  = 2;  // 鱼数量指标
    AMT_FISH_VALUE  = 3;  // 鱼价值指标
}
 接口定义

////////////////////////////////////////接口
// 获取活动进度请求
message GetActivityProgressReq {
    int64 activity_id                                = 1; // 活动ID，可选，为0时返回全部活动数据
}

// 获取活动进度响应
message GetActivityProgressRsp {
    common.Result ret                                = 1; // 结果
    repeated ActivityProgress activity_progress_list = 2; // 活动进度列表
}

// 领取活动奖励请求
message ClaimActivityRewardReq {
    common.ACTIVITY_TYPE activity_id                 = 1; // 活动ID
    int32                stage_id                    = 2; // 阶段ID
}

// 领取活动奖励响应
message ClaimActivityRewardRsp {
    common.Result ret                                = 1; // 结果
    common.ACTIVITY_TYPE         activity_id         = 2; // 活动ID
    int32         stage_id                           = 3; // 阶段ID
    common.Reward reward                             = 4; // 奖励信息
}



////////////////////////////////////////参数
// 活动指标信息
message ActivityMetric {
    ACTIVITY_TYPE                       activity_id  = 1; // 活动ID
    int32                               cycle_id     = 2; // 周期ID
    common.ACTIVITY_METRIC_TYPE         metric_type  = 3; // 指标类型（鱼重等）
    int64                               metric_value = 4; // 指标当前值
    int64                               updated_at   = 5; // 更新时间戳
}

// 活动奖励领取记录
message ActivityRewardRecord {
    common.ACTIVITY_TYPE activity_id                 = 1; // 活动ID
    int32 cycle_id                                   = 2; // 周期ID
    int32 stage_id                                   = 3; // 阶段ID
    int64 claimed_at                                 = 4; // 领取时间戳
}

// 活动进度信息
message ActivityProgress {
    common.ACTIVITY_TYPE activity_id                 = 1; // 活动ID
    int32 current_cycle_id                           = 2; // 当前周期ID
    int64 cycle_end_time                             = 3; // 当前周期结束时间戳
    ActivityMetric metrics                           = 4; // 玩家指标列表
    repeated ActivityRewardRecord claimed_records    = 5; // 已领取记录列表
}

## 接口设计详细说明

### 1. 获取活动进度接口
**接口路径**: `/api/activity/explosive_protection/progress`
**请求方法**: POST
**功能描述**: 获取玩家当前活动周期的进度信息

#### 请求参数扩展
```protobuf
message GetActivityProgressReq {
    int64 activity_id        = 1; // 活动ID，0表示获取所有活动
    int64 user_id           = 2; // 玩家ID（服务端从token解析）
    bool  include_history   = 3; // 是否包含历史周期数据
    int32 history_limit     = 4; // 历史周期数量限制，默认5个
}
```

#### 响应参数扩展
```protobuf
message GetActivityProgressRsp {
    common.Result ret                    = 1; // 操作结果
    ActivityProgress current_progress    = 2; // 当前周期进度
    repeated ActivityProgress history    = 3; // 历史周期进度（可选）
    map<int32, string> stage_configs     = 4; // 阶段配置信息
    bool has_red_dot                     = 5; // 是否有红点提示
}
```

### 2. 领取奖励接口
**接口路径**: `/api/activity/explosive_protection/claim`
**请求方法**: POST
**功能描述**: 领取指定阶段的奖励

#### 请求参数扩展
```protobuf
message ClaimActivityRewardReq {
    int64 activity_id    = 1; // 活动ID
    int64 user_id       = 2; // 玩家ID（服务端从token解析）
    int32 stage_id      = 3; // 阶段ID
    string client_info  = 4; // 客户端信息（版本、设备等）
}
```

#### 响应参数扩展
```protobuf
message ClaimActivityRewardRsp {
    common.Result ret           = 1; // 操作结果
    int64 activity_id          = 2; // 活动ID
    int32 stage_id             = 3; // 阶段ID
    repeated common.Reward rewards = 4; // 奖励列表
    int64 claimed_at           = 5; // 领取时间戳
    string transaction_id      = 6; // 交易ID（用于追踪）
}
```

### 3. 红点检查接口
**接口路径**: `/api/activity/explosive_protection/red_dot`
**请求方法**: GET
**功能描述**: 检查是否有可领取的奖励

#### 请求参数
```protobuf
message CheckRedDotReq {
    int64 user_id           = 1; // 玩家ID
    repeated int64 activity_ids = 2; // 活动ID列表，空表示所有活动
}
```

#### 响应参数
```protobuf
message CheckRedDotRsp {
    common.Result ret                      = 1; // 操作结果
    map<int64, bool> activity_red_dots     = 2; // 各活动的红点状态
    int32 total_claimable_count           = 3; // 总可领取数量
}
```

## 性能优化策略

### 1. 缓存分层设计
#### L1缓存：内存缓存
- **活动配置缓存**: 将活动配置表缓存到应用内存中，减少配置查询
- **用户会话缓存**: 缓存用户当前周期的基础信息，避免重复Redis查询
- **TTL设置**: 配置缓存5分钟，用户会话缓存30秒

#### L2缓存：Redis缓存
- **热点数据预热**: 预加载活跃用户的活动数据
- **批量操作**: 使用Pipeline进行批量读写操作
- **分片策略**: 按用户ID hash分片，避免热key问题

### 2. 数据库优化
#### Redis优化
- **连接池管理**: 合理设置连接池大小，避免连接耗尽
- **内存管理**: 设置合理的maxmemory和过期策略
- **持久化策略**: 配置RDB+AOF混合持久化

#### 查询优化
- **Key设计优化**: 避免长key名，使用简短但有意义的命名
- **数据结构选择**: 根据访问模式选择最优的Redis数据结构
- **过期时间设置**: 合理设置TTL，避免内存泄漏

### 3. 并发控制
#### 事务处理
- **乐观锁**: 使用Redis WATCH实现乐观锁控制
- **重试机制**: 并发冲突时的指数退避重试
- **超时控制**: 设置合理的事务超时时间

#### 限流策略
- **接口限流**: 基于用户ID的接口调用频率限制
- **热点保护**: 对热点活动进行特殊的流量控制
- **熔断机制**: Redis异常时的服务降级

## 异常处理和容错

### 1. Redis异常处理
#### 连接异常
- **自动重连**: 实现连接断开后的自动重连机制
- **降级策略**: Redis不可用时的服务降级方案
- **数据补偿**: 异常恢复后的数据一致性检查和补偿

#### 数据异常
- **数据校验**: 读取Redis数据时的完整性校验
- **版本兼容**: 旧版本数据的自动升级处理
- **异常数据**: 损坏数据的修复和重建机制

### 2. 业务异常处理
#### 周期切换异常
- **时间校验**: 防止系统时间异常导致的周期错乱
- **数据一致性**: 周期切换时的数据一致性保证
- **回滚机制**: 周期创建失败时的回滚策略

#### 奖励发放异常
- **事务补偿**: 奖励发放失败时的补偿机制
- **重复检查**: 防止重复发放奖励的多重检查
- **异步处理**: 将奖励发放作为异步任务处理

### 3. 数据一致性保障
#### 最终一致性
- **事件溯源**: 记录所有关键操作的事件日志
- **数据对账**: 定期进行数据一致性检查
- **修复机制**: 发现不一致时的自动修复

#### 分布式一致性
- **分布式锁**: 关键操作的分布式锁保护
- **版本控制**: 数据版本号管理
- **冲突解决**: 并发修改时的冲突解决策略

## 监控和运维

### 1. 关键指标监控
#### 业务指标
- **活跃用户数**: 各活动的日活跃用户统计
- **参与率**: 用户参与活动的比例
- **领奖率**: 各阶段奖励的领取比例
- **留存率**: 跨周期的用户留存情况

#### 技术指标
- **接口响应时间**: P50、P95、P99响应时间
- **Redis性能**: 连接数、内存使用率、命令执行时间
- **错误率**: 各接口的错误率和异常统计
- **并发量**: 系统并发处理能力

### 2. 告警机制
#### 实时告警
- **服务异常**: 接口错误率超过阈值时告警
- **性能异常**: 响应时间超过阈值时告警
- **资源异常**: Redis内存、CPU使用率异常告警

#### 业务告警
- **数据异常**: 指标数据异常波动时告警
- **周期异常**: 周期切换失败或延迟告警
- **奖励异常**: 奖励发放失败率超过阈值告警

### 3. 日志记录
#### 操作日志
- **关键操作**: 记录所有关键业务操作的详细日志
- **错误日志**: 详细记录错误信息和堆栈信息
- **性能日志**: 记录慢查询和性能瓶颈信息

#### 审计日志
- **奖励发放**: 记录所有奖励发放的详细信息
- **数据变更**: 记录重要数据的变更历史
- **管理操作**: 记录管理员的后台操作

## 安全性考虑

### 1. 数据安全
#### 访问控制
- **权限验证**: 验证用户只能访问自己的活动数据
- **接口鉴权**: 所有接口都需要有效的用户token
- **IP白名单**: 管理接口限制IP访问

#### 数据保护
- **敏感数据**: 对敏感数据进行加密存储
- **数据脱敏**: 日志中的敏感信息脱敏处理
- **访问日志**: 记录所有数据访问操作

### 2. 业务安全
#### 防作弊
- **数据校验**: 严格校验用户提交的数据
- **行为分析**: 监控异常的用户行为模式
- **限制机制**: 防止恶意刷取奖励

#### 防攻击
- **请求限流**: 防止暴力请求攻击
- **参数校验**: 严格校验所有输入参数
- **SQL注入**: 防止注入攻击（虽然主要用Redis）

## 扩展性设计

### 1. 活动类型扩展
#### 通用框架
- **指标类型**: 通过配置支持新的指标类型
- **计算方式**: 支持不同的指标计算方式
- **奖励类型**: 支持多种奖励类型和发放方式

#### 配置驱动
- **活动配置**: 通过配置表驱动活动行为
- **阶段配置**: 灵活配置不同的奖励阶段
- **规则引擎**: 支持复杂的奖励规则配置

### 2. 系统扩展
#### 水平扩展
- **服务拆分**: 支持微服务化拆分
- **数据分片**: 支持Redis集群和数据分片
- **负载均衡**: 支持多实例负载均衡

#### 垂直扩展
- **缓存分层**: 支持多级缓存
- **存储分离**: 支持读写分离和冷热数据分离
- **计算分离**: 支持计算和存储的分离

---

