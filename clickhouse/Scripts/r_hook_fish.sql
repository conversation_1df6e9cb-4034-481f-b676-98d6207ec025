-- auto-generated definition
create table r_hook_fish_kafka
(
    Ktable         String,
    ProductID      UInt8,
    ChannelType    UInt32,
    Platform       UInt8,
    AppLanguage    UInt8,
    Country        String,
    AccType        UInt8,
    AppVersion     String,
    NowDate        Date,
    TimeStampValue DateTime,
    PlayerId       UInt64,
    PondId         Int64,
    BaitId         Int64,
    FishId         Int64,
    FishLength     Int32,
    FishWeight     Int32,
    FishExp        Int32
) engine = Kafka SETTINGS kafka_broker_list = 'ip-172-31-17-83.us-east-2.compute.internal:9092', kafka_topic_list = 'r_hook_fish', kafka_group_name = 'ck', kafka_format = 'CSV', format_csv_delimiter = '|', kafka_skip_broken_messages = 10;

-- auto-generated definition
create table r_hook_fish_ods
(
    Ktable         String,
    ProductID      UInt8,
    ChannelType    UInt32,
    Platform       UInt8,
    AppLanguage    UInt8,
    Country        String,
    AccType        UInt8,
    AppVersion     String,
    NowDate        Date,
    TimeStampValue DateTime,
    PlayerId       UInt64,
    PondId         Int64,
    BaitId         Int64,
    FishId         Int64,
    FishLength     Int32,
    FishWeight     Int32,
    FishExp        Int32
) engine = MergeTree PARTITION BY NowDate
        ORDER BY PlayerId
        SETTINGS index_granularity = 8192;

CREATE
MATERIALIZED VIEW r_hook_fish_view TO r_hook_fish_ods AS
SELECT *
FROM r_hook_fish_kafka;
