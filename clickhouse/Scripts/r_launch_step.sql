create table r_launch_step_kafka
(
    K<PERSON>             String,
    ProductID          UInt8,
    ChannelType        UInt32,
    Platform           UInt8,
    AppLanguage        UInt8,
    Country            String,
    AccType            UInt8,
    AppVersion         String,
    NowDate            Date,
    TimeStampValue     DateTime,
    PlayerId           UInt64,
    LaunchStepId       UInt32,
    LaunchStepDuration UInt64,
    LaunchFailDetail   String,
    DeviceCode         String,
    Expand             String
)
    engine = Kafka SETTINGS kafka_broker_list = 'ip-172-31-17-83.us-east-2.compute.internal:9092', kafka_topic_list = 'r_launch_step', kafka_group_name = 'ck', kafka_format = 'CSV', format_csv_delimiter = '|', kafka_skip_broken_messages = 10;

create table r_launch_step_ods
(
    Ktable             String,
    ProductID          UInt8,
    ChannelType        UInt32,
    Platform           UInt8,
    AppLanguage        UInt8,
    Country            String,
    AccType            UInt8,
    AppVersion         String,
    NowDate            Date,
    TimeStampValue     DateTime,
    PlayerId           UInt64,
    LaunchStepId       UInt32,
    LaunchStepDuration UInt64,
    LaunchFailDetail   String,
    DeviceCode         String,
    Expand             String
)
    engine = MergeTree PARTITION BY NowDate
        ORDER BY PlayerId
        SETTINGS index_granularity = 8192;

CREATE MATERIALIZED VIEW r_launch_step_view TO r_launch_step_ods AS
SELECT *
FROM r_launch_step_kafka;
