# server name
rpc_server_name: chaos

# log config
#log_level: trace
log_level: error
log_write: true
log_dir: ../../logs/chaos
log_json: false

# consul config
# 路由方式: poll=轮训, standby=分组主备, hash=哈希, lb=负载均衡, specify=指定[默认:poll].
rpc_router: poll
msg_router: poll

http_port: 13009

# 压测相关配置
st_gateway: http://192.168.1.54:21101
st_tcp_addr: 192.168.1.54:31201
# st_gateway: http://192.168.1.61:21101
# st_tcp_addr: 192.168.1.61:31201
# st_gateway: http://192.168.1.97:21101
# st_tcp_addr: 192.168.1.97:31201

# 压测机器人设备码起始位
st_uid_assign_begin: ************
st_player_num: 1

consul_addr: 192.168.1.58:8500

watch_log: false

# 游戏配置开关
switch_module:
  hall:
    play_basic: false # Basic
    play_mine: false # Mine
    play_charge: true # 充值
  game:
    play_game: false

# 1s 并发玩家
multi_player: 1000

reconnect: false