package main

import (
	"context"
	"runtime"
	"statssrv/config"
	"statssrv/internal/pubsub"
	"statssrv/internal/rpc"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type statsService struct {
	Name string
	Ctx  context.Context
}

func (s *statsService) Init() error {
	s.Ctx = context.Background()
	s.Name = viper.GetString(dict.ConfigRpcServerName)
	logrus.Infoln(s.Name + "服务Init")

	// 初始化配置
	err := config.InitConfig()
	if err != nil {
		logrus.Errorf("初始化配置失败:%+v", err)
		return err
	}

	// 实现各模块初始化操作
	pubsub.InitSubscribe()
	rpc.InitStatsRpcService()

	return nil
}

func (s *statsService) Start() error {
	// 这里实现启动逻辑
	logrus.Infoln(s.Name + "服务启动成功")
	return nil
}

func (s *statsService) Stop() error {
	// 这里实现服务正常关闭逻辑
	logrus.Infoln(s.Name + "服务关闭中...")
	return nil
}

func (s *statsService) ForceStop() error {
	// 这里实现强制关闭逻辑
	logrus.Infoln(s.Name + " ForceStop ...")
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource() // 初始化随机数
	driver.Run(&statsService{})
}
