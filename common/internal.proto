// 服务器内部proto 客户端不需要解析
syntax = "proto3";

package common;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common;commonPB";

import "enum.proto";

// 事件通用信息
message EventCommon {
    uint64 player_id = 1;              // 玩家id
    int32 product_id = 2;              // 产品id
    int32 channel_id = 3;              // 渠道id
    EVENT_TYPE event_type = 4;         // 事件类型
    map<int32, int64> int_data  = 5;   // 整形数据
    map<int32, string> str_data = 6;   // 字符串数据
}

// 实名认证
message PlayerRealNameAuth {
   int32  product_id = 1;  // 产品id
   uint64 player_id  = 2;  // 玩家id
   string pi         = 3;  // 实名认证返回pi
   string real_name  = 4;  // 真实姓名
   string id_card    = 5;  // 身份证号
   int32  year       = 6;  // 年份
   int32  month      = 7;  // 月份
   int32  day        = 8;  // 日
}
