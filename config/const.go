package config

import (
	"fmt"
	"time"
)

const (
	RedisKeyLockActivity         = "activity:lock:%d"
	RedisKeyLockActivityProgress = "activity:progress:lock:%d"
)

const (
	// activity:cache:$playerId:$activityType
	RedisKeyCacheActivity = "activity:cache:%d:%d"

	// activity:progress:$playerId:$activityType
	RedisKeyCacheActivityProgress = "activity:progress:%d:%d"
)

const (
	ActivityCacheExpire = 3 * 86400 * time.Second
)

// 活动修改分布式锁
func DLMActivityLockKey(playerId uint64) string {
	return fmt.Sprintf(RedisKeyLockActivity, playerId)
}

// 活动进度分布式锁
func DLMActivityProgressLockKey(playerId uint64) string {
	return fmt.Sprintf(RedisKeyLockActivityProgress, playerId)
}
