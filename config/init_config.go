package config

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/config"
)

// InitConfig 初始化配置信息
func InitConfig() error {
	serviceConfig := config.NewServiceConfig()

	serviceConfig.Register("InitActivityCfg", cmodel.InitActivityCfg)                 // 初始化活动配置
	serviceConfig.Register("InitActivityTypeCfg", cmodel.InitActivityTypeCfg)         // 初始化活动类型配置
	serviceConfig.Register("InitActivityRewardCfg", cmodel.InitActivityRewardCfg)     // 初始化活动奖励配置
	serviceConfig.Register("InitActivityCondCfg", cmodel.InitActivityCondCfg)         // 初始化活动条件配置
	serviceConfig.Register("InitActivityProgressCfg", cmodel.InitActivityProgressCfg) // 初始化活动进度配置

	return serviceConfig.ExecuteAll()
}
