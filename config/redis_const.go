package config

import (
	"fmt"
	"time"
)

const (
	// stats:cache:[productId]:[playerId] 玩家统计数据
	RedisKeyCacheStats = "stats:c:%d:%d"
	// stats_rule:[playerId] 玩家统计规则
	RedisKeyStatsRule = "stats_rule:%d"
)

const (
	RedisKeyCacheStatsExpire     = 86400 * 3 * time.Second
	RedisKeyCacheStatsRuleExpire = 30 * 60 * time.Second // 半个小时过期
)

func KeyCacheStats(productId int32, playerId uint64) string {
	return fmt.Sprintf(RedisKeyCacheStats, productId, playerId)
}

func KeyCacheStatsRule(playerId uint64) string {
	return fmt.Sprintf(RedisKeyStatsRule, playerId)
}
