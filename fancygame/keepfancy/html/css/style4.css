.ch-item {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	position: relative;
	box-shadow: 0 1px 2px rgba(0,0,0,0.1);
	cursor: default;
}
.ch-info-wrap{
	position: absolute;
	width: 180px;
	height: 180px;
	border-radius: 50%;
	-webkit-perspective: 800px;
	-moz-perspective: 800px;
	-o-perspective: 800px;
	-ms-perspective: 800px;
	perspective: 800px;

	-webkit-transition: all 0.4s ease-in-out;
	-moz-transition: all 0.4s ease-in-out;
	-o-transition: all 0.4s ease-in-out;
	-ms-transition: all 0.4s ease-in-out;
	transition: all 0.4s ease-in-out;

	top: 20px;
	left: 20px;
	background: #f9f9f9 url(../images/bg.jpg);
	box-shadow: 
		0 0 0 20px rgba(255,255,255,0.2), 
		inset 0 0 3px rgba(115,114, 23, 0.8);
}
.ch-info{
	position: absolute;
	width: 180px;
	height: 180px;
	border-radius: 50%;
	-webkit-transition: all 0.4s ease-in-out;
	-moz-transition: all 0.4s ease-in-out;
	-o-transition: all 0.4s ease-in-out;
	-ms-transition: all 0.4s ease-in-out;
	transition: all 0.4s ease-in-out;	
	-webkit-transform-style: preserve-3d;
	-moz-transform-style: preserve-3d;
	-o-transform-style: preserve-3d;
	-ms-transform-style: preserve-3d;
	transform-style: preserve-3d;
}
.ch-info > div {
	display: block;
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background-position: center center;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	-o-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	backface-visibility: hidden;
}
.ch-info .ch-info-back {
	-webkit-transform: rotate3d(0,1,0,180deg);
	-moz-transform: rotate3d(0,1,0,180deg);
	-o-transform: rotate3d(0,1,0,180deg);
	-ms-transform: rotate3d(0,1,0,180deg);
	transform: rotate3d(0,1,0,180deg);
	background:#0dc5dd;
}

.ch-img-1 { 
	background-image: url(../images/team-1.jpg);
}

.ch-img-2 { 
	background-image: url(../images/team-3.jpg);
}

.ch-img-3 { 
	background-image: url(../images/team-2.jpg);
}

.ch-info h3 {
	color: #fff;
	text-transform: uppercase;
	letter-spacing: 2px;
	font-size: 14px;
	margin: 0 15px;
	padding: 40px 0 0 0;
	height: 90px;
	font-family: 'Open Sans', Arial, sans-serif;
	text-shadow: 
		0 0 1px #fff, 
		0 1px 2px rgba(0,0,0,0.3);
}

.ch-info p {
	color: #fff;
	padding: 10px 5px;
	font-style: italic;
	margin: 0 30px;
	font-size: 12px;
	border-top: 1px solid rgba(255,255,255,0.5);
}

.ch-info p a {
	display: block;
	color: #fff;
	color: rgba(255,255,255,0.7);
	font-style: normal;
	font-weight: 700;
	text-transform: uppercase;
	font-size: 9px;
	letter-spacing: 1px;
	padding-top: 4px;
	font-family: 'Open Sans', Arial, sans-serif;
}

.ch-info p a:hover {
	color: #fff222;
	color: rgba(255,242,34, 0.8);
}

.ch-item:hover .ch-info-wrap {
	box-shadow: 
		0 0 0 0 rgba(255,255,255,0.8), 
		inset 0 0 3px rgba(115,114, 23, 0.8);
}

.ch-item:hover .ch-info {
	-webkit-transform: rotate3d(0,1,0,-180deg);
	-moz-transform: rotate3d(0,1,0,-180deg);
	-o-transform: rotate3d(0,1,0,-180deg);
	-ms-transform: rotate3d(0,1,0,-180deg);
	transform: rotate3d(0,1,0,-180deg);
}