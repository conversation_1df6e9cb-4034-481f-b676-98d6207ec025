{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T10:05:17.61342+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-06T10:05:17.613658+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T10:05:17.613674+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T10:05:17.613721+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-06T10:05:17.613732+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-06T10:05:17.613744+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-06T10:05:17.613755+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-06T10:05:18.011365+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-06T10:05:18.286922+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-06T10:05:18.541434+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-06T10:05:18.541624+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-06T10:05:18.541702+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-06T10:05:18.541829+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-06T10:05:18.541916+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-06T10:05:18.541932+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T10:05:18.54222+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T10:05:18.580511+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000a0a580}","time":"2025-05-06T10:05:18.580545+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(gate)注册完成","time":"2025-05-06T10:05:18.580575+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T10:06:17.00274+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T10:07:17.011168+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T10:17:51.766332+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T10:20:18.006118+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-06T10:22:23.18915+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-06T10:22:23.199514+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_UPDATE_ITEM_NTF, msgLen: 97, playerIDs: [23], addr:************:11201]","time":"2025-05-06T10:22:23.203937+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T10:23:06.457331+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T10:54:55.841061+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-06T10:54:55.841408+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T10:54:55.841429+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T10:54:55.84144+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-06T10:54:55.841451+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-06T10:54:55.841462+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-06T10:54:55.841476+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-06T10:54:56.1594+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-06T10:54:56.466948+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-06T10:54:56.793387+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-06T10:54:56.793726+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-06T10:54:56.793802+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-06T10:54:56.793922+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-06T10:54:56.793984+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-06T10:54:56.794006+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T10:54:56.7943+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T10:54:56.813617+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400099e000}","time":"2025-05-06T10:54:56.813646+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(gate)注册完成","time":"2025-05-06T10:54:56.813672+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T10:55:55.001918+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T10:56:55.00299+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T10:57:55.002498+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T10:58:55.002366+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T10:59:55.001922+08:00"}
