{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:00:55.002029+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:01:55.002974+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:02:55.00252+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:03:55.002548+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:215","func":"1","level":"info","msg":"Detect config context change: WRITE         \"config.yml\" ","time":"2025-05-06T11:04:44.541326+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:215","func":"1","level":"info","msg":"Detect config context change: WRITE         \"config.yml\" ","time":"2025-05-06T11:04:44.544066+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:239","func":"func2","level":"info","msg":"配置变更后Tags：debug","time":"2025-05-06T11:04:44.544127+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T11:04:44.544437+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T11:04:44.564771+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:04:55.002945+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:215","func":"1","level":"info","msg":"Detect config context change: WRITE         \"config.yml\" ","time":"2025-05-06T11:05:08.471647+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:215","func":"1","level":"info","msg":"Detect config context change: WRITE         \"config.yml\" ","time":"2025-05-06T11:05:08.475144+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:239","func":"func2","level":"info","msg":"配置变更后Tags：normal","time":"2025-05-06T11:05:08.475142+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T11:05:08.475365+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T11:05:08.503595+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:215","func":"1","level":"info","msg":"Detect config context change: WRITE         \"config.yml\" ","time":"2025-05-06T11:05:16.655564+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:215","func":"1","level":"info","msg":"Detect config context change: WRITE         \"config.yml\" ","time":"2025-05-06T11:05:16.656163+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:239","func":"func2","level":"info","msg":"配置变更后Tags：debug","time":"2025-05-06T11:05:16.656173+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T11:05:16.65643+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T11:05:16.67449+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:05:55.002984+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:06:55.003042+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:07:55.00305+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:08:55.002067+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:09:55.00212+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:48","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-06T11:10:55.002978+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T11:13:54.148158+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-06T11:13:54.14854+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T11:13:54.14856+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T11:13:54.148571+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-06T11:13:54.148582+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-06T11:13:54.148593+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-06T11:13:54.148606+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-06T11:13:54.480466+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-06T11:13:54.77821+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-06T11:13:55.081253+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-06T11:13:55.08144+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-06T11:13:55.081516+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-06T11:13:55.081661+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-06T11:13:55.081741+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-06T11:13:55.081729+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T11:13:55.082129+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T11:13:55.095642+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000992420}","time":"2025-05-06T11:13:55.095672+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(gate)注册完成","time":"2025-05-06T11:13:55.095699+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T11:18:06.787933+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-06T11:18:06.788137+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T11:18:06.788154+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T11:18:06.788168+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-06T11:18:06.788181+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-06T11:18:06.788194+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-06T11:18:06.788215+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-06T11:18:07.117007+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-06T11:18:07.44897+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-06T11:18:07.7234+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-06T11:18:07.723639+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-06T11:18:07.929429+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-06T11:18:07.929922+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-06T11:18:07.9301+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-06T11:18:07.930129+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T11:18:07.930494+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T11:18:07.945775+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400022c9a0}","time":"2025-05-06T11:18:07.945809+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(gate)注册完成","time":"2025-05-06T11:18:07.945846+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T11:28:12.942382+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-06T11:28:12.942909+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T11:28:12.942927+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T11:28:12.942938+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-06T11:28:12.94295+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-06T11:28:12.94296+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-06T11:28:12.942972+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-06T11:28:13.274065+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-06T11:28:13.607676+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-06T11:28:13.915713+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-06T11:28:13.915952+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-06T11:28:13.91604+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-06T11:28:13.91624+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-06T11:28:13.916289+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-06T11:28:13.916408+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T11:28:13.916834+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T11:28:13.931002+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400059c2c0}","time":"2025-05-06T11:28:13.931031+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(gate)注册完成","time":"2025-05-06T11:28:13.931063+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-06T11:29:39.132653+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-06T11:29:39.148079+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_UPDATE_ITEM_NTF, msgLen: 333, playerIDs: [47], addr:************:11201]","time":"2025-05-06T11:29:39.153567+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_UPDATE_ITEM_NTF, msgLen: 67, playerIDs: [47], addr:************:11201]","time":"2025-05-06T11:29:39.250404+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T11:35:06.020166+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-06T11:35:06.020656+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T11:35:06.02067+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T11:35:06.02068+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-06T11:35:06.020689+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-06T11:35:06.020699+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-06T11:35:06.020711+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-06T11:35:06.374586+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-06T11:35:06.696897+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-06T11:35:06.989894+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-06T11:35:06.990099+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-06T11:35:06.990177+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-06T11:35:06.990337+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-06T11:35:06.990403+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-06T11:35:06.990469+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T11:35:06.9908+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T11:35:07.006434+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140005b26e0}","time":"2025-05-06T11:35:07.006484+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(gate)注册完成","time":"2025-05-06T11:35:07.006521+08:00"}
