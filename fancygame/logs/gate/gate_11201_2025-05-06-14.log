{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T14:42:40.382808+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-06T14:42:40.383402+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T14:42:40.383428+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T14:42:40.383443+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-06T14:42:40.383458+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-06T14:42:40.383471+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-06T14:42:40.383485+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"error","msg":"consul config err:get list fail: Unexpected response code: 502 () path:1/1001/gate_info","time":"2025-05-06T14:47:40.411711+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-06T14:47:40.415139+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"error","msg":"consul config err:get list fail: Unexpected response code: 502 () path:1/1001/gate_info","time":"2025-05-06T14:52:40.450744+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/gate_info_cfg.go:69","func":"GetGateInfo","level":"error","msg":"fail json.Unmarshal is empty, err:get list fail: Unexpected response code: 502 ()","time":"2025-05-06T14:52:40.455107+08:00"}
