{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T16:46:19.024782+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-06T16:46:19.02522+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T16:46:19.02525+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T16:46:19.025267+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-06T16:46:19.02528+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-06T16:46:19.025291+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-06T16:46:19.025306+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-06T16:46:19.355434+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-06T16:46:19.644781+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-06T16:46:19.917217+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-06T16:46:19.917527+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-06T16:46:20.209294+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-06T16:46:20.209488+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-06T16:46:20.209571+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-06T16:46:20.209578+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T16:46:20.210115+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T16:46:20.238048+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000a02420}","time":"2025-05-06T16:46:20.238131+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(gate)注册完成","time":"2025-05-06T16:46:20.238176+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/wsserver.go:136","func":"ServeHTTP","level":"info","msg":"SubProtocol:  network","time":"2025-05-06T16:47:31.640305+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/handle.go:23","func":"OnClientConnect","level":"info","msg":"client connected: client_id=1,ip=************","time":"2025-05-06T16:47:31.640649+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/netservicesimpl.go:95","func":"workOnExchanger","level":"info","msg":"[客户端 1 开启连接]","time":"2025-05-06T16:47:31.640818+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:119","func":"OnRecv","header":"msg_id:1101 body_length:223","level":"info","msg":"收到处理登录消息","player_id":0,"time":"2025-05-06T16:47:31.800463+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/login 成功","time":"2025-05-06T16:47:31.818528+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/services/login.go:56","func":"HandleLoginSession","level":"error","msg":"1 login_fail, rsp=ret:{code:ERR_LOGIN_ACCOUNT_PASSWORD_ERROR desc:\"ERR_LOGIN_ACCOUNT_PASSWORD_ERROR\"}","time":"2025-05-06T16:47:31.858135+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_LOGIN_RSP,rsp_len=38,req_msg=CMD_LOGIN_REQ,req_seq=0,client_id=1,player_id=0","time":"2025-05-06T16:47:31.858435+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/wsserver.go:25","func":"Recv","level":"info","msg":"error: websocket: close 1000 (normal): Bye!","time":"2025-05-06T16:47:31.882258+08:00"}
{"error":"websocket: close 1000 (normal): Bye!","file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:292","func":"recv","level":"warning","msg":"接受数据失败，clientID: 1","time":"2025-05-06T16:47:31.882321+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:61","func":"OnRecv","header":"\u003cnil\u003e","level":"info","msg":"player:0 clientID:1 disconnect","time":"2025-05-06T16:47:31.882358+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:227","func":"1","level":"debug","msg":"send loop routine end","time":"2025-05-06T16:47:31.882426+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:190","func":"run","level":"debug","msg":"run loop routine end","time":"2025-05-06T16:47:31.882515+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/netservicesimpl.go:100","func":"workOnExchanger","level":"info","msg":"客户端 1 关闭连接","time":"2025-05-06T16:47:31.882593+08:00"}
{"client_id":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/handle.go:52","func":"OnClientDisconnect","func_name":"ConnectionMgr.OnClientDisconnect","level":"info","msg":"client disconnected","time":"2025-05-06T16:47:31.882622+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:138","func":"DetachPlayer","level":"debug","msg":"DetachPlayer 解除和 player 的绑定,connect:1 playerId:0","time":"2025-05-06T16:47:31.882678+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/wsserver.go:136","func":"ServeHTTP","level":"info","msg":"SubProtocol:  network","time":"2025-05-06T16:47:52.578354+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/handle.go:23","func":"OnClientConnect","level":"info","msg":"client connected: client_id=2,ip=************","time":"2025-05-06T16:47:52.578639+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/netservicesimpl.go:95","func":"workOnExchanger","level":"info","msg":"[客户端 2 开启连接]","time":"2025-05-06T16:47:52.57873+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:119","func":"OnRecv","header":"msg_id:1101 send_seq:1 body_length:224","level":"info","msg":"收到处理登录消息","player_id":0,"time":"2025-05-06T16:47:52.612581+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/services/login.go:71","func":"HandleLoginSession","header":"msg_id:1101 send_seq:1 body_length:224 client_ip:\"************\"","level":"info","msg":"rsp from loginSrv rpc : ret:{desc:\"ERR_SUCCESS\"} player_id:31 token:\"d1c71ee3b1807da2be7a0c11f82b322a\" rich_user_info:{brief_user_info:{player_id:31 avatar:4020000 frame:4010000} app_version:\"0.1.15b4\" acc_type:AT_PASSWORD register_time:1745045501 platform:PT_ANDROID app_language:LT_EN_US real_name_auth:true extend_user_info:{novice_guide:23}}","player_id":31,"time":"2025-05-06T16:47:52.683246+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-06T16:47:52.683372+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-06T16:47:52.696162+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/logic/another_check.go:27","func":"CheckAnother","gateAddr":"","level":"debug","msg":"检查是否顶号登录[31]，local : ************:11201","name":"checkAnother()","player_id":31,"time":"2025-05-06T16:47:52.699707+08:00"}
{"attach_player_id":31,"client_id":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:197","func":"AttachPlayer","ip":"************","level":"info","msg":"client[2] connect success player[1:31]","time":"2025-05-06T16:47:52.707898+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/services/login.go:100","func":"HandleLoginSession","header":"msg_id:1101 send_seq:1 body_length:224 client_ip:\"************\"","level":"info","msg":"登录成功, response=ret:{desc:\"ERR_SUCCESS\"} player_id:31 token:\"d1c71ee3b1807da2be7a0c11f82b322a\" rich_user_info:{brief_user_info:{player_id:31 avatar:4020000 frame:4010000} app_version:\"0.1.15b4\" acc_type:AT_PASSWORD register_time:1745045501 platform:PT_ANDROID app_language:LT_EN_US real_name_auth:true extend_user_info:{novice_guide:23}} version:0.1.14","player_id":31,"time":"2025-05-06T16:47:52.708068+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/pushlish.go:96","func":"PushEventLogin","level":"info","msg":"logout event push success:player_id:31 product_id:1 channel_id:1001 event_type:ET_LOGIN int_data:{key:1 value:1746521272} int_data:{key:4002 value:20250506} int_data:{key:4003 value:0}","time":"2025-05-06T16:47:52.709246+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_LOGIN_RSP,rsp_len=95,req_msg=CMD_LOGIN_REQ,req_seq=1,client_id=2,player_id=0","time":"2025-05-06T16:47:52.709354+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-06T16:47:52.805499+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/hall 成功","time":"2025-05-06T16:47:52.820027+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2001,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:52.851775+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2001 send_seq:2","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:52.851886+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2105,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:52.888598+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2105 send_seq:3","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:52.88871+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/msg 成功","time":"2025-05-06T16:47:52.904511+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_MSG_GET_MAIL_LIST_RSP,rsp_len=509,req_msg=CMD_MSG_GET_MAIL_LIST_REQ,req_seq=4,client_id=2,player_id=31","time":"2025-05-06T16:47:52.998896+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:3601 send_seq:4 body_length:4","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400056adc0)}","player_id":31,"time":"2025-05-06T16:47:52.998988+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_MSG_GET_MAIL_LIST_RSP,rsp_len=17,req_msg=CMD_MSG_GET_MAIL_LIST_REQ,req_seq=5,client_id=2,player_id=31","time":"2025-05-06T16:47:53.025882+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:3601 send_seq:5 body_length:4","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400056ba90)}","player_id":31,"time":"2025-05-06T16:47:53.025943+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/world 成功","time":"2025-05-06T16:47:53.042347+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WORLD_TIME_RSP,rsp_len=27,req_msg=CMD_WORLD_GET_WORLD_TIME_REQ,req_seq=6,client_id=2,player_id=31","time":"2025-05-06T16:47:53.063764+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:3205 send_seq:6 body_length:6","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000112870)}","player_id":31,"time":"2025-05-06T16:47:53.063836+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2017,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:53.094444+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2017 send_seq:7 body_length:2","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:53.094497+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2023,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:53.126305+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2023 send_seq:8 body_length:2","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:53.126436+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2015,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:53.158004+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2015 send_seq:9","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:53.1581+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2001,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:53.190106+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2001 send_seq:10","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:53.190171+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2007,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:53.220071+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2007 send_seq:11 body_length:11","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:53.220232+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2045,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:53.254153+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2045 send_seq:12","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:53.254214+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2053,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:53.286576+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2053 send_seq:13 body_length:2","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:53.286696+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2053,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:53.321658+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2053 send_seq:14 body_length:2","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:53.321927+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WORLD_TIME_RSP,rsp_len=27,req_msg=CMD_WORLD_GET_WORLD_TIME_REQ,req_seq=15,client_id=2,player_id=31","time":"2025-05-06T16:47:53.350804+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:3205 send_seq:15 body_length:6","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000113e00)}","player_id":31,"time":"2025-05-06T16:47:53.35094+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/task 成功","time":"2025-05-06T16:47:53.369315+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_GET_LIST_RSP,rsp_len=197,req_msg=CMD_TASK_GET_LIST_REQ,req_seq=16,client_id=2,player_id=31","time":"2025-05-06T16:47:53.393534+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:3501 send_seq:16 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001266370)}","player_id":31,"time":"2025-05-06T16:47:53.393623+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_PROGRESS_RSP,rsp_len=17,req_msg=CMD_TASK_PROGRESS_REQ,req_seq=17,client_id=2,player_id=31","time":"2025-05-06T16:47:53.420151+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:3513 send_seq:17 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001266910)}","player_id":31,"time":"2025-05-06T16:47:53.420262+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_GET_LIST_RSP,rsp_len=125,req_msg=CMD_TASK_GET_LIST_REQ,req_seq=18,client_id=2,player_id=31","time":"2025-05-06T16:47:53.442828+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:3501 send_seq:18 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001266b40)}","player_id":31,"time":"2025-05-06T16:47:53.442922+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_PROGRESS_RSP,rsp_len=17,req_msg=CMD_TASK_PROGRESS_REQ,req_seq=19,client_id=2,player_id=31","time":"2025-05-06T16:47:53.467996+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:3513 send_seq:19 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001266d70)}","player_id":31,"time":"2025-05-06T16:47:53.468116+08:00"}
{"clientID":2,"error":"rpc error: code = Unavailable desc = connection error: desc = \"error reading server preface: read tcp 127.0.0.1:50416-\u003e127.0.0.1:7890: read: connection reset by peer\"","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/handler.go:94","func":"handleMsg","level":"error","msg":"调用服务失败","msgID":2097,"name":"receiver.handleMsg","player_id":31,"time":"2025-05-06T16:47:53.498047+08:00"}
{"clientID":2,"error":"调用服务失败","file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:140","func":"OnRecv","header":"msg_id:2097 send_seq:20","level":"error","msg":"调用远程处理器失败","player_id":31,"time":"2025-05-06T16:47:53.498119+08:00"}
