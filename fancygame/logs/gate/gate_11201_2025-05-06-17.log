{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:61","func":"GetConnection","level":"debug","msg":"GetConnection Debug hall By Tag yes","time":"2025-05-06T17:02:07.817017+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:100","func":"GetConnectionWithDyeTag","level":"info","msg":"GRPC Connect With Tag : consul://192.168.1.58:8500/hall?tag=debug","time":"2025-05-06T17:02:07.824571+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_STAT_LIST_RSP,rsp_len=26,req_msg=CMD_GET_STAT_LIST_REQ,req_seq=319,client_id=2,player_id=31","time":"2025-05-06T17:02:07.931788+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:2041 send_seq:319","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001302690)}","player_id":31,"time":"2025-05-06T17:02:07.932298+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_GET_LIST_RSP,rsp_len=197,req_msg=CMD_TASK_GET_LIST_REQ,req_seq=321,client_id=2,player_id=31","time":"2025-05-06T17:02:10.195318+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:3501 send_seq:321 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000154000)}","player_id":31,"time":"2025-05-06T17:02:10.195489+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_PROGRESS_RSP,rsp_len=17,req_msg=CMD_TASK_PROGRESS_REQ,req_seq=322,client_id=2,player_id=31","time":"2025-05-06T17:02:10.343655+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:145","func":"OnRecv","header":"msg_id:3513 send_seq:322 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001302af0)}","player_id":31,"time":"2025-05-06T17:02:10.343865+08:00"}
{"error":"websocket: close 1006 (abnormal closure): unexpected EOF","file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:292","func":"recv","level":"warning","msg":"接受数据失败，clientID: 2","time":"2025-05-06T17:02:26.592665+08:00"}
{"clientID":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:61","func":"OnRecv","header":"\u003cnil\u003e","level":"info","msg":"player:31 clientID:2 disconnect","time":"2025-05-06T17:02:26.593321+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:227","func":"1","level":"debug","msg":"send loop routine end","time":"2025-05-06T17:02:26.593148+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:190","func":"run","level":"debug","msg":"run loop routine end","time":"2025-05-06T17:02:26.593437+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/netservicesimpl.go:100","func":"workOnExchanger","level":"info","msg":"客户端 2 关闭连接","time":"2025-05-06T17:02:26.593475+08:00"}
{"client_id":2,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/handle.go:52","func":"OnClientDisconnect","func_name":"ConnectionMgr.OnClientDisconnect","level":"info","msg":"client disconnected","time":"2025-05-06T17:02:26.593505+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/pushlish.go:51","func":"PushEventLogout","level":"info","msg":"logout event push success:player_id:31 product_id:1 channel_id:1001 event_type:ET_LOGOUT int_data:{key:4101 value:874}","time":"2025-05-06T17:02:26.595875+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:138","func":"DetachPlayer","level":"debug","msg":"DetachPlayer 解除和 player 的绑定,connect:2 playerId:31","time":"2025-05-06T17:02:26.596111+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-06T17:17:52.685855+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-06T17:17:53.027082+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-06T17:17:53.352113+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-06T17:32:07.932865+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-06T17:32:10.350501+08:00"}
