{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-07T11:05:47.109592+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-07T11:05:47.11007+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-07T11:05:47.110099+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-07T11:05:47.110111+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-07T11:05:47.110122+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-07T11:05:47.110134+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-07T11:05:47.110175+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-07T11:05:47.453813+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-07T11:05:47.847882+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-07T11:05:48.203291+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-07T11:05:48.203664+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-07T11:05:48.520538+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-07T11:05:48.5207+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-07T11:05:48.520758+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-07T11:05:48.520766+08:00"}
{"error":"listen tcp :11201: bind: address already in use","file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:167","func":"StartServer","level":"error","msg":"监听地址 :11201 失败","time":"2025-05-07T11:05:48.521021+08:00"}
