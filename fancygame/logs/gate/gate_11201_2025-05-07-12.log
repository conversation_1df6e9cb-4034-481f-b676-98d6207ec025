{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-07T12:17:48.854213+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-07T12:17:48.855085+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-07T12:17:48.855134+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-07T12:17:48.855147+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-07T12:17:48.85516+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-07T12:17:48.855175+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-07T12:17:48.855202+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-07T12:17:49.212175+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-07T12:17:49.57472+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-07T12:17:49.916922+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-07T12:17:49.917086+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-07T12:17:49.917165+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-07T12:17:49.917447+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-07T12:17:49.917515+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-07T12:17:49.917539+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-07T12:17:49.917897+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-07T12:17:49.932391+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000a82000}","time":"2025-05-07T12:17:49.932477+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(gate)注册完成","time":"2025-05-07T12:17:49.93251+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/wsserver.go:136","func":"ServeHTTP","level":"info","msg":"SubProtocol:  network","time":"2025-05-07T12:17:59.940018+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/handle.go:23","func":"OnClientConnect","level":"info","msg":"client connected: client_id=1,ip=************","time":"2025-05-07T12:17:59.940819+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/netservicesimpl.go:95","func":"workOnExchanger","level":"info","msg":"[客户端 1 开启连接]","time":"2025-05-07T12:17:59.940972+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:122","func":"OnRecv","header":"msg_id:1101 send_seq:2559 body_length:224","level":"info","msg":"收到处理登录消息","player_id":0,"time":"2025-05-07T12:17:59.975492+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/login 成功","time":"2025-05-07T12:17:59.991516+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/services/login.go:71","func":"HandleLoginSession","header":"msg_id:1101 send_seq:2559 body_length:224 client_ip:\"************\"","level":"info","msg":"rsp from loginSrv rpc : ret:{desc:\"ERR_SUCCESS\"} player_id:31 token:\"065742c64e3563202b3e7b750383faf4\" rich_user_info:{brief_user_info:{player_id:31 avatar:4020000 frame:4010000 lev:25} app_version:\"0.1.15b4\" acc_type:AT_PASSWORD register_time:1745045501 platform:PT_ANDROID app_language:LT_EN_US real_name_auth:true extend_user_info:{novice_guide:23}}","player_id":31,"time":"2025-05-07T12:18:00.069173+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-07T12:18:00.069316+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-07T12:18:00.082373+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/logic/another_check.go:27","func":"CheckAnother","gateAddr":"************:11201","level":"debug","msg":"检查是否顶号登录[31]，local : ************:11201","name":"checkAnother()","player_id":31,"time":"2025-05-07T12:18:00.086018+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/logic/another_login.go:24","func":"AnotherLogin","level":"debug","msg":"request=player_id:31 device:\"SM-S7310\" imei:\"997193f21d88cb609f81f27571162cdc\"","name":"AnotherLogin","player_id":31,"time":"2025-05-07T12:18:00.086132+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/logic/another_login.go:28","func":"AnotherLogin","level":"info","msg":"The player[31] is not at this gateway","name":"AnotherLogin","player_id":31,"time":"2025-05-07T12:18:00.086151+08:00"}
{"attach_player_id":31,"client_id":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:201","func":"AttachPlayer","ip":"************","level":"info","msg":"client[1] connect success player[1:31]","time":"2025-05-07T12:18:00.094172+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/services/login.go:100","func":"HandleLoginSession","header":"msg_id:1101 send_seq:2559 body_length:224 client_ip:\"************\"","level":"info","msg":"登录成功, response=ret:{desc:\"ERR_SUCCESS\"} player_id:31 token:\"065742c64e3563202b3e7b750383faf4\" rich_user_info:{brief_user_info:{player_id:31 avatar:4020000 frame:4010000 lev:25} app_version:\"0.1.15b4\" acc_type:AT_PASSWORD register_time:1745045501 platform:PT_ANDROID app_language:LT_EN_US real_name_auth:true extend_user_info:{novice_guide:23}} version:0.1.14","player_id":31,"time":"2025-05-07T12:18:00.094377+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/pushlish.go:103","func":"PushEventLogin","level":"info","msg":"logout event push success:player_id:31 product_id:1 channel_id:1001 event_type:ET_LOGIN int_data:{key:1 value:1746591480} int_data:{key:4002 value:20250507} int_data:{key:4003 value:0}","time":"2025-05-07T12:18:00.094728+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_LOGIN_RSP,rsp_len=97,req_msg=CMD_LOGIN_REQ,req_seq=2559,client_id=1,player_id=0","time":"2025-05-07T12:18:00.094904+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-07T12:18:00.145662+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/hall 成功","time":"2025-05-07T12:18:00.159486+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_ROOM_INFO_RSP,rsp_len=28,req_msg=CMD_GET_ROOM_INFO_REQ,req_seq=2560,client_id=1,player_id=31","time":"2025-05-07T12:18:00.190676+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2001 send_seq:2560","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140002e9310)}","player_id":31,"time":"2025-05-07T12:18:00.19076+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_PLAYER_ALL_RED_DOT_RSP,rsp_len=15,req_msg=CMD_GET_PLAYER_ALL_RED_DOT_REQ,req_seq=2561,client_id=1,player_id=31","time":"2025-05-07T12:18:00.221224+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2105 send_seq:2561","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400005fcc0)}","player_id":31,"time":"2025-05-07T12:18:00.221288+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/msg 成功","time":"2025-05-07T12:18:00.237729+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_MSG_GET_MAIL_LIST_RSP,rsp_len=509,req_msg=CMD_MSG_GET_MAIL_LIST_REQ,req_seq=2562,client_id=1,player_id=31","time":"2025-05-07T12:18:00.324856+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3601 send_seq:2562 body_length:4","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400069b400)}","player_id":31,"time":"2025-05-07T12:18:00.32495+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_MSG_GET_MAIL_LIST_RSP,rsp_len=17,req_msg=CMD_MSG_GET_MAIL_LIST_REQ,req_seq=2563,client_id=1,player_id=31","time":"2025-05-07T12:18:00.355819+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3601 send_seq:2563 body_length:4","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400069bc70)}","player_id":31,"time":"2025-05-07T12:18:00.355885+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/world 成功","time":"2025-05-07T12:18:00.373027+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WORLD_TIME_RSP,rsp_len=27,req_msg=CMD_WORLD_GET_WORLD_TIME_REQ,req_seq=2564,client_id=1,player_id=31","time":"2025-05-07T12:18:00.397539+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3205 send_seq:2564 body_length:6","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400031adc0)}","player_id":31,"time":"2025-05-07T12:18:00.397596+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_PLAYER_INFO_RSP,rsp_len=17,req_msg=CMD_GET_PLAYER_INFO_REQ,req_seq=2565,client_id=1,player_id=31","time":"2025-05-07T12:18:00.425256+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2017 send_seq:2565 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000cd6820)}","player_id":31,"time":"2025-05-07T12:18:00.425444+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_FIRST_ENTER_HALL_RSP,rsp_len=17,req_msg=CMD_FIRST_ENTER_HALL_REQ,req_seq=2566,client_id=1,player_id=31","time":"2025-05-07T12:18:00.459828+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2023 send_seq:2566 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000c70500)}","player_id":31,"time":"2025-05-07T12:18:00.459891+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_LAST_GAME_INFO_RSP,rsp_len=17,req_msg=CMD_GET_LAST_GAME_INFO_REQ,req_seq=2567,client_id=1,player_id=31","time":"2025-05-07T12:18:00.48422+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2015 send_seq:2567","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000c707d0)}","player_id":31,"time":"2025-05-07T12:18:00.484295+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_ROOM_INFO_RSP,rsp_len=28,req_msg=CMD_GET_ROOM_INFO_REQ,req_seq=2568,client_id=1,player_id=31","time":"2025-05-07T12:18:00.512694+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2001 send_seq:2568","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000d4d090)}","player_id":31,"time":"2025-05-07T12:18:00.512938+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_ITEM_INFO_BY_TYPE_RSP,rsp_len=496,req_msg=CMD_GET_ITEM_INFO_BY_TYPE_REQ,req_seq=2569,client_id=1,player_id=31","time":"2025-05-07T12:18:00.585327+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2007 send_seq:2569 body_length:11","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000c70dc0)}","player_id":31,"time":"2025-05-07T12:18:00.585436+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_TRIP_ROD_RSP,rsp_len=54,req_msg=CMD_GET_TRIP_ROD_REQ,req_seq=2570,client_id=1,player_id=31","time":"2025-05-07T12:18:00.620938+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2045 send_seq:2570","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000d4d2c0)}","player_id":31,"time":"2025-05-07T12:18:00.621027+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_TRIP_BAG_RSP,rsp_len=17,req_msg=CMD_GET_TRIP_BAG_REQ,req_seq=2571,client_id=1,player_id=31","time":"2025-05-07T12:18:00.652111+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2053 send_seq:2571 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000d4d590)}","player_id":31,"time":"2025-05-07T12:18:00.652173+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_TRIP_BAG_RSP,rsp_len=78,req_msg=CMD_GET_TRIP_BAG_REQ,req_seq=2572,client_id=1,player_id=31","time":"2025-05-07T12:18:00.678954+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2053 send_seq:2572 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000c71630)}","player_id":31,"time":"2025-05-07T12:18:00.679037+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WORLD_TIME_RSP,rsp_len=27,req_msg=CMD_WORLD_GET_WORLD_TIME_REQ,req_seq=2573,client_id=1,player_id=31","time":"2025-05-07T12:18:00.699526+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3205 send_seq:2573 body_length:6","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000c71900)}","player_id":31,"time":"2025-05-07T12:18:00.699617+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/task 成功","time":"2025-05-07T12:18:00.717927+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_GET_LIST_RSP,rsp_len=197,req_msg=CMD_TASK_GET_LIST_REQ,req_seq=2574,client_id=1,player_id=31","time":"2025-05-07T12:18:00.740386+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3501 send_seq:2574 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000e4c190)}","player_id":31,"time":"2025-05-07T12:18:00.740449+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_PROGRESS_RSP,rsp_len=17,req_msg=CMD_TASK_PROGRESS_REQ,req_seq=2575,client_id=1,player_id=31","time":"2025-05-07T12:18:00.772214+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3513 send_seq:2575 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000e4c3c0)}","player_id":31,"time":"2025-05-07T12:18:00.772303+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_GET_LIST_RSP,rsp_len=125,req_msg=CMD_TASK_GET_LIST_REQ,req_seq=2576,client_id=1,player_id=31","time":"2025-05-07T12:18:00.797881+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3501 send_seq:2576 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000cd75e0)}","player_id":31,"time":"2025-05-07T12:18:00.797942+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_PROGRESS_RSP,rsp_len=17,req_msg=CMD_TASK_PROGRESS_REQ,req_seq=2577,client_id=1,player_id=31","time":"2025-05-07T12:18:00.82762+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3513 send_seq:2577 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000cd7810)}","player_id":31,"time":"2025-05-07T12:18:00.827853+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_HALL_CONTINUOUS_LOGIN_RSP,rsp_len=15,req_msg=CMD_HALL_CONTINUOUS_LOGIN_REQ,req_seq=2578,client_id=1,player_id=31","time":"2025-05-07T12:18:00.853215+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2097 send_seq:2578","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000cd7ae0)}","player_id":31,"time":"2025-05-07T12:18:00.853269+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:231","func":"IncMsg","level":"error","msg":"1秒消息数超20告警: count=20,playerID=31,clientID=1","time":"2025-05-07T12:18:00.853316+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_PLAYER_INFO_RSP,rsp_len=17,req_msg=CMD_GET_PLAYER_INFO_REQ,req_seq=2579,client_id=1,player_id=31","time":"2025-05-07T12:18:00.888653+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2017 send_seq:2579 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001c941e0)}","player_id":31,"time":"2025-05-07T12:18:00.888733+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:18:48.001225+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:18:48.002104+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:19:48.000956+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:19:48.001813+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:20:48.001234+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:20:48.002294+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:21:48.001174+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:21:48.002024+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:22:48.0013+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:22:48.002735+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:23:48.001219+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:23:48.002269+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:24:48.000625+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:24:48.001607+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:25:48.001247+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:25:48.002252+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:26:48.000873+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:26:48.003123+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:27:48.001174+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:27:48.002831+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:28:48.001144+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:28:48.001729+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:29:48.001243+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:29:48.00272+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:30:48.025126+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:30:48.026516+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:31:48.013334+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:31:48.014668+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:32:48.002627+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:32:48.003618+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:33:48.002291+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:33:48.003353+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:34:48.002294+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:34:48.004245+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:35:48.002316+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:35:48.002612+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:36:48.002501+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T12:36:48.003807+08:00"}
{"error":"read tcp ************:21201-\u003e************:62032: read: connection reset by peer","file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:292","func":"recv","level":"warning","msg":"接受数据失败，clientID: 1","time":"2025-05-07T12:37:43.715041+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:61","func":"OnRecv","header":"\u003cnil\u003e","level":"info","msg":"player:31 clientID:1 disconnect","time":"2025-05-07T12:37:43.71598+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:227","func":"1","level":"debug","msg":"send loop routine end","time":"2025-05-07T12:37:43.716078+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:190","func":"run","level":"debug","msg":"run loop routine end","time":"2025-05-07T12:37:43.71646+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/netservicesimpl.go:100","func":"workOnExchanger","level":"info","msg":"客户端 1 关闭连接","time":"2025-05-07T12:37:43.71652+08:00"}
{"client_id":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/handle.go:52","func":"OnClientDisconnect","func_name":"ConnectionMgr.OnClientDisconnect","level":"info","msg":"client disconnected","time":"2025-05-07T12:37:43.716576+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/pushlish.go:58","func":"PushEventLogout","level":"info","msg":"logout event push success:player_id:31 product_id:1 channel_id:1001 event_type:ET_LOGOUT int_data:{key:4101 value:1184} str_data:{key:4103 value:\"\"}","time":"2025-05-07T12:37:43.720107+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:142","func":"DetachPlayer","level":"debug","msg":"DetachPlayer 解除和 player 的绑定,connect:1 playerId:31","time":"2025-05-07T12:37:43.720418+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:37:48.001542+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:38:48.00152+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='world' healthy='false' tag=''}","time":"2025-05-07T12:39:31.159811+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='task' healthy='false' tag=''}","time":"2025-05-07T12:39:31.540557+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='hall' healthy='false' tag=''}","time":"2025-05-07T12:39:37.398053+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='login' healthy='false' tag=''}","time":"2025-05-07T12:39:38.672885+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='msg' healthy='false' tag=''}","time":"2025-05-07T12:39:40.312891+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:39:48.0024+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:40:48.002297+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:41:48.002264+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:42:48.002296+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:43:48.002396+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:44:48.002368+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:45:48.002393+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:46:48.002385+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:47:48.003387+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-07T12:48:00.07143+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-07T12:48:00.358061+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-07T12:48:00.70142+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-07T12:48:00.82973+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-07T12:48:00.890463+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:48:48.003941+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:49:48.0039+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:50:48.004266+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:51:48.003541+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:52:48.002442+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:53:48.00244+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:54:48.001883+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:55:48.001907+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:56:48.002476+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:57:48.002292+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:58:48.003202+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T12:59:48.002804+08:00"}
