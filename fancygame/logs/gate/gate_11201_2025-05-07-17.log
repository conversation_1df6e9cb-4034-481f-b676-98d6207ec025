{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:00:48.002561+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T17:00:48.006598+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:01:48.001791+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T17:01:48.003749+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:02:48.001976+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T17:02:48.00349+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:03:48.001866+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T17:03:48.002482+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:04:48.001535+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T17:04:48.002775+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:05:48.003784+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T17:05:48.00631+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-07T17:28:37.600322+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-07T17:28:37.600986+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-07T17:28:37.601014+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-07T17:28:37.60103+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-07T17:28:37.601044+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-07T17:28:37.601059+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-07T17:28:37.601074+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-07T17:28:37.933379+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-07T17:28:38.270854+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-07T17:28:38.679165+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-07T17:28:38.67939+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-07T17:28:38.679466+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-07T17:28:38.679602+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-07T17:28:38.679678+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-07T17:28:38.679703+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-07T17:28:38.680056+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-07T17:28:38.697045+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000b0a000}","time":"2025-05-07T17:28:38.697088+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(gate)注册完成","time":"2025-05-07T17:28:38.697119+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/wsserver.go:136","func":"ServeHTTP","level":"info","msg":"SubProtocol:  network","time":"2025-05-07T17:29:11.852828+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/handle.go:23","func":"OnClientConnect","level":"info","msg":"client connected: client_id=1,ip=************","time":"2025-05-07T17:29:11.853407+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/netservicesimpl.go:95","func":"workOnExchanger","level":"info","msg":"[客户端 1 开启连接]","time":"2025-05-07T17:29:11.853567+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:122","func":"OnRecv","header":"msg_id:1101 send_seq:6451 body_length:224","level":"info","msg":"收到处理登录消息","player_id":0,"time":"2025-05-07T17:29:11.9165+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/login 成功","time":"2025-05-07T17:29:11.928459+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/services/login.go:71","func":"HandleLoginSession","header":"msg_id:1101 send_seq:6451 body_length:224 client_ip:\"************\"","level":"info","msg":"rsp from loginSrv rpc : ret:{desc:\"ERR_SUCCESS\"} player_id:31 token:\"1837257ca3af98f840faaf2c3c4b4359\" rich_user_info:{brief_user_info:{player_id:31 avatar:4020000 frame:4010000 lev:25} app_version:\"0.1.15b4\" acc_type:AT_PASSWORD register_time:1745045501 platform:PT_ANDROID app_language:LT_EN_US real_name_auth:true extend_user_info:{novice_guide:23}}","player_id":31,"time":"2025-05-07T17:29:12.00666+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-07T17:29:12.006961+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-07T17:29:12.018251+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/logic/another_check.go:27","func":"CheckAnother","gateAddr":"************:11201","level":"debug","msg":"检查是否顶号登录[31]，local : ************:11201","name":"checkAnother()","player_id":31,"time":"2025-05-07T17:29:12.022118+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/logic/another_login.go:24","func":"AnotherLogin","level":"debug","msg":"request=player_id:31 device:\"SM-S7310\" imei:\"997193f21d88cb609f81f27571162cdc\"","name":"AnotherLogin","player_id":31,"time":"2025-05-07T17:29:12.022504+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/logic/another_login.go:28","func":"AnotherLogin","level":"info","msg":"The player[31] is not at this gateway","name":"AnotherLogin","player_id":31,"time":"2025-05-07T17:29:12.022832+08:00"}
{"attach_player_id":31,"client_id":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:201","func":"AttachPlayer","ip":"************","level":"info","msg":"client[1] connect success player[1:31]","time":"2025-05-07T17:29:12.030197+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/services/login.go:100","func":"HandleLoginSession","header":"msg_id:1101 send_seq:6451 body_length:224 client_ip:\"************\"","level":"info","msg":"登录成功, response=ret:{desc:\"ERR_SUCCESS\"} player_id:31 token:\"1837257ca3af98f840faaf2c3c4b4359\" rich_user_info:{brief_user_info:{player_id:31 avatar:4020000 frame:4010000 lev:25} app_version:\"0.1.15b4\" acc_type:AT_PASSWORD register_time:1745045501 platform:PT_ANDROID app_language:LT_EN_US real_name_auth:true extend_user_info:{novice_guide:23}} version:0.1.14","player_id":31,"time":"2025-05-07T17:29:12.030473+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/pushlish.go:103","func":"PushEventLogin","level":"info","msg":"logout event push success:player_id:31 product_id:1 channel_id:1001 event_type:ET_LOGIN int_data:{key:1 value:1746610152} int_data:{key:4002 value:20250507} int_data:{key:4003 value:0}","time":"2025-05-07T17:29:12.031119+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_LOGIN_RSP,rsp_len=97,req_msg=CMD_LOGIN_REQ,req_seq=6451,client_id=1,player_id=0","time":"2025-05-07T17:29:12.031706+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-07T17:29:12.091059+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/hall 成功","time":"2025-05-07T17:29:12.106583+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_ROOM_INFO_RSP,rsp_len=28,req_msg=CMD_GET_ROOM_INFO_REQ,req_seq=6452,client_id=1,player_id=31","time":"2025-05-07T17:29:12.144215+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2001 send_seq:6452","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140002df400)}","player_id":31,"time":"2025-05-07T17:29:12.144394+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_PLAYER_ALL_RED_DOT_RSP,rsp_len=15,req_msg=CMD_GET_PLAYER_ALL_RED_DOT_REQ,req_seq=6453,client_id=1,player_id=31","time":"2025-05-07T17:29:12.170699+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2105 send_seq:6453","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140002dfe00)}","player_id":31,"time":"2025-05-07T17:29:12.170831+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/msg 成功","time":"2025-05-07T17:29:12.188682+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_MSG_GET_MAIL_LIST_RSP,rsp_len=509,req_msg=CMD_MSG_GET_MAIL_LIST_REQ,req_seq=6454,client_id=1,player_id=31","time":"2025-05-07T17:29:12.27674+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3601 send_seq:6454 body_length:4","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400020f3b0)}","player_id":31,"time":"2025-05-07T17:29:12.276965+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_MSG_GET_MAIL_LIST_RSP,rsp_len=17,req_msg=CMD_MSG_GET_MAIL_LIST_REQ,req_seq=6455,client_id=1,player_id=31","time":"2025-05-07T17:29:12.30772+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3601 send_seq:6455 body_length:4","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000310d70)}","player_id":31,"time":"2025-05-07T17:29:12.308024+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/world 成功","time":"2025-05-07T17:29:12.435182+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WORLD_TIME_RSP,rsp_len=27,req_msg=CMD_WORLD_GET_WORLD_TIME_REQ,req_seq=6456,client_id=1,player_id=31","time":"2025-05-07T17:29:12.456925+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3205 send_seq:6456 body_length:6","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140003bedc0)}","player_id":31,"time":"2025-05-07T17:29:12.457132+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_PLAYER_INFO_RSP,rsp_len=17,req_msg=CMD_GET_PLAYER_INFO_REQ,req_seq=6457,client_id=1,player_id=31","time":"2025-05-07T17:29:12.497029+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2017 send_seq:6457 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140004820a0)}","player_id":31,"time":"2025-05-07T17:29:12.497224+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_FIRST_ENTER_HALL_RSP,rsp_len=17,req_msg=CMD_FIRST_ENTER_HALL_REQ,req_seq=6458,client_id=1,player_id=31","time":"2025-05-07T17:29:12.534142+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2023 send_seq:6458 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000482960)}","player_id":31,"time":"2025-05-07T17:29:12.534366+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_LAST_GAME_INFO_RSP,rsp_len=17,req_msg=CMD_GET_LAST_GAME_INFO_REQ,req_seq=6459,client_id=1,player_id=31","time":"2025-05-07T17:29:12.558601+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2015 send_seq:6459","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400043eb40)}","player_id":31,"time":"2025-05-07T17:29:12.558705+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/sender_agent/send_local.go:33","func":"SendMultiPlayerMessage2Local","level":"debug","msg":"广播消息完成 playerIDs: [31] clientIDs: [1] msgID: CMD_TASK_UPDATE_NTF msgLen: 366","time":"2025-05-07T17:29:12.571076+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_ROOM_INFO_RSP,rsp_len=28,req_msg=CMD_GET_ROOM_INFO_REQ,req_seq=6460,client_id=1,player_id=31","time":"2025-05-07T17:29:12.590547+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2001 send_seq:6460","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140003bf7c0)}","player_id":31,"time":"2025-05-07T17:29:12.590652+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_ITEM_INFO_BY_TYPE_RSP,rsp_len=496,req_msg=CMD_GET_ITEM_INFO_BY_TYPE_REQ,req_seq=6461,client_id=1,player_id=31","time":"2025-05-07T17:29:12.686115+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2007 send_seq:6461 body_length:11","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001b50870)}","player_id":31,"time":"2025-05-07T17:29:12.686313+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_TRIP_ROD_RSP,rsp_len=54,req_msg=CMD_GET_TRIP_ROD_REQ,req_seq=6462,client_id=1,player_id=31","time":"2025-05-07T17:29:12.718187+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2045 send_seq:6462","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000eca780)}","player_id":31,"time":"2025-05-07T17:29:12.718268+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_TRIP_BAG_RSP,rsp_len=17,req_msg=CMD_GET_TRIP_BAG_REQ,req_seq=6463,client_id=1,player_id=31","time":"2025-05-07T17:29:12.751065+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2053 send_seq:6463 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001b50dc0)}","player_id":31,"time":"2025-05-07T17:29:12.751262+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_TRIP_BAG_RSP,rsp_len=78,req_msg=CMD_GET_TRIP_BAG_REQ,req_seq=6464,client_id=1,player_id=31","time":"2025-05-07T17:29:12.784204+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2053 send_seq:6464 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400043ed70)}","player_id":31,"time":"2025-05-07T17:29:12.784388+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WORLD_TIME_RSP,rsp_len=27,req_msg=CMD_WORLD_GET_WORLD_TIME_REQ,req_seq=6465,client_id=1,player_id=31","time":"2025-05-07T17:29:12.807076+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3205 send_seq:6465 body_length:6","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001b51400)}","player_id":31,"time":"2025-05-07T17:29:12.807282+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/task 成功","time":"2025-05-07T17:29:12.826921+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_GET_LIST_RSP,rsp_len=197,req_msg=CMD_TASK_GET_LIST_REQ,req_seq=6466,client_id=1,player_id=31","time":"2025-05-07T17:29:12.84996+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3501 send_seq:6466 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001b51db0)}","player_id":31,"time":"2025-05-07T17:29:12.850152+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_PROGRESS_RSP,rsp_len=17,req_msg=CMD_TASK_PROGRESS_REQ,req_seq=6467,client_id=1,player_id=31","time":"2025-05-07T17:29:12.878646+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3513 send_seq:6467 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001cb60a0)}","player_id":31,"time":"2025-05-07T17:29:12.878802+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_GET_LIST_RSP,rsp_len=260,req_msg=CMD_TASK_GET_LIST_REQ,req_seq=6468,client_id=1,player_id=31","time":"2025-05-07T17:29:12.902558+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3501 send_seq:6468 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001c1cd20)}","player_id":31,"time":"2025-05-07T17:29:12.902705+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_PROGRESS_RSP,rsp_len=17,req_msg=CMD_TASK_PROGRESS_REQ,req_seq=6469,client_id=1,player_id=31","time":"2025-05-07T17:29:12.930914+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3513 send_seq:6469 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001cb65f0)}","player_id":31,"time":"2025-05-07T17:29:12.931125+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_HALL_CONTINUOUS_LOGIN_RSP,rsp_len=15,req_msg=CMD_HALL_CONTINUOUS_LOGIN_REQ,req_seq=6470,client_id=1,player_id=31","time":"2025-05-07T17:29:12.95364+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2097 send_seq:6470","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001c1d310)}","player_id":31,"time":"2025-05-07T17:29:12.953758+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:231","func":"IncMsg","level":"error","msg":"1秒消息数超20告警: count=20,playerID=31,clientID=1","time":"2025-05-07T17:29:12.953931+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_PLAYER_INFO_RSP,rsp_len=17,req_msg=CMD_GET_PLAYER_INFO_REQ,req_seq=6471,client_id=1,player_id=31","time":"2025-05-07T17:29:12.981123+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2017 send_seq:6471 body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14000ecb400)}","player_id":31,"time":"2025-05-07T17:29:12.981296+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_SELL_ITEM_RSP,rsp_len=33,req_msg=CMD_SELL_ITEM_REQ,req_seq=6475,client_id=1,player_id=31","time":"2025-05-07T17:29:23.18842+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2083 send_seq:6475 body_length:49","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001cb7090)}","player_id":31,"time":"2025-05-07T17:29:23.18864+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:29:37.001097+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T17:29:37.002224+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:30:37.001287+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T17:30:37.002167+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[1]:1","time":"2025-05-07T17:31:37.012671+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:31:37.013184+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WEATHER_RSP,rsp_len=1065,req_msg=CMD_WORLD_GET_WEATHER_REQ,req_seq=6530,client_id=1,player_id=31","time":"2025-05-07T17:31:52.774257+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3201 send_seq:6530 body_length:8","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140006a4960)}","player_id":31,"time":"2025-05-07T17:31:52.7747+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WEATHER_RSP,rsp_len=1065,req_msg=CMD_WORLD_GET_WEATHER_REQ,req_seq=6531,client_id=1,player_id=31","time":"2025-05-07T17:31:52.798101+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3201 send_seq:6531 body_length:8","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140000a0fa0)}","player_id":31,"time":"2025-05-07T17:31:52.798209+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WEATHER_RSP,rsp_len=1065,req_msg=CMD_WORLD_GET_WEATHER_REQ,req_seq=6532,client_id=1,player_id=31","time":"2025-05-07T17:31:52.822931+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3201 send_seq:6532 body_length:8","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140003100a0)}","player_id":31,"time":"2025-05-07T17:31:52.823158+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WEATHER_RSP,rsp_len=1065,req_msg=CMD_WORLD_GET_WEATHER_REQ,req_seq=6534,client_id=1,player_id=31","time":"2025-05-07T17:31:56.545012+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3201 send_seq:6534 body_length:8","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400020e7d0)}","player_id":31,"time":"2025-05-07T17:31:56.545171+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:128","func":"OnRecv","header":"msg_id:1103 send_seq:6538 body_length:2","level":"info","msg":"处理登出消息","player_id":31,"time":"2025-05-07T17:32:07.873885+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:61","func":"GetConnection","level":"debug","msg":"GetConnection Debug login By Tag yes","time":"2025-05-07T17:32:07.885184+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:100","func":"GetConnectionWithDyeTag","level":"info","msg":"GRPC Connect With Tag : consul://************:8500/login?tag=debug","time":"2025-05-07T17:32:07.885988+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_LOGOUT_RSP,rsp_len=15,req_msg=CMD_LOGOUT_REQ,req_seq=6538,client_id=1,player_id=31","time":"2025-05-07T17:32:07.909801+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/wsserver.go:25","func":"Recv","level":"info","msg":"error: websocket: close 1000 (normal): Bye!","time":"2025-05-07T17:32:07.94996+08:00"}
{"error":"websocket: close 1000 (normal): Bye!","file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:292","func":"recv","level":"warning","msg":"接受数据失败，clientID: 1","time":"2025-05-07T17:32:07.950139+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:61","func":"OnRecv","header":"\u003cnil\u003e","level":"info","msg":"player:31 clientID:1 disconnect","time":"2025-05-07T17:32:07.950236+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:227","func":"1","level":"debug","msg":"send loop routine end","time":"2025-05-07T17:32:07.950305+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:190","func":"run","level":"debug","msg":"run loop routine end","time":"2025-05-07T17:32:07.950384+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/netservicesimpl.go:100","func":"workOnExchanger","level":"info","msg":"客户端 1 关闭连接","time":"2025-05-07T17:32:07.95043+08:00"}
{"client_id":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/handle.go:52","func":"OnClientDisconnect","func_name":"ConnectionMgr.OnClientDisconnect","level":"info","msg":"client disconnected","time":"2025-05-07T17:32:07.950488+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/pushlish.go:58","func":"PushEventLogout","level":"info","msg":"logout event push success:player_id:31 product_id:1 channel_id:1001 event_type:ET_LOGOUT int_data:{key:4101 value:176} str_data:{key:4103 value:\"\"}","time":"2025-05-07T17:32:07.950701+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:142","func":"DetachPlayer","level":"debug","msg":"DetachPlayer 解除和 player 的绑定,connect:1 playerId:31","time":"2025-05-07T17:32:07.95082+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:32:37.008542+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:33:37.002308+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:34:37.00264+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/gate","file_name":"gate_11201","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-07T17:35:15.290451+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {gate} run beginning...","time":"2025-05-07T17:35:15.290687+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-07T17:35:15.290713+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-07T17:35:15.290723+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/gate","time":"2025-05-07T17:35:15.290733+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:56","func":"Init","level":"info","msg":"gate服务Init","time":"2025-05-07T17:35:15.290742+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/subscribe.go:26","func":"Init","level":"info","msg":"NSQ模块初始化...","time":"2025-05-07T17:35:15.290755+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gate_info","time":"2025-05-07T17:35:15.690293+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/route_key","time":"2025-05-07T17:35:16.041745+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gray_strategy","time":"2025-05-07T17:35:16.426991+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/routetable/msgrange.go:109","func":"loadMsgRange","level":"debug","msg":"消息路由表加载完成","time":"2025-05-07T17:35:16.427195+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/config/config.go:77","func":"InitAntiAttack","level":"trace","msg":"AntiAttack安全策略配置：\u0026{false 20 30 50 20 3000}","time":"2025-05-07T17:35:16.427281+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[gate]服务初始化完成","time":"2025-05-07T17:35:16.427446+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/cmd/main.go:33","func":"Start","level":"info","msg":"gate服务启动成功","time":"2025-05-07T17:35:16.427512+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21201","time":"2025-05-07T17:35:16.427566+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-07T17:35:16.427848+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-07T17:35:16.443541+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000a92420}","time":"2025-05-07T17:35:16.443568+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(gate)注册完成","time":"2025-05-07T17:35:16.443607+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-07T17:35:22.81893+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-07T17:35:22.831013+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_EXP_LEVEL_CHANGE_NTF, msgLen: 74, playerIDs: [62], addr:************:11201]","time":"2025-05-07T17:35:22.835157+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_UPDATE_ITEM_NTF, msgLen: 379, playerIDs: [62], addr:************:11201]","time":"2025-05-07T17:35:23.01413+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_UPDATE_ROD_RIG_NTF, msgLen: 300, playerIDs: [62], addr:************:11201]","time":"2025-05-07T17:35:23.063734+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_UPDATE_ITEM_NTF, msgLen: 67, playerIDs: [62], addr:************:11201]","time":"2025-05-07T17:35:23.153083+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/wsserver.go:136","func":"ServeHTTP","level":"info","msg":"SubProtocol:  network","time":"2025-05-07T17:35:26.682396+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/handle.go:23","func":"OnClientConnect","level":"info","msg":"client connected: client_id=1,ip=************","time":"2025-05-07T17:35:26.682515+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/netservicesimpl.go:95","func":"workOnExchanger","level":"info","msg":"[客户端 1 开启连接]","time":"2025-05-07T17:35:26.682588+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:122","func":"OnRecv","header":"msg_id:1101  send_seq:6539  body_length:224","level":"info","msg":"收到处理登录消息","player_id":0,"time":"2025-05-07T17:35:26.710323+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:61","func":"GetConnection","level":"debug","msg":"GetConnection Debug login By Tag yes","time":"2025-05-07T17:35:26.723342+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:100","func":"GetConnectionWithDyeTag","level":"info","msg":"GRPC Connect With Tag : consul://************:8500/login?tag=debug","time":"2025-05-07T17:35:26.724092+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/services/login.go:71","func":"HandleLoginSession","header":"msg_id:1101  send_seq:6539  body_length:224  client_ip:\"************\"","level":"info","msg":"rsp from loginSrv rpc : ret:{desc:\"ERR_SUCCESS\"}  player_id:31  token:\"ed0ff1971d58a5bdf1fe3cd687ccf49c\"  rich_user_info:{brief_user_info:{player_id:31  avatar:4020000  frame:4010000  lev:25}  app_version:\"0.1.15b4\"  acc_type:AT_PASSWORD  register_time:1745045501  platform:PT_ANDROID  app_language:LT_EN_US  real_name_auth:true  extend_user_info:{novice_guide:23}}","player_id":31,"time":"2025-05-07T17:35:26.786828+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/logic/another_check.go:27","func":"CheckAnother","gateAddr":"","level":"debug","msg":"检查是否顶号登录[31]，local : ************:11201","name":"checkAnother()","player_id":31,"time":"2025-05-07T17:35:26.79487+08:00"}
{"attach_player_id":31,"client_id":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:201","func":"AttachPlayer","ip":"************","level":"info","msg":"client[1] connect success player[1:31]","time":"2025-05-07T17:35:26.802486+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/services/login.go:100","func":"HandleLoginSession","header":"msg_id:1101  send_seq:6539  body_length:224  client_ip:\"************\"","level":"info","msg":"登录成功, response=ret:{desc:\"ERR_SUCCESS\"}  player_id:31  token:\"ed0ff1971d58a5bdf1fe3cd687ccf49c\"  rich_user_info:{brief_user_info:{player_id:31  avatar:4020000  frame:4010000  lev:25}  app_version:\"0.1.15b4\"  acc_type:AT_PASSWORD  register_time:1745045501  platform:PT_ANDROID  app_language:LT_EN_US  real_name_auth:true  extend_user_info:{novice_guide:23}} version:0.1.14","player_id":31,"time":"2025-05-07T17:35:26.802647+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/pushlish.go:105","func":"PushEventLogin","level":"info","msg":"logout event push success:player_id:31  product_id:1  channel_id:1001  event_type:ET_LOGIN  int_data:{key:1  value:1746610526}  int_data:{key:4002  value:20250507}  int_data:{key:4003  value:0}","time":"2025-05-07T17:35:26.803035+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_LOGIN_RSP,rsp_len=97,req_msg=CMD_LOGIN_REQ,req_seq=6539,client_id=1,player_id=0","time":"2025-05-07T17:35:26.803123+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-07T17:35:26.84275+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/hall 成功","time":"2025-05-07T17:35:26.856392+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_ROOM_INFO_RSP,rsp_len=28,req_msg=CMD_GET_ROOM_INFO_REQ,req_seq=6540,client_id=1,player_id=31","time":"2025-05-07T17:35:26.892016+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2001  send_seq:6540","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140014ee280)}","player_id":31,"time":"2025-05-07T17:35:26.892102+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_PLAYER_ALL_RED_DOT_RSP,rsp_len=15,req_msg=CMD_GET_PLAYER_ALL_RED_DOT_REQ,req_seq=6541,client_id=1,player_id=31","time":"2025-05-07T17:35:26.914419+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2105  send_seq:6541","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140003676d0)}","player_id":31,"time":"2025-05-07T17:35:26.914521+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/msg 成功","time":"2025-05-07T17:35:26.93072+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_MSG_GET_MAIL_LIST_RSP,rsp_len=509,req_msg=CMD_MSG_GET_MAIL_LIST_REQ,req_seq=6542,client_id=1,player_id=31","time":"2025-05-07T17:35:27.020357+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3601  send_seq:6542  body_length:4","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140003579a0)}","player_id":31,"time":"2025-05-07T17:35:27.020424+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_MSG_GET_MAIL_LIST_RSP,rsp_len=17,req_msg=CMD_MSG_GET_MAIL_LIST_REQ,req_seq=6543,client_id=1,player_id=31","time":"2025-05-07T17:35:27.046976+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3601  send_seq:6543  body_length:4","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400156c550)}","player_id":31,"time":"2025-05-07T17:35:27.047104+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/world 成功","time":"2025-05-07T17:35:27.076259+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WORLD_TIME_RSP,rsp_len=27,req_msg=CMD_WORLD_GET_WORLD_TIME_REQ,req_seq=6544,client_id=1,player_id=31","time":"2025-05-07T17:35:27.097518+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3205  send_seq:6544  body_length:6","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140003755e0)}","player_id":31,"time":"2025-05-07T17:35:27.097605+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_PLAYER_INFO_RSP,rsp_len=17,req_msg=CMD_GET_PLAYER_INFO_REQ,req_seq=6545,client_id=1,player_id=31","time":"2025-05-07T17:35:27.123076+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2017  send_seq:6545  body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140014eec80)}","player_id":31,"time":"2025-05-07T17:35:27.123153+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_FIRST_ENTER_HALL_RSP,rsp_len=17,req_msg=CMD_FIRST_ENTER_HALL_REQ,req_seq=6546,client_id=1,player_id=31","time":"2025-05-07T17:35:27.155529+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2023  send_seq:6546  body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400156ce60)}","player_id":31,"time":"2025-05-07T17:35:27.155617+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_LAST_GAME_INFO_RSP,rsp_len=17,req_msg=CMD_GET_LAST_GAME_INFO_REQ,req_seq=6547,client_id=1,player_id=31","time":"2025-05-07T17:35:27.178549+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2015  send_seq:6547","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140014ef400)}","player_id":31,"time":"2025-05-07T17:35:27.178686+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_ROOM_INFO_RSP,rsp_len=28,req_msg=CMD_GET_ROOM_INFO_REQ,req_seq=6548,client_id=1,player_id=31","time":"2025-05-07T17:35:27.202607+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2001  send_seq:6548","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140014ef630)}","player_id":31,"time":"2025-05-07T17:35:27.202774+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_ITEM_INFO_BY_TYPE_RSP,rsp_len=496,req_msg=CMD_GET_ITEM_INFO_BY_TYPE_REQ,req_seq=6549,client_id=1,player_id=31","time":"2025-05-07T17:35:27.272588+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2007  send_seq:6549  body_length:11","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140014efb80)}","player_id":31,"time":"2025-05-07T17:35:27.272763+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_TRIP_ROD_RSP,rsp_len=54,req_msg=CMD_GET_TRIP_ROD_REQ,req_seq=6550,client_id=1,player_id=31","time":"2025-05-07T17:35:27.294677+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2045  send_seq:6550","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140014efdb0)}","player_id":31,"time":"2025-05-07T17:35:27.294786+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_TRIP_BAG_RSP,rsp_len=17,req_msg=CMD_GET_TRIP_BAG_REQ,req_seq=6551,client_id=1,player_id=31","time":"2025-05-07T17:35:27.324968+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2053  send_seq:6551  body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400166c410)}","player_id":31,"time":"2025-05-07T17:35:27.325146+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_TRIP_BAG_RSP,rsp_len=78,req_msg=CMD_GET_TRIP_BAG_REQ,req_seq=6552,client_id=1,player_id=31","time":"2025-05-07T17:35:27.349785+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2053  send_seq:6552  body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140016ec140)}","player_id":31,"time":"2025-05-07T17:35:27.349928+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_WORLD_GET_WORLD_TIME_RSP,rsp_len=27,req_msg=CMD_WORLD_GET_WORLD_TIME_REQ,req_seq=6553,client_id=1,player_id=31","time":"2025-05-07T17:35:27.37158+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3205  send_seq:6553  body_length:6","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400166c780)}","player_id":31,"time":"2025-05-07T17:35:27.371668+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/task 成功","time":"2025-05-07T17:35:27.388533+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_GET_LIST_RSP,rsp_len=197,req_msg=CMD_TASK_GET_LIST_REQ,req_seq=6554,client_id=1,player_id=31","time":"2025-05-07T17:35:27.412179+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3501  send_seq:6554  body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400166cd20)}","player_id":31,"time":"2025-05-07T17:35:27.412256+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_PROGRESS_RSP,rsp_len=17,req_msg=CMD_TASK_PROGRESS_REQ,req_seq=6555,client_id=1,player_id=31","time":"2025-05-07T17:35:27.43911+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3513  send_seq:6555  body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400156def0)}","player_id":31,"time":"2025-05-07T17:35:27.439236+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_GET_LIST_RSP,rsp_len=260,req_msg=CMD_TASK_GET_LIST_REQ,req_seq=6556,client_id=1,player_id=31","time":"2025-05-07T17:35:27.462613+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3501  send_seq:6556  body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x14001830280)}","player_id":31,"time":"2025-05-07T17:35:27.46268+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_TASK_PROGRESS_RSP,rsp_len=17,req_msg=CMD_TASK_PROGRESS_REQ,req_seq=6557,client_id=1,player_id=31","time":"2025-05-07T17:35:27.489897+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:3513  send_seq:6557  body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140015d0af0)}","player_id":31,"time":"2025-05-07T17:35:27.489986+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_HALL_CONTINUOUS_LOGIN_RSP,rsp_len=15,req_msg=CMD_HALL_CONTINUOUS_LOGIN_REQ,req_seq=6558,client_id=1,player_id=31","time":"2025-05-07T17:35:27.520139+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2097  send_seq:6558","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x1400166d270)}","player_id":31,"time":"2025-05-07T17:35:27.520252+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/repo/utils/response.go:54","func":"response","level":"info","msg":"回复成功: rsp_msg=CMD_GET_PLAYER_INFO_RSP,rsp_len=17,req_msg=CMD_GET_PLAYER_INFO_REQ,req_seq=6559,client_id=1,player_id=31","time":"2025-05-07T17:35:27.549157+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:148","func":"OnRecv","header":"msg_id:2017  send_seq:6559  body_length:2","level":"info","msg":"处理远程消息完成, responses: []*intranet_grpc.ResponseMessage{(*intranet_grpc.ResponseMessage)(0x140015d0e60)}","player_id":31,"time":"2025-05-07T17:35:27.549263+08:00"}
{"error":"websocket: close 1006 (abnormal closure): unexpected EOF","file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:292","func":"recv","level":"warning","msg":"接受数据失败，clientID: 1","time":"2025-05-07T17:35:40.620518+08:00"}
{"clientID":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/recv.go:61","func":"OnRecv","header":"\u003cnil\u003e","level":"info","msg":"player:31 clientID:1 disconnect","time":"2025-05-07T17:35:40.620835+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:227","func":"1","level":"debug","msg":"send loop routine end","time":"2025-05-07T17:35:40.620896+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/clientv2.go:190","func":"run","level":"debug","msg":"run loop routine end","time":"2025-05-07T17:35:40.620998+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/network/netservicesimpl.go:100","func":"workOnExchanger","level":"info","msg":"客户端 1 关闭连接","time":"2025-05-07T17:35:40.621035+08:00"}
{"client_id":1,"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/handle.go:52","func":"OnClientDisconnect","func_name":"ConnectionMgr.OnClientDisconnect","level":"info","msg":"client disconnected","time":"2025-05-07T17:35:40.621072+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/pushlish.go:41","func":"PushEventLogout","level":"info","msg":"recentMsgIDs:[3205 2017 2023 2015 2001 2007 2045 2053 2053 3205 3501 3513 3501 3513 2097 2017 1001 1001 1001 1001]","time":"2025-05-07T17:35:40.621118+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/pushlish.go:43","func":"PushEventLogout","level":"info","msg":"eventStrData:map[4103:]","time":"2025-05-07T17:35:40.621174+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/pubsub/pushlish.go:60","func":"PushEventLogout","level":"info","msg":"logout event push success:player_id:31  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:14}  str_data:{key:4103  value:\"\"}","time":"2025-05-07T17:35:40.621257+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:142","func":"DetachPlayer","level":"debug","msg":"DetachPlayer 解除和 player 的绑定,connect:1 playerId:31","time":"2025-05-07T17:35:40.621313+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:36:15.001474+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:37:15.002325+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:38:15.002193+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:39:15.002195+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:40:15.002302+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:41:15.002236+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:42:15.00222+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:43:15.002223+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:44:15.002256+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:45:15.002255+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:46:15.002117+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:47:15.002265+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:48:15.002235+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:49:15.002256+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:50:15.000951+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_TASK_UPDATE_NTF, msgLen: 195, playerIDs: [63], addr:************:11201]","time":"2025-05-07T17:50:21.528358+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:51:15.000581+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:52:15.001141+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:53:15.00128+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:54:15.001247+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_UPDATE_ITEM_NTF, msgLen: 446, playerIDs: [63], addr:************:11201]","time":"2025-05-07T17:54:41.637535+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_UPDATE_ITEM_NTF, msgLen: 335, playerIDs: [63], addr:************:11201]","time":"2025-05-07T17:54:41.765114+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_UPDATE_ROD_RIG_NTF, msgLen: 298, playerIDs: [63], addr:************:11201]","time":"2025-05-07T17:54:41.88911+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_UPDATE_ITEM_NTF, msgLen: 74, playerIDs: [63], addr:************:11201]","time":"2025-05-07T17:54:41.994277+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_ENTER_ROOM_BS_NTF, msgLen: 25, playerIDs: [63 61], addr:************:11201]","time":"2025-05-07T17:54:58.249737+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/conn/connection.go:49","func":"onlineAdjust","level":"debug","msg":"cron onlineAdjust[0]:0","time":"2025-05-07T17:55:15.001254+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/gatewaysrv/internal/handle/sender_impl.go:84","func":"SendMessage","level":"debug","msg":"玩家在其它网关服[msgID: CMD_ENTER_ROOM_BS_NTF, msgLen: 25, playerIDs: [63 61], addr:************:11201]","time":"2025-05-07T17:55:24.691114+08:00"}
