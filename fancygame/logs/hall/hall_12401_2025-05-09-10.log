{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/hall","file_name":"hall_12401","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-09T10:00:51.639921+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {hall} run beginning...","time":"2025-05-09T10:00:51.640135+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-09T10:00:51.640158+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-09T10:00:51.640168+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/hall","time":"2025-05-09T10:00:51.640177+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/cmd/main.go:29","func":"Init","level":"info","msg":"hall服务Init","time":"2025-05-09T10:00:51.640187+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/fish_pond_list","time":"2025-05-09T10:00:52.017181+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item_food","time":"2025-05-09T10:00:52.388483+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/continuous_login","time":"2025-05-09T10:00:52.770136+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/recharge_limit","time":"2025-05-09T10:00:53.106613+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/bobbers","time":"2025-05-09T10:00:53.487701+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/purchase_list","time":"2025-05-09T10:00:53.894251+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/store_buy","time":"2025-05-09T10:00:54.223965+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item_const","time":"2025-05-09T10:00:54.469104+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gift_basic","time":"2025-05-09T10:00:54.705531+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/lines","time":"2025-05-09T10:00:55.064981+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/reels","time":"2025-05-09T10:00:55.466539+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/rig_rule","time":"2025-05-09T10:00:55.83165+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/trip_bag_store_rule","time":"2025-05-09T10:00:56.19765+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/rods","time":"2025-05-09T10:00:56.553646+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/goods_basic","time":"2025-05-09T10:00:56.86528+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item","time":"2025-05-09T10:00:57.250625+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/role_level","time":"2025-05-09T10:00:57.631566+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/store_room","time":"2025-05-09T10:00:58.040621+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/real_name_auth","time":"2025-05-09T10:00:58.397087+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/novice_scene_guide","time":"2025-05-09T10:00:58.744041+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_rig/rig_rule.go:38","func":"LoadRigRuleCache","level":"info","msg":"rig rule cache load success","time":"2025-05-09T10:00:58.744339+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_item initial word success config= {Broker:[************:9092] GroupID: Topic:r_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T10:00:58.74468+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T10:00:58.744805+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/forbid/forbid.go:31","func":"InitForbid","level":"info","msg":"[forbid] load forbid start path : ../forbid.txt","time":"2025-05-09T10:00:58.744935+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/forbid/forbid.go:34","func":"InitForbid","level":"error","msg":"[forbid] 读取屏蔽字文件失败","time":"2025-05-09T10:00:58.745012+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[hall]服务初始化完成","time":"2025-05-09T10:00:58.745101+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:22401","time":"2025-05-09T10:00:58.74533+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-09T10:00:58.745628+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-09T10:00:58.764038+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140001f1080}","time":"2025-05-09T10:00:58.764109+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(hall)注册完成","time":"2025-05-09T10:00:58.764135+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:930","func":"GetPlayerAllRedDot","level":"debug","msg":"GetPlayerAllRedDot:","player_id":66,"productID":1,"time":"2025-05-09T10:06:43.828666+08:00","trace_id":"406213fd-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-09T10:06:43.829945+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-09T10:06:43.844239+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/mysql/mysql.go:95","func":"init","level":"info","msg":"Mysql 配置列表加载完成","time":"2025-05-09T10:06:43.848287+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_player_state/player_state.go:54","func":"QueryPlayerStateBmStr","level":"debug","msg":"get player:66 state bm info field:red_dot, ","player_id":66,"productID":1,"time":"2025-05-09T10:06:43.890876+08:00","trace_id":"406213fd-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_red_dot/red_dot.go:128","func":"GetPlayerAllRedDots","level":"debug","msg":"【Get All Red Dots】playerID=66, total=0, enabled=0","player_id":66,"productID":1,"time":"2025-05-09T10:06:43.891204+08:00","trace_id":"406213fd-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:940","func":"GetPlayerAllRedDot","level":"debug","msg":"GetPlayerAllRedDot success: playerID=66, count=0","player_id":66,"productID":1,"time":"2025-05-09T10:06:43.891292+08:00","trace_id":"406213fd-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:63","func":"FirstEnterHallReq","level":"debug","msg":"FirstEnterHallReq req playerId=66 channelId=1001","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.259592+08:00","trace_id":"40a414ae-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/asset 成功","time":"2025-05-09T10:06:44.272064+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/repo/rpc_asset/rpc_asset.go:108","func":"GetPlayerCategoryListItemInfo","level":"info","msg":"get player category list:[IC_CURRENCY] info success, rsp:ret:{desc:\"ERR_SUCCESS\"}  player_id:66  item_list:{item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  item_count:4800}  item_list:{item:{item_id:104  item_category:IC_CURRENCY  item_type:IT_CURRENCY_EXP}  item_count:1100}","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.302789+08:00","trace_id":"40a414ae-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_player/player.go:60","func":"QueryPlayerInfo","level":"debug","msg":"query player:66 info success, playerInfo:player_id:66  exp_num:1100  coins:4800  expLevel:4","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.303306+08:00","trace_id":"40a414ae-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/repo/rpc_asset/rpc_asset.go:108","func":"GetPlayerCategoryListItemInfo","level":"info","msg":"get player category list:[IC_CURRENCY] info success, rsp:ret:{desc:\"ERR_SUCCESS\"}  player_id:66  item_list:{item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  item_count:4800}  item_list:{item:{item_id:104  item_category:IC_CURRENCY  item_type:IT_CURRENCY_EXP}  item_count:1100}","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.334395+08:00","trace_id":"40a414ae-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_hall/hall_scene.go:40","func":"SendPlayerInitLevelReward","level":"info","msg":"player:66 exp num:1100 already get init award","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.334479+08:00","trace_id":"40a414ae-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:80","func":"FirstEnterHallReq","level":"debug","msg":"player:66, first enter hall success, req:is_reg:true, rsp:player_info:{player_id:66  exp_num:1100  coins:4800  expLevel:4}","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.334604+08:00","trace_id":"40a414ae-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_item/item.go:53","func":"PlayerQueryItemInfo","level":"info","msg":"query player:66, itemType:-1, categoryList:[IC_REWARD IC_GOODS IC_EQUIP IC_TICKET IC_PROP IC_TACKLE IC_WEARABLE IC_FRAGMENTS IC_CATCH IC_CURRENCY]","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.351069+08:00","trace_id":"40b218dc-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/repo/rpc_asset/rpc_asset.go:108","func":"GetPlayerCategoryListItemInfo","level":"info","msg":"get player category list:[IC_REWARD IC_GOODS IC_EQUIP IC_TICKET IC_PROP IC_TACKLE IC_WEARABLE IC_FRAGMENTS IC_CATCH IC_CURRENCY] info success, rsp:ret:{desc:\"ERR_SUCCESS\"}  player_id:66  item_list:{item:{item_id:301200101  item_category:IC_TACKLE  item_type:IT_TACKLE_RODS  instanceId:\"a65de02a-2b3a-11f0-8c77-080027d1b278\"  update_time:1746619134  extra:{key:1  value:115200}  extra:{key:2  value:115200}}  item_count:1}  item_list:{item:{item_id:302100201  item_category:IC_TACKLE  item_type:IT_TACKLE_REEl  instanceId:\"a65de15e-2b3a-11f0-8c77-080027d1b278\"  update_time:1746619134  extra:{key:1  value:46080}  extra:{key:2  value:46080}}  item_count:1}  item_list:{item:{item_id:30510400301  item_category:IC_TACKLE  item_type:IT_TACKLE_BAIT  instanceId:\"a6607c63-2b3a-11f0-a2b0-080027d1b278\"  update_time:1746619134}  item_count:50}  item_list:{item:{item_id:30930300101  item_category:IC_TACKLE  item_type:IT_TACKLE_LURES  instanceId:\"a6607c7c-2b3a-11f0-a2b0-080027d1b278\"  update_time:1746619134}  item_count:1}  item_list:{item:{item_id:30810100101  item_category:IC_TACKLE  item_type:IT_TACKLE_HOOKS  instanceId:\"ec7f7ffe-2b3a-11f0-a2b0-080027d1b278\"  update_time:1746619260}  item_count:9}  item_list:{item:{item_id:4010000  item_category:IC_WEARABLE  item_type:IT_WEARABLE_HEAD_FRAME  update_time:1746619240}  item_count:1}  item_list:{item:{item_id:4020000  item_category:IC_WEARABLE  item_type:IT_WEARABLE_AVATAR  update_time:1746619240}  item_count:1}  item_list:{item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  item_count:4800}  item_list:{item:{item_id:104  item_category:IC_CURRENCY  item_type:IT_CURRENCY_EXP}  item_count:1100}","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.415267+08:00","trace_id":"40b218dc-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:204","func":"GetItemInfoByTypeReq","level":"debug","msg":"get player:66, item type:-1, query item info success, req:item_type:-1, rsp:ret:{desc:\"ERR_SUCCESS\"}  item_info:{item:{item_id:301200101  item_category:IC_TACKLE  item_type:IT_TACKLE_RODS  instanceId:\"a65de02a-2b3a-11f0-8c77-080027d1b278\"  update_time:1746619134  extra:{key:1  value:115200}  extra:{key:2  value:115200}}  item_count:1}  item_info:{item:{item_id:302100201  item_category:IC_TACKLE  item_type:IT_TACKLE_REEl  instanceId:\"a65de15e-2b3a-11f0-8c77-080027d1b278\"  update_time:1746619134  extra:{key:1  value:46080}  extra:{key:2  value:46080}}  item_count:1}  item_info:{item:{item_id:30510400301  item_category:IC_TACKLE  item_type:IT_TACKLE_BAIT  instanceId:\"a6607c63-2b3a-11f0-a2b0-080027d1b278\"  update_time:1746619134}  item_count:50}  item_info:{item:{item_id:30930300101  item_category:IC_TACKLE  item_type:IT_TACKLE_LURES  instanceId:\"a6607c7c-2b3a-11f0-a2b0-080027d1b278\"  update_time:1746619134}  item_count:1}  item_info:{item:{item_id:30810100101  item_category:IC_TACKLE  item_type:IT_TACKLE_HOOKS  instanceId:\"ec7f7ffe-2b3a-11f0-a2b0-080027d1b278\"  update_time:1746619260}  item_count:9}  item_info:{item:{item_id:4010000  item_category:IC_WEARABLE  item_type:IT_WEARABLE_HEAD_FRAME  update_time:1746619240}  item_count:1}  item_info:{item:{item_id:4020000  item_category:IC_WEARABLE  item_type:IT_WEARABLE_AVATAR  update_time:1746619240}  item_count:1}  item_info:{item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  item_count:4800}  item_info:{item:{item_id:104  item_category:IC_CURRENCY  item_type:IT_CURRENCY_EXP}  item_count:1100}","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.415547+08:00","trace_id":"40b218dc-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:458","func":"GetTripBagReq","level":"debug","msg":"GetTripBagReq req type:TBT_FOOD","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.44603+08:00","trace_id":"40c08cbd-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/repo/rpc_asset/rpc_asset.go:108","func":"GetPlayerCategoryListItemInfo","level":"info","msg":"get player category list:[IC_PROP] info success, rsp:ret:{desc:\"ERR_SUCCESS\"}  player_id:66","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.464716+08:00","trace_id":"40c08cbd-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:475","func":"GetTripBagReq","level":"debug","msg":"GetTripBagReq rsp ret:{desc:\"ERR_SUCCESS\"}  type:TBT_FOOD","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.464914+08:00","trace_id":"40c08cbd-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:888","func":"ContinuousLogin","level":"debug","msg":"ContinoutsLoginReq:","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.555499+08:00","trace_id":"40d13eb5-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-09T10:06:44.565067+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:898","func":"ContinuousLogin","level":"debug","msg":"ContinoutsLoginReq Successs ","player_id":66,"productID":1,"time":"2025-05-09T10:06:44.569019+08:00","trace_id":"40d13eb5-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:699","func":"DelTripRodReq","level":"debug","msg":"DelTripRodReq req id:53","player_id":66,"productID":1,"time":"2025-05-09T10:07:04.018112+08:00","trace_id":"4c5ce0ae-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-09T10:07:04.033511+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/gate 成功","time":"2025-05-09T10:07:04.109578+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_notify/notify.go:87","func":"PlayerRodRigNotify","level":"debug","msg":"notify player rod :[id:53  info:{key:1  value:{item_id:301400101  item_category:IC_TACKLE  item_type:IT_TACKLE_RODS  instanceId:\"a646b24b-2b3a-11f0-8c77-080027d1b278\"  extra:{key:1  value:576000}  extra:{key:2  value:576000}}}  info:{key:2  value:{item_id:302100201  item_category:IC_TACKLE  item_type:IT_TACKLE_REEl  instanceId:\"a646b3ab-2b3a-11f0-8c77-080027d1b278\"  extra:{key:1  value:46080}  extra:{key:2  value:46080}}}  info:{key:3  value:{item_id:303100101  item_category:IC_TACKLE  item_type:IT_TACKLE_LINE  instanceId:\"a646b4ed-2b3a-11f0-8c77-080027d1b278\"  extra:{key:1  value:46080}  extra:{key:2  value:46080}}}  info:{key:6  value:{item_id:306500101  item_category:IC_TACKLE  item_type:IT_TACKLE_BOBBERS  extra:{key:1  value:0}  extra:{key:2  value:576000}}}  name:\"钓组1\"]","player_id":66,"productID":1,"time":"2025-05-09T10:07:04.133817+08:00","trace_id":"4c5ce0ae-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:715","func":"DelTripRodReq","level":"debug","msg":"DelTripRodReq rsp ret:{desc:\"ERR_SUCCESS\"}  id:53  info:{id:53  info:{key:1  value:{item_id:301400101  item_category:IC_TACKLE  item_type:IT_TACKLE_RODS  instanceId:\"a646b24b-2b3a-11f0-8c77-080027d1b278\"  extra:{key:1  value:576000}  extra:{key:2  value:576000}}}  info:{key:2  value:{item_id:302100201  item_category:IC_TACKLE  item_type:IT_TACKLE_REEl  instanceId:\"a646b3ab-2b3a-11f0-8c77-080027d1b278\"  extra:{key:1  value:46080}  extra:{key:2  value:46080}}}  info:{key:3  value:{item_id:303100101  item_category:IC_TACKLE  item_type:IT_TACKLE_LINE  instanceId:\"a646b4ed-2b3a-11f0-8c77-080027d1b278\"  extra:{key:1  value:46080}  extra:{key:2  value:46080}}}  info:{key:6  value:{item_id:306500101  item_category:IC_TACKLE  item_type:IT_TACKLE_BOBBERS  extra:{key:1  value:0}  extra:{key:2  value:576000}}}  name:\"钓组1\"}","player_id":66,"productID":1,"time":"2025-05-09T10:07:04.137841+08:00","trace_id":"4c5ce0ae-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_notify/notify.go:87","func":"PlayerRodRigNotify","level":"debug","msg":"notify player rod :[id:53  info:{key:1  value:{item_id:301400101  item_category:IC_TACKLE  item_type:IT_TACKLE_RODS  extra:{key:1  value:0}  extra:{key:2  value:576000}}}  info:{key:2  value:{item_id:302100201  item_category:IC_TACKLE  item_type:IT_TACKLE_REEl  instanceId:\"a646b3ab-2b3a-11f0-8c77-080027d1b278\"  extra:{key:1  value:46080}  extra:{key:2  value:46080}}}  info:{key:3  value:{item_id:303100101  item_category:IC_TACKLE  item_type:IT_TACKLE_LINE  instanceId:\"a646b4ed-2b3a-11f0-8c77-080027d1b278\"  extra:{key:1  value:46080}  extra:{key:2  value:46080}}}  info:{key:6  value:{item_id:306500101  item_category:IC_TACKLE  item_type:IT_TACKLE_BOBBERS  extra:{key:1  value:0}  extra:{key:2  value:576000}}}  name:\"钓组1\"  bag_index:3]","player_id":66,"productID":1,"time":"2025-05-09T10:07:23.138099+08:00","trace_id":"57bf04d2-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_notify/notify.go:87","func":"PlayerRodRigNotify","level":"debug","msg":"notify player rod :[id:53  info:{key:1  value:{item_id:301400101  item_category:IC_TACKLE  item_type:IT_TACKLE_RODS  extra:{key:1  value:0}  extra:{key:2  value:576000}}}  info:{key:2  value:{item_id:302100201  item_category:IC_TACKLE  item_type:IT_TACKLE_REEl  extra:{key:1  value:0}  extra:{key:2  value:46080}}}  info:{key:3  value:{item_id:303100101  item_category:IC_TACKLE  item_type:IT_TACKLE_LINE  extra:{key:1  value:0}  extra:{key:2  value:46080}}}  info:{key:6  value:{item_id:306500101  item_category:IC_TACKLE  item_type:IT_TACKLE_BOBBERS  extra:{key:1  value:0}  extra:{key:2  value:576000}}}  name:\"钓组1\"  bag_index:3]","player_id":66,"productID":1,"time":"2025-05-09T10:07:23.335584+08:00","trace_id":"57dab082-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:699","func":"DelTripRodReq","level":"debug","msg":"DelTripRodReq req id:53","player_id":66,"productID":1,"time":"2025-05-09T10:07:30.626425+08:00","trace_id":"5c46ebdb-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_notify/notify.go:87","func":"PlayerRodRigNotify","level":"debug","msg":"notify player rod :[id:53  info:{key:1  value:{item_id:301400101  item_category:IC_TACKLE  item_type:IT_TACKLE_RODS  extra:{key:1  value:0}  extra:{key:2  value:576000}}}  info:{key:2  value:{item_id:302100201  item_category:IC_TACKLE  item_type:IT_TACKLE_REEl  extra:{key:1  value:0}  extra:{key:2  value:46080}}}  info:{key:3  value:{item_id:303100101  item_category:IC_TACKLE  item_type:IT_TACKLE_LINE  extra:{key:1  value:0}  extra:{key:2  value:46080}}}  info:{key:6  value:{item_id:306500101  item_category:IC_TACKLE  item_type:IT_TACKLE_BOBBERS  extra:{key:1  value:0}  extra:{key:2  value:576000}}}  name:\"钓组1\"]","player_id":66,"productID":1,"time":"2025-05-09T10:07:30.715028+08:00","trace_id":"5c46ebdb-2c7a-11f0-835a-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:715","func":"DelTripRodReq","level":"debug","msg":"DelTripRodReq rsp ret:{desc:\"ERR_SUCCESS\"}  id:53  info:{id:53  info:{key:1  value:{item_id:301400101  item_category:IC_TACKLE  item_type:IT_TACKLE_RODS  extra:{key:1  value:0}  extra:{key:2  value:576000}}}  info:{key:2  value:{item_id:302100201  item_category:IC_TACKLE  item_type:IT_TACKLE_REEl  extra:{key:1  value:0}  extra:{key:2  value:46080}}}  info:{key:3  value:{item_id:303100101  item_category:IC_TACKLE  item_type:IT_TACKLE_LINE  extra:{key:1  value:0}  extra:{key:2  value:46080}}}  info:{key:6  value:{item_id:306500101  item_category:IC_TACKLE  item_type:IT_TACKLE_BOBBERS  extra:{key:1  value:0}  extra:{key:2  value:576000}}}  name:\"钓组1\"}","player_id":66,"productID":1,"time":"2025-05-09T10:07:30.718828+08:00","trace_id":"5c46ebdb-2c7a-11f0-835a-080027d1b278"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/hall","file_name":"hall_12401","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-09T10:14:12.257205+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {hall} run beginning...","time":"2025-05-09T10:14:12.257579+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-09T10:14:12.257613+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-09T10:14:12.257625+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/hall","time":"2025-05-09T10:14:12.257636+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/cmd/main.go:29","func":"Init","level":"info","msg":"hall服务Init","time":"2025-05-09T10:14:12.257648+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/recharge_limit","time":"2025-05-09T10:14:12.635319+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item","time":"2025-05-09T10:14:13.060539+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/store_buy","time":"2025-05-09T10:14:13.452555+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item_const","time":"2025-05-09T10:14:13.852191+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/role_level","time":"2025-05-09T10:14:14.220605+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/rods","time":"2025-05-09T10:14:14.518519+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/rig_rule","time":"2025-05-09T10:14:14.882709+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/store_room","time":"2025-05-09T10:14:15.282132+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/real_name_auth","time":"2025-05-09T10:14:15.640462+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/goods_basic","time":"2025-05-09T10:14:16.021927+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/continuous_login","time":"2025-05-09T10:14:16.329567+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/purchase_list","time":"2025-05-09T10:14:16.668342+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/bobbers","time":"2025-05-09T10:14:17.047498+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/reels","time":"2025-05-09T10:14:17.414282+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item_food","time":"2025-05-09T10:14:17.786419+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gift_basic","time":"2025-05-09T10:14:18.136762+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/lines","time":"2025-05-09T10:14:18.502533+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/trip_bag_store_rule","time":"2025-05-09T10:14:18.89803+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/novice_scene_guide","time":"2025-05-09T10:14:19.498916+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/fish_pond_list","time":"2025-05-09T10:14:19.91308+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_rig/rig_rule.go:38","func":"LoadRigRuleCache","level":"info","msg":"rig rule cache load success","time":"2025-05-09T10:14:19.913234+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_item initial word success config= {Broker:[************:9092] GroupID: Topic:r_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T10:14:19.913413+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T10:14:19.913482+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/forbid/forbid.go:31","func":"InitForbid","level":"info","msg":"[forbid] load forbid start path : ../forbid.txt","time":"2025-05-09T10:14:19.913506+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/forbid/forbid.go:34","func":"InitForbid","level":"error","msg":"[forbid] 读取屏蔽字文件失败","time":"2025-05-09T10:14:19.913543+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[hall]服务初始化完成","time":"2025-05-09T10:14:19.913564+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:22401","time":"2025-05-09T10:14:19.913706+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-09T10:14:19.913972+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-09T10:14:19.928368+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000b051e0}","time":"2025-05-09T10:14:19.928394+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(hall)注册完成","time":"2025-05-09T10:14:19.928423+08:00"}
