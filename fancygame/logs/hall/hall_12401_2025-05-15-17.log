{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/hall","file_name":"hall_12401","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-15T17:55:26.962917+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {hall} run beginning...","time":"2025-05-15T17:55:26.963712+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-15T17:55:26.96379+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-15T17:55:26.963826+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/hall","time":"2025-05-15T17:55:26.96387+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/cmd/main.go:29","func":"Init","level":"info","msg":"hall服务Init","time":"2025-05-15T17:55:26.963897+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/role_level","time":"2025-05-15T17:55:27.347082+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/reels","time":"2025-05-15T17:55:27.761604+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/store_room","time":"2025-05-15T17:55:28.107823+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/purchase_list","time":"2025-05-15T17:55:28.467216+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gift_basic","time":"2025-05-15T17:55:28.838803+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/store_buy","time":"2025-05-15T17:55:29.153841+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item_food","time":"2025-05-15T17:55:29.445592+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/rods","time":"2025-05-15T17:55:29.846628+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/rig_rule","time":"2025-05-15T17:55:30.175233+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/continuous_login","time":"2025-05-15T17:55:30.51662+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/fish_pond_list","time":"2025-05-15T17:55:30.792639+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/trip_bag_store_rule","time":"2025-05-15T17:55:31.144402+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/novice_scene_guide","time":"2025-05-15T17:55:31.501302+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/recharge_limit","time":"2025-05-15T17:55:31.886354+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item","time":"2025-05-15T17:55:32.297303+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item_const","time":"2025-05-15T17:55:32.66795+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/lines","time":"2025-05-15T17:55:33.062892+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/real_name_auth","time":"2025-05-15T17:55:33.441229+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/bobbers","time":"2025-05-15T17:55:33.825895+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/goods_basic","time":"2025-05-15T17:55:34.196143+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_rig/rig_rule.go:38","func":"LoadRigRuleCache","level":"info","msg":"rig rule cache load success","time":"2025-05-15T17:55:34.196287+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_item initial word success config= {Broker:[************:9092] GroupID: Topic:r_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-15T17:55:34.196447+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-15T17:55:34.19652+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/forbid/forbid.go:31","func":"InitForbid","level":"info","msg":"[forbid] load forbid start path : ../forbid.txt","time":"2025-05-15T17:55:34.196541+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/forbid/forbid.go:34","func":"InitForbid","level":"error","msg":"[forbid] 读取屏蔽字文件失败","time":"2025-05-15T17:55:34.196582+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[hall]服务初始化完成","time":"2025-05-15T17:55:34.196601+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:22401","time":"2025-05-15T17:55:34.196757+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to 192.168.1.58:8500\n","time":"2025-05-15T17:55:34.196931+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : 192.168.1.58:8500","time":"2025-05-15T17:55:34.218979+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140005cb600}","time":"2025-05-15T17:55:34.219006+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(hall)注册完成","time":"2025-05-15T17:55:34.219034+08:00"}
