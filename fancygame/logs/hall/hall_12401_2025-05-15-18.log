{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/hall","file_name":"hall_12401","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-15T18:04:08.673798+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {hall} run beginning...","time":"2025-05-15T18:04:08.67451+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-15T18:04:08.674628+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-15T18:04:08.674723+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/hall","time":"2025-05-15T18:04:08.674789+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/cmd/main.go:29","func":"Init","level":"info","msg":"hall服务Init","time":"2025-05-15T18:04:08.674835+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/trip_bag_store_rule","time":"2025-05-15T18:04:09.099218+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/store_room","time":"2025-05-15T18:04:09.547444+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/purchase_list","time":"2025-05-15T18:04:09.910793+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/continuous_login","time":"2025-05-15T18:04:10.236651+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/bobbers","time":"2025-05-15T18:04:10.640486+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/role_level","time":"2025-05-15T18:04:11.027547+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/rods","time":"2025-05-15T18:04:11.347698+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/lines","time":"2025-05-15T18:04:11.724162+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/novice_scene_guide","time":"2025-05-15T18:04:12.102104+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/goods_basic","time":"2025-05-15T18:04:12.530581+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item_const","time":"2025-05-15T18:04:12.940743+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item_food","time":"2025-05-15T18:04:13.304733+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gift_basic","time":"2025-05-15T18:04:13.694616+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/rig_rule","time":"2025-05-15T18:04:14.099338+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/real_name_auth","time":"2025-05-15T18:04:14.448137+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/recharge_limit","time":"2025-05-15T18:04:14.792561+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/fish_pond_list","time":"2025-05-15T18:04:15.184513+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item","time":"2025-05-15T18:04:15.584139+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/reels","time":"2025-05-15T18:04:15.807532+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/store_buy","time":"2025-05-15T18:04:16.186003+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_rig/rig_rule.go:38","func":"LoadRigRuleCache","level":"info","msg":"rig rule cache load success","time":"2025-05-15T18:04:16.186163+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_item initial word success config= {Broker:[************:9092] GroupID: Topic:r_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-15T18:04:16.186336+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-15T18:04:16.186397+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/forbid/forbid.go:31","func":"InitForbid","level":"info","msg":"[forbid] load forbid start path : ../forbid.txt","time":"2025-05-15T18:04:16.186419+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/forbid/forbid.go:34","func":"InitForbid","level":"error","msg":"[forbid] 读取屏蔽字文件失败","time":"2025-05-15T18:04:16.186453+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[hall]服务初始化完成","time":"2025-05-15T18:04:16.186474+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:22401","time":"2025-05-15T18:04:16.186653+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-15T18:04:16.186903+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-15T18:04:16.207062+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140009ecb00}","time":"2025-05-15T18:04:16.207093+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(hall)注册完成","time":"2025-05-15T18:04:16.207128+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:1011","func":"CDKeyExchange","level":"debug","msg":"CDKeyExchangeReq:cd_key:\"0LfP4BcJj8Wn\"","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.104524+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_cdk/cdk.go:22","func":"CDKeyExchange","level":"debug","msg":"CDKeyExchange 请求: cd_key:\"0LfP4BcJj8Wn\"","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.105945+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-15T18:05:38.106242+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-05-15T18:05:38.119151+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/dao/dao_cdk/cdk_rdb.go:118","func":"GetCdkBatchInfoByCdk","level":"info","msg":"GetCdkBatchInfoByCdk: starting query for CDK code: 0LfP4BcJj8Wn","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.123726+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/mysql/mysql.go:95","func":"init","level":"info","msg":"Mysql 配置列表加载完成","time":"2025-05-15T18:05:38.12396+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/dao/dao_cdk/cdk_rdb.go:156","func":"UpdateCdkRecordUsage","level":"info","msg":"UpdateCdkRecordUsage: starting update for CDK code: 0LfP4BcJj8Wn with used_bm: 0100000000000000000000003a3000000100000000000000100000000c00","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.167175+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/dao/dao_cdk/cdk_rdb.go:179","func":"UpdateCdkRecordUsage","level":"info","msg":"UpdateCdkRecordUsage: successfully updated record for CDK code: 0LfP4BcJj8Wn, affected rows: 1","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.188198+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/asset 成功","time":"2025-05-15T18:05:38.202608+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/repo/rpc_asset/rpc_asset.go:152","func":"PlayerOperateItemReq","level":"info","msg":"player:12 operate item:[item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  value:10000 item:{item_id:102  item_category:IC_CURRENCY  item_type:IT_CURRENCY_DIAMOND}  value:100], optType:1, srcType:25, success, rsp:ret:{desc:\"ERR_SUCCESS\"}  transaction:\"1747303538202-12H4sB\"  timestamp:1747303537  item_list:{item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  item_count:18900  item_delta_count:10000}  item_list:{item:{item_id:102  item_category:IC_CURRENCY  item_type:IT_CURRENCY_DIAMOND}  item_count:200  item_delta_count:100}","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.254641+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/repo/record/log.go:62","func":"ItemRecord","level":"debug","msg":"add item record, player:12, RewardInfo:claimID:\"1747303538202-12H4sB\"  source_type:IST_CDK_REWARD  timestamp:1747303538  item_list:{item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  item_count:18900  item_delta_count:10000}  item_list:{item:{item_id:102  item_category:IC_CURRENCY  item_type:IT_CURRENCY_DIAMOND}  item_count:200  item_delta_count:100}","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.255147+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/gate 成功","time":"2025-05-15T18:05:38.266117+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_notify/notify.go:27","func":"PlayerRewardInfoNotify","level":"debug","msg":"notify player 12 update item info success, btf:reward_info:{claimID:\"1747303538202-12H4sB\"  source_type:IST_CDK_REWARD  timestamp:1747303538  item_list:{item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  item_count:18900  item_delta_count:10000}  item_list:{item:{item_id:102  item_category:IC_CURRENCY  item_type:IT_CURRENCY_DIAMOND}  item_count:200  item_delta_count:100}}  storage:ST_STORE","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.303276+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_item/pond_item.go:29","func":"PlayerPondItemChange","level":"debug","msg":"item change param empty","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.303414+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_role/exp.go:37","func":"CheckPlayerExpLevelChange","level":"debug","msg":"check player:12 exp level change: no exp info, rewardInfo:claimID:\"1747303538202-12H4sB\"  source_type:IST_CDK_REWARD  timestamp:1747303538  item_list:{item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  item_count:18900  item_delta_count:10000}  item_list:{item:{item_id:102  item_category:IC_CURRENCY  item_type:IT_CURRENCY_DIAMOND}  item_count:200  item_delta_count:100}","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.303493+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_item/item.go:197","func":"OperatePlayerItem","level":"debug","msg":"operator playerId:12, productId:1, itemList:[{\"item_id\":101,\"item_count\":10000},{\"item_id\":102,\"item_count\":100}], is_unpack:true, optType:1, srcType:25, rewardInfo:claimID:\"1747303538202-12H4sB\"  source_type:IST_CDK_REWARD  timestamp:1747303538  item_list:{item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  item_count:18900  item_delta_count:10000}  item_list:{item:{item_id:102  item_category:IC_CURRENCY  item_type:IT_CURRENCY_DIAMOND}  item_count:200  item_delta_count:100}","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.303618+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/repo/record/log.go:104","func":"LogItemRecord","level":"debug","msg":"ItemRecord In uid:12 - product_id1","time":"2025-05-15T18:05:38.303907+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_cdk/cdk.go:107","func":"CDKeyExchange","level":"info","msg":"player:12 cdk:0LfP4BcJj8Wn has reward","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.303984+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/proc/hall_service.go:1022","func":"CDKeyExchange","level":"debug","msg":"CDKeyExchangeReq Successs cd_key:\"0LfP4BcJj8Wn\"","player_id":12,"productID":1,"time":"2025-05-15T18:05:38.312132+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/repo/record/log.go:99","func":"ItemRecord","level":"debug","msg":"add item record success, player:12, RewardInfo:claimID:\"1747303538202-12H4sB\"  source_type:IST_CDK_REWARD  timestamp:1747303538  item_list:{item:{item_id:101  item_category:IC_CURRENCY  item_type:IT_CURRENCY_COIN}  item_count:18900  item_delta_count:10000}  item_list:{item:{item_id:102  item_category:IC_CURRENCY  item_type:IT_CURRENCY_DIAMOND}  item_count:200  item_delta_count:100}","player_id":12,"productID":1,"time":"2025-05-15T18:05:40.27804+08:00","trace_id":"2632e82e-3174-11f0-b58d-080027d1b278"}
