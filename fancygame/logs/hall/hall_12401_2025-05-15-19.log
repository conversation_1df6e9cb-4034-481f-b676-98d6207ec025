{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='asset' healthy='false' tag=''}","time":"2025-05-15T19:00:29.992945+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='gate' healthy='false' tag=''}","time":"2025-05-15T19:00:50.658996+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='gate' healthy='false' tag=''}","time":"2025-05-15T19:09:15.819797+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='asset' healthy='false' tag=''}","time":"2025-05-15T19:09:25.197771+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-15T19:09:54.788219+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-15T19:09:54.850144+08:00"}
