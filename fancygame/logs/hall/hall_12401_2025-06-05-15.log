{"fields.level":"trace","file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/lib/logdog/logger.go:160","file_dir":"../../logs/hall","file_name":"hall_12401","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-05T15:53:26.144219+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {hall} run beginning...","time":"2025-06-05T15:53:26.144567+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-05T15:53:26.144587+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-05T15:53:26.144599+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/hall","time":"2025-06-05T15:53:26.144612+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/cmd/main.go:29","func":"Init","level":"info","msg":"hall服务Init","time":"2025-06-05T15:53:26.144624+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/real_name_auth","time":"2025-06-05T15:53:26.73772+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/real_name_auth","time":"2025-06-05T15:53:26.743942+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/real_name_auth","time":"2025-06-05T15:53:26.743956+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/real_name_auth_cfg.go:110","func":"LoadAllRealNameAuthCfg","level":"info","msg":"successfully loaded RealNameAuth config for channels: [1001 2001 1002]","time":"2025-06-05T15:53:26.743953+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/recharge_limit","time":"2025-06-05T15:53:27.36795+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/recharge_limit","time":"2025-06-05T15:53:27.378471+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/recharge_limit","time":"2025-06-05T15:53:27.40198+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/recharge_limit_cfg.go:132","func":"LoadAllRechargeLimitCfg","level":"info","msg":"successfully loaded RechargeLimit config for channels: [1002 2001 1001]","time":"2025-06-05T15:53:27.401979+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/bobbers","time":"2025-06-05T15:53:27.815597+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/bobbers","time":"2025-06-05T15:53:27.857753+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/bobbers","time":"2025-06-05T15:53:27.867007+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/bobbers_cfg.go:144","func":"LoadAllBobbersCfg","level":"info","msg":"successfully loaded Bobbers config for channels: [2001 1001 1002]","time":"2025-06-05T15:53:27.867008+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/fish_pond_list","time":"2025-06-05T15:53:28.198472+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/fish_pond_list","time":"2025-06-05T15:53:28.22036+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/fish_pond_list","time":"2025-06-05T15:53:28.245559+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/fish_pond_list_cfg.go:140","func":"LoadAllFishPondListCfg","level":"info","msg":"successfully loaded FishPondList config for channels: [2001 1002 1001]","time":"2025-06-05T15:53:28.245561+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/gift_basic","time":"2025-06-05T15:53:28.587037+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/gift_basic","time":"2025-06-05T15:53:28.629857+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/gift_basic","time":"2025-06-05T15:53:28.635223+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/gift_basic_cfg.go:137","func":"LoadAllGiftBasicCfg","level":"info","msg":"successfully loaded GiftBasic config for channels: [1002 2001 1001]","time":"2025-06-05T15:53:28.635224+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/lines","time":"2025-06-05T15:53:28.955838+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/lines","time":"2025-06-05T15:53:28.965609+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/lines","time":"2025-06-05T15:53:29.070534+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/lines_cfg.go:153","func":"LoadAllLinesCfg","level":"info","msg":"successfully loaded Lines config for channels: [2001 1002 1001]","time":"2025-06-05T15:53:29.070534+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/reels","time":"2025-06-05T15:53:29.358501+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/reels","time":"2025-06-05T15:53:29.383363+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/reels_cfg.go:163","func":"LoadAllReelsCfg","level":"info","msg":"successfully loaded Reels config for channels: [2001 1002 1001]","time":"2025-06-05T15:53:29.438695+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/reels","time":"2025-06-05T15:53:29.438697+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/purchase_list","time":"2025-06-05T15:53:29.79104+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/purchase_list","time":"2025-06-05T15:53:29.824301+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/purchase_list","time":"2025-06-05T15:53:29.824302+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/purchase_list_cfg.go:138","func":"LoadAllPurchaseListCfg","level":"info","msg":"successfully loaded PurchaseList config for channels: [1001 2001 1002]","time":"2025-06-05T15:53:29.824304+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/trip_bag_store_rule","time":"2025-06-05T15:53:31.266088+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/trip_bag_store_rule","time":"2025-06-05T15:53:31.269539+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/trip_bag_store_rule_cfg.go:130","func":"LoadAllTripBagStoreRuleCfg","level":"info","msg":"successfully loaded TripBagStoreRule config for channels: [1001 1002 2001]","time":"2025-06-05T15:53:31.293903+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/trip_bag_store_rule","time":"2025-06-05T15:53:31.293896+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/continuous_login","time":"2025-06-05T15:53:31.675784+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/continuous_login","time":"2025-06-05T15:53:31.686128+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/continuous_login_cfg.go:138","func":"LoadAllContinuousLoginCfg","level":"info","msg":"successfully loaded ContinuousLogin config for channels: [2001 1002 1001]","time":"2025-06-05T15:53:31.730083+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/continuous_login","time":"2025-06-05T15:53:31.73026+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/goods_basic","time":"2025-06-05T15:53:32.144699+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/goods_basic","time":"2025-06-05T15:53:32.155282+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/goods_basic_cfg.go:135","func":"LoadAllGoodsBasicCfg","level":"info","msg":"successfully loaded GoodsBasic config for channels: [1002 1001 2001]","time":"2025-06-05T15:53:32.177628+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/goods_basic","time":"2025-06-05T15:53:32.177219+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/store_buy","time":"2025-06-05T15:53:32.568174+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/store_buy","time":"2025-06-05T15:53:32.568575+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/store_buy","time":"2025-06-05T15:53:32.603605+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/store_buy_cfg.go:135","func":"LoadAllStoreBuyCfg","level":"info","msg":"successfully loaded StoreBuy config for channels: [2001 1001 1002]","time":"2025-06-05T15:53:32.603605+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/item","time":"2025-06-05T15:53:32.896885+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/item","time":"2025-06-05T15:53:32.917517+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item","time":"2025-06-05T15:53:32.987955+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/item_cfg.go:141","func":"LoadAllItemCfg","level":"info","msg":"successfully loaded Item config for channels: [1002 2001 1001]","time":"2025-06-05T15:53:32.987956+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/item_food","time":"2025-06-05T15:53:33.312281+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/item_food","time":"2025-06-05T15:53:33.325922+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item_food","time":"2025-06-05T15:53:33.365389+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/item_food_cfg.go:135","func":"LoadAllItemFoodCfg","level":"info","msg":"successfully loaded ItemFood config for channels: [2001 1002 1001]","time":"2025-06-05T15:53:33.365391+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/role_level","time":"2025-06-05T15:53:33.636585+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/role_level","time":"2025-06-05T15:53:33.677+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/role_level","time":"2025-06-05T15:53:33.69105+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/role_level_cfg.go:139","func":"LoadAllRoleLevelCfg","level":"info","msg":"successfully loaded RoleLevel config for channels: [1001 1002 2001]","time":"2025-06-05T15:53:33.691048+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/item_const","time":"2025-06-05T15:53:33.942647+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/item_const","time":"2025-06-05T15:53:33.991957+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/item_const","time":"2025-06-05T15:53:33.99462+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/item_const_cfg.go:103","func":"LoadAllItemConstCfg","level":"info","msg":"successfully loaded ItemConst config for channels: [1001 2001 1002]","time":"2025-06-05T15:53:33.994618+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/rods","time":"2025-06-05T15:53:34.268912+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/rods","time":"2025-06-05T15:53:34.305879+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/rods_cfg.go:161","func":"LoadAllRodsCfg","level":"info","msg":"successfully loaded Rods config for channels: [1001 1002 2001]","time":"2025-06-05T15:53:34.315028+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/rods","time":"2025-06-05T15:53:34.315022+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/rig_rule","time":"2025-06-05T15:53:34.599551+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/rig_rule","time":"2025-06-05T15:53:34.62506+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/rig_rule_cfg.go:138","func":"LoadAllRigRuleCfg","level":"info","msg":"successfully loaded RigRule config for channels: [1002 2001 1001]","time":"2025-06-05T15:53:34.711842+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/rig_rule","time":"2025-06-05T15:53:34.711861+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/store_room","time":"2025-06-05T15:53:34.981954+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/store_room","time":"2025-06-05T15:53:35.025094+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/store_room","time":"2025-06-05T15:53:35.113791+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/store_room_cfg.go:136","func":"LoadAllStoreRoomCfg","level":"info","msg":"successfully loaded StoreRoom config for channels: [1002 2001 1001]","time":"2025-06-05T15:53:35.113795+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/novice_scene_guide","time":"2025-06-05T15:53:35.399476+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1002/novice_scene_guide","time":"2025-06-05T15:53:35.424699+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/novice_scene_guide","time":"2025-06-05T15:53:35.431259+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/fancy-common@v0.0.0-20250528074837-27779cd4d4f4/pkg/cmodel/novice_scene_guide_cfg.go:133","func":"LoadAllNoviceSceneGuideCfg","level":"info","msg":"successfully loaded NoviceSceneGuide config for channels: [1001 1002 2001]","time":"2025-06-05T15:53:35.43126+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/hallsrv/internal/logic/logic_rig/rig_rule.go:38","func":"LoadRigRuleCache","level":"info","msg":"rig rule cache load success","time":"2025-06-05T15:53:35.431565+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_item initial word success config= {Broker:[************:9092] GroupID: Topic:r_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-05T15:53:35.43172+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-05T15:53:35.431769+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/forbid/forbid.go:31","func":"InitForbid","level":"info","msg":"[forbid] load forbid start path : ../forbid.txt","time":"2025-06-05T15:53:35.43179+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/forbid/forbid.go:34","func":"InitForbid","level":"error","msg":"[forbid] 读取屏蔽字文件失败","time":"2025-06-05T15:53:35.431834+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[hall]服务初始化完成","time":"2025-06-05T15:53:35.431865+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:22401","time":"2025-06-05T15:53:35.432051+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to 192.168.1.58:8500\n","time":"2025-06-05T15:53:35.432192+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : 192.168.1.58:8500","time":"2025-06-05T15:53:35.450634+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14001a07080}","time":"2025-06-05T15:53:35.450666+08:00"}
{"file":"/Users/<USER>/go/pkg/mod/git.keepfancy.xyz/back-end/frameworks@v0.0.0-20250519093126-5b299a18b217/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(hall)注册完成","time":"2025-06-05T15:53:35.450692+08:00"}
