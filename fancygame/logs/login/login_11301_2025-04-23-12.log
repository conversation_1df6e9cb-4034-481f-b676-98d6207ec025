{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/login","file_name":"login_11301","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-04-23T12:12:40.281139+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {login} run beginning...","time":"2025-04-23T12:12:40.28138+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-04-23T12:12:40.281402+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-04-23T12:12:40.281411+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/login","time":"2025-04-23T12:12:40.281421+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/cmd/main.go:28","func":"Init","level":"info","msg":"login服务Init","time":"2025-04-23T12:12:40.281431+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-04-23T12:12:40.281514+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-04-23T12:12:40.281569+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-04-23T12:12:40.281595+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_login initial word success config= {Broker:[************:9092] GroupID: Topic:r_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-04-23T12:12:40.28162+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_register initial word success config= {Broker:[************:9092] GroupID: Topic:r_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-04-23T12:12:40.281648+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: consumer_channel_login_broker","time":"2025-04-23T12:12:40.29898+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-04-23T12:12:40.313637+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[login]服务初始化完成","time":"2025-04-23T12:12:40.313706+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21301","time":"2025-04-23T12:12:40.31401+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:148","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-04-23T12:12:40.313981+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/cmd/main.go:54","func":"Start","level":"info","msg":"login服务启动成功","time":"2025-04-23T12:12:40.31426+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-04-23T12:12:40.314337+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-04-23T12:12:40.325344+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140007ba000}","time":"2025-04-23T12:12:40.325409+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(login)注册完成","time":"2025-04-23T12:12:40.325445+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/login","file_name":"login_11301","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-04-23T12:14:11.49381+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {login} run beginning...","time":"2025-04-23T12:14:11.494939+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-04-23T12:14:11.495046+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-04-23T12:14:11.495084+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/login","time":"2025-04-23T12:14:11.495117+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/cmd/main.go:28","func":"Init","level":"info","msg":"login服务Init","time":"2025-04-23T12:14:11.495146+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-04-23T12:14:11.495365+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-04-23T12:14:11.495545+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-04-23T12:14:11.495621+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_login initial word success config= {Broker:[************:9092] GroupID: Topic:r_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-04-23T12:14:11.495688+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_register initial word success config= {Broker:[************:9092] GroupID: Topic:r_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-04-23T12:14:11.495816+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:244","func":"createTopicChannel","level":"error","msg":"topic请求失败，statusCode: 502","time":"2025-04-23T12:14:11.50286+08:00","url":"http://************:4151/topic/create?topic=dev_ET_LOGOUT"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-04-23T12:14:11.516279+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[login]服务初始化完成","time":"2025-04-23T12:14:11.516358+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21301","time":"2025-04-23T12:14:11.516525+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:148","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-04-23T12:14:11.516542+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/cmd/main.go:54","func":"Start","level":"info","msg":"login服务启动成功","time":"2025-04-23T12:14:11.516714+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-04-23T12:14:11.516766+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-04-23T12:14:11.530475+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000175600}","time":"2025-04-23T12:14:11.530537+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(login)注册完成","time":"2025-04-23T12:14:11.530569+08:00"}
