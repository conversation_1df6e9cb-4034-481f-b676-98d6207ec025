{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/login","file_name":"login_11301","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T11:11:07.458493+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {login} run beginning...","time":"2025-05-06T11:11:07.458793+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T11:11:07.458811+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T11:11:07.458824+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/login","time":"2025-05-06T11:11:07.458836+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/cmd/main.go:28","func":"Init","level":"info","msg":"login服务Init","time":"2025-05-06T11:11:07.458847+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/white_list","time":"2025-05-06T11:11:07.776809+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/location_block","time":"2025-05-06T11:11:08.066743+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/real_name_auth","time":"2025-05-06T11:11:08.342809+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/block_strategy","time":"2025-05-06T11:11:08.633806+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-06T11:11:08.633899+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-06T11:11:08.633998+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-06T11:11:08.634043+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_login initial word success config= {Broker:[************:9092] GroupID: Topic:r_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-06T11:11:08.634075+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_register initial word success config= {Broker:[************:9092] GroupID: Topic:r_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-06T11:11:08.634104+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: consumer_channel_login_broker","time":"2025-05-06T11:11:08.651059+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-05-06T11:11:08.669215+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[login]服务初始化完成","time":"2025-05-06T11:11:08.669268+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21301","time":"2025-05-06T11:11:08.669449+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-06T11:11:08.669454+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/cmd/main.go:58","func":"Start","level":"info","msg":"login服务启动成功","time":"2025-05-06T11:11:08.669721+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T11:11:08.669832+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T11:11:08.687677+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000902b00}","time":"2025-05-06T11:11:08.687739+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(login)注册完成","time":"2025-05-06T11:11:08.687772+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/login","file_name":"login_11301","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T11:11:32.350523+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {login} run beginning...","time":"2025-05-06T11:11:32.350933+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T11:11:32.350965+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T11:11:32.350977+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/login","time":"2025-05-06T11:11:32.350986+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/cmd/main.go:28","func":"Init","level":"info","msg":"login服务Init","time":"2025-05-06T11:11:32.350997+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/real_name_auth","time":"2025-05-06T11:11:32.636511+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/block_strategy","time":"2025-05-06T11:11:32.898144+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/white_list","time":"2025-05-06T11:11:33.186791+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/location_block","time":"2025-05-06T11:11:33.459035+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-06T11:11:33.459132+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-06T11:11:33.459215+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-06T11:11:33.459247+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_login initial word success config= {Broker:[************:9092] GroupID: Topic:r_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-06T11:11:33.459277+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_register initial word success config= {Broker:[************:9092] GroupID: Topic:r_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-06T11:11:33.459302+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: consumer_channel_login_broker","time":"2025-05-06T11:11:33.476903+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-05-06T11:11:33.499397+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[login]服务初始化完成","time":"2025-05-06T11:11:33.499454+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21301","time":"2025-05-06T11:11:33.499569+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-06T11:11:33.499644+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/cmd/main.go:58","func":"Start","level":"info","msg":"login服务启动成功","time":"2025-05-06T11:11:33.499754+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T11:11:33.499873+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T11:11:33.513182+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000314f20}","time":"2025-05-06T11:11:33.51325+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(login)注册完成","time":"2025-05-06T11:11:33.513282+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:27  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:1002}","time":"2025-05-06T11:13:05.284782+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:27","time":"2025-05-06T11:13:05.286041+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-6NL19QH\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-6NL19QH\"  device_code:\"cde0695d2baf8d45948d77872296ed69bd8dc6600\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:13:18.573871+08:00","trace_id":"0ee808a7-2a28-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/client.go:80","func":"GetConnectionByRobin","level":"info","msg":"GetConnection Connect : consul://************:8500/user 成功","time":"2025-05-06T11:13:18.586406+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 27","player_id":0,"productID":0,"time":"2025-05-06T11:13:18.610278+08:00","trace_id":"0ee808a7-2a28-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:13:18.610943+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 27, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:13:18.64065+08:00","trace_id":"0ee808a7-2a28-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:27 - product_id1","time":"2025-05-06T11:13:18.662312+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_FACEBOOK  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"samsung SM-S916U1\"  device_brand:\"Galaxy S23+\"  os:\"Android OS 14 / API-34 (UP1A.231005.007/S916U1UES6CYB3)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2340X1080\"  device_name:\"Galaxy S23+\"  device_code:\"5daa55902968ead52795c12d31870246\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame.GP\"  ip:\"************\"  platform:PT_ANDROID  third_info:{open_id:\"2445847**********\"  token:\"EAAd6E15hmfABOzZAC0yVqrlLxIHdCq0egoaaOnZAZATQR7x4uqLsjA26kUh5ZC2KAYrhFKT3fzZCVghYwRIsrxD22ra5hYLRMrGZAAo63rXPpKmDHTWNTkvoV1SrfTces7iukVk5vE43vzk23KhV9OluwxoBQYtuUc9WUbiKrNKdjPFhZCsYxdza7kLYnjyCZB00WcCKoZAV9UehwsQUmaZCELKryvD7rEesNK76XCssHNdqpRQy3XEt1ZA31JXcZCoZD\"}]","player_id":0,"productID":0,"time":"2025-05-06T11:13:24.480398+08:00","trace_id":"126efd20-2a28-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/sdk/facebook/facebook.go:21","func":"GetUserAccount","level":"info","msg":"GetUserAccount called with: accessToken=EAAd...CoZD, code=","time":"2025-05-06T11:13:24.480635+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/sdk/facebook/facebook.go:56","func":"getUserInfo","level":"debug","msg":"facebook get user info request: url=https://graph.facebook.com/v19.0/me, params=map[access_token:[EAAd6E15hmfABOzZAC0yVqrlLxIHdCq0egoaaOnZAZATQR7x4uqLsjA26kUh5ZC2KAYrhFKT3fzZCVghYwRIsrxD22ra5hYLRMrGZAAo63rXPpKmDHTWNTkvoV1SrfTces7iukVk5vE43vzk23KhV9OluwxoBQYtuUc9WUbiKrNKdjPFhZCsYxdza7kLYnjyCZB00WcCKoZAV9UehwsQUmaZCELKryvD7rEesNK76XCssHNdqpRQy3XEt1ZA31JXcZCoZD] appsecret_proof:[a868f6654d4c9634be4bf280e7929e94d6a1b396c2b5f26cf65699bfcf67cacd] fields:[id,name,picture,email]]","time":"2025-05-06T11:13:24.480826+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/httpx/get.go:58","func":"GetBackJson","level":"info","msg":"GetBackJson :{\"id\":\"2445847**********\",\"name\":\"\\u90c1\\u6052\",\"picture\":{\"data\":{\"height\":50,\"is_silhouette\":false,\"url\":\"https:\\/\\/platform-lookaside.fbsbx.com\\/platform\\/profilepic\\/?asid=2445847**********\u0026height=50\u0026width=50\u0026ext=**********\u0026hash=AbZROQc1itNK8RNrD7XbTTu7\",\"width\":50}},\"email\":\"*********\\u0040qq.com\"}","time":"2025-05-06T11:13:25.55429+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/sdk/facebook/facebook.go:64","func":"getUserInfo","level":"debug","msg":"facebook get user info response: {\"id\":\"2445847**********\",\"name\":\"\\u90c1\\u6052\",\"picture\":{\"data\":{\"height\":50,\"is_silhouette\":false,\"url\":\"https:\\/\\/platform-lookaside.fbsbx.com\\/platform\\/profilepic\\/?asid=2445847**********\u0026height=50\u0026width=50\u0026ext=**********\u0026hash=AbZROQc1itNK8RNrD7XbTTu7\",\"width\":50}},\"email\":\"*********\\u0040qq.com\"}","time":"2025-05-06T11:13:25.554739+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/sdk/facebook/facebook.go:38","func":"GetUserAccount","level":"info","msg":"Successfully got user info: name=郁恒, openid=2445847**********","time":"2025-05-06T11:13:25.555215+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:13:25.575636+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_FACEBOOK login success - playerId: 260, isReg: true","player_id":0,"productID":0,"time":"2025-05-06T11:13:25.614011+08:00","trace_id":"126efd20-2a28-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:128","func":"RegisterRecord","level":"debug","msg":"LoginRecord In uid:260 - product_id1","time":"2025-05-06T11:13:25.647208+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:260 - product_id1","time":"2025-05-06T11:13:25.647222+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:260  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:13}","time":"2025-05-06T11:13:37.668699+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:260","time":"2025-05-06T11:13:37.66996+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:27  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:453}","time":"2025-05-06T11:20:51.165427+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:27","time":"2025-05-06T11:20:51.16886+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-6NL19QH\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-6NL19QH\"  device_code:\"meijun1006\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:21:08.95276+08:00","trace_id":"2747946f-2a29-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 256","player_id":0,"productID":0,"time":"2025-05-06T11:21:08.974719+08:00","trace_id":"2747946f-2a29-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:21:08.976732+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 256, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:21:09.016383+08:00","trace_id":"2747946f-2a29-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:256 - product_id1","time":"2025-05-06T11:21:09.040386+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:259  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:633}","time":"2025-05-06T11:21:50.344389+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:259","time":"2025-05-06T11:21:50.345494+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:25:08.876461+08:00","trace_id":"b6494161-2a29-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 259","player_id":0,"productID":0,"time":"2025-05-06T11:25:08.894579+08:00","trace_id":"b6494161-2a29-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:25:08.895262+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 259, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:25:08.917308+08:00","trace_id":"b6494161-2a29-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:259 - product_id1","time":"2025-05-06T11:25:08.940003+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"0.1.16\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"Xiaomi 2410DPN6CC\"  device_brand:\"Xiaomi 15 Pro\"  os:\"Android OS 15 / API-35 (AQ3A.240812.002/OS2.0.101.0.VOBCNXM)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2400X1080\"  device_name:\"Xiaomi 15 Pro\"  device_code:\"28e5457af474e932ec81f6efbe2b4990\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_ANDROID  account_info:{account:\"13538250023lhX\"  password:\"32167lyX\"}]","player_id":0,"productID":0,"time":"2025-05-06T11:27:49.350902+08:00","trace_id":"15ef4a7b-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:87","func":"Login","level":"info","msg":"账号登录:13538250023lhX - playerId : 227","player_id":0,"productID":0,"time":"2025-05-06T11:27:49.368844+08:00","trace_id":"15ef4a7b-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:27:49.385503+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_PASSWORD login success - playerId: 227, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:27:49.416829+08:00","trace_id":"15ef4a7b-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:227 - product_id1","time":"2025-05-06T11:27:49.439256+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-GB3Q0LI\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-GB3Q0LI\"  device_code:\"bfe71c711da67372cfe9cd1a5aabdc138f76827e1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:30:12.051667+08:00","trace_id":"6afc4d8d-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 190","player_id":0,"productID":0,"time":"2025-05-06T11:30:12.080964+08:00","trace_id":"6afc4d8d-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:30:12.081691+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 190, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:30:12.112037+08:00","trace_id":"6afc4d8d-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:190 - product_id1","time":"2025-05-06T11:30:12.137822+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:32:19.696398+08:00","trace_id":"b7131424-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 259","player_id":0,"productID":0,"time":"2025-05-06T11:32:19.718413+08:00","trace_id":"b7131424-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:32:19.718996+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 259, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:32:19.744377+08:00","trace_id":"b7131424-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:259 - product_id1","time":"2025-05-06T11:32:19.767422+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/proc/login_service.go:50","func":"DeleteAccountReq","level":"info","msg":"delete product:1 account:259 success","player_id":259,"productID":1,"time":"2025-05-06T11:32:30.154651+08:00","trace_id":"bd498355-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:259  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:11}","time":"2025-05-06T11:32:30.179978+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:259","time":"2025-05-06T11:32:30.18071+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:32:32.664734+08:00","trace_id":"becde5ae-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 0","player_id":0,"productID":0,"time":"2025-05-06T11:32:32.683234+08:00","trace_id":"becde5ae-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:32:32.683696+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 261, isReg: true","player_id":0,"productID":0,"time":"2025-05-06T11:32:32.718078+08:00","trace_id":"becde5ae-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:128","func":"RegisterRecord","level":"debug","msg":"LoginRecord In uid:261 - product_id1","time":"2025-05-06T11:32:32.741991+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:261 - product_id1","time":"2025-05-06T11:32:32.742031+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-N8380UN\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-N8380UN\"  device_code:\"a28e11d0d26ef9c80538bc67ecc94fbdd852486c0\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:33:00.373143+08:00","trace_id":"cf4b97ff-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 1","player_id":0,"productID":0,"time":"2025-05-06T11:33:00.392152+08:00","trace_id":"cf4b97ff-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:33:00.393182+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 1, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:33:00.425647+08:00","trace_id":"cf4b97ff-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:1 - product_id1","time":"2025-05-06T11:33:00.450908+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-6NL19QH\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-6NL19QH\"  device_code:\"meijun1006\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:33:26.705976+08:00","trace_id":"df03dd53-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 256","player_id":0,"productID":0,"time":"2025-05-06T11:33:26.72769+08:00","trace_id":"df03dd53-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:33:26.728528+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 256, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:33:26.752385+08:00","trace_id":"df03dd53-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:256 - product_id1","time":"2025-05-06T11:33:26.775095+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-6NL19QH\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-6NL19QH\"  device_code:\"meijun1006\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:33:51.093525+08:00","trace_id":"ed8d025a-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 256","player_id":0,"productID":0,"time":"2025-05-06T11:33:51.11927+08:00","trace_id":"ed8d025a-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:33:51.119651+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 256, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:33:51.138737+08:00","trace_id":"ed8d025a-2a2a-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:256 - product_id1","time":"2025-05-06T11:33:51.160134+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:1  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:91}","time":"2025-05-06T11:34:31.256105+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:1","time":"2025-05-06T11:34:31.257452+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-N8380UN\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-N8380UN\"  device_code:\"a28e11d0d26ef9c80538bc67ecc94fbdd852486c0\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:35:42.539467+08:00","trace_id":"2ffa710e-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 1","player_id":0,"productID":0,"time":"2025-05-06T11:35:42.555674+08:00","trace_id":"2ffa710e-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:35:42.555942+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 1, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:35:42.575797+08:00","trace_id":"2ffa710e-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:1 - product_id1","time":"2025-05-06T11:35:42.595945+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/proc/login_service.go:50","func":"DeleteAccountReq","level":"info","msg":"delete product:1 account:261 success","player_id":261,"productID":1,"time":"2025-05-06T11:37:35.063574+08:00","trace_id":"********-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:37:54.339247+08:00","trace_id":"7e894e1a-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 0","player_id":0,"productID":0,"time":"2025-05-06T11:37:54.359456+08:00","trace_id":"7e894e1a-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:37:54.36013+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 262, isReg: true","player_id":0,"productID":0,"time":"2025-05-06T11:37:54.400502+08:00","trace_id":"7e894e1a-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:128","func":"RegisterRecord","level":"debug","msg":"LoginRecord In uid:262 - product_id1","time":"2025-05-06T11:37:54.423395+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:262 - product_id1","time":"2025-05-06T11:37:54.423432+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-GB3Q0LI\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-GB3Q0LI\"  device_code:\"bfe71c711da67372cfe9cd1a5aabdc138f76827e1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:38:13.99136+08:00","trace_id":"8a40236f-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 190","player_id":0,"productID":0,"time":"2025-05-06T11:38:14.009903+08:00","trace_id":"8a40236f-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:38:14.01064+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 190, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:38:14.038973+08:00","trace_id":"8a40236f-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:190 - product_id1","time":"2025-05-06T11:38:14.06119+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"0.1.16\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"OPPO PKC110\"  device_brand:\"OPPO Find X8 Pro\"  os:\"Android OS 15 / API-35 (AP3A.240617.008/V.1d3ec7b-d845-34b32)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2376X1080\"  device_name:\"OPPO Find X8 Pro\"  device_code:\"63ee85fa2150d7f439222c30b0f69c60\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_ANDROID  account_info:{account:\"Jk333666\"  password:\"Jk333666\"}]","player_id":0,"productID":0,"time":"2025-05-06T11:39:10.186809+08:00","trace_id":"abbedd86-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:87","func":"Login","level":"info","msg":"账号登录:Jk333666 - playerId : 66","player_id":0,"productID":0,"time":"2025-05-06T11:39:10.208693+08:00","trace_id":"abbedd86-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:39:10.225264+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_PASSWORD login success - playerId: 66, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:39:10.248192+08:00","trace_id":"abbedd86-2a2b-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:66 - product_id1","time":"2025-05-06T11:39:10.2695+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-GB3Q0LI\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-GB3Q0LI\"  device_code:\"bfe71c711da67372cfe9cd1a5aabdc138f76827e1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:43:38.966818+08:00","trace_id":"4bf34265-2a2c-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 190","player_id":0,"productID":0,"time":"2025-05-06T11:43:38.984897+08:00","trace_id":"4bf34265-2a2c-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:43:38.985718+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 190, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:43:39.019095+08:00","trace_id":"4bf34265-2a2c-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:190 - product_id1","time":"2025-05-06T11:43:39.046171+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:45:10.342585+08:00","trace_id":"82698964-2a2c-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 262","player_id":0,"productID":0,"time":"2025-05-06T11:45:10.364138+08:00","trace_id":"82698964-2a2c-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:45:10.364926+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 262, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:45:10.391581+08:00","trace_id":"82698964-2a2c-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:262 - product_id1","time":"2025-05-06T11:45:10.417913+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:46:23.365916+08:00","trace_id":"adf09f67-2a2c-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 262","player_id":0,"productID":0,"time":"2025-05-06T11:46:23.385403+08:00","trace_id":"adf09f67-2a2c-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:46:23.386056+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 262, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:46:23.416351+08:00","trace_id":"adf09f67-2a2c-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:262 - product_id1","time":"2025-05-06T11:46:23.440192+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:22  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:3408}","time":"2025-05-06T11:53:03.502812+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:22","time":"2025-05-06T11:53:03.50399+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-VAKR0QU\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-VAKR0QU\"  device_code:\"fa8d29a13a7c730d60ca79b83d6dc6ec6aeab0171\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:53:28.965516+08:00","trace_id":"ab9dd12b-2a2d-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 22","player_id":0,"productID":0,"time":"2025-05-06T11:53:28.981935+08:00","trace_id":"ab9dd12b-2a2d-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:53:28.98269+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 22, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:53:29.013371+08:00","trace_id":"ab9dd12b-2a2d-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:22 - product_id1","time":"2025-05-06T11:53:29.036597+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/proc/login_service.go:50","func":"DeleteAccountReq","level":"info","msg":"delete product:1 account:262 success","player_id":262,"productID":1,"time":"2025-05-06T11:54:53.292658+08:00","trace_id":"dddb9c9b-2a2d-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:262  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:510}","time":"2025-05-06T11:54:53.319787+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:262","time":"2025-05-06T11:54:53.319992+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:54:54.637881+08:00","trace_id":"deae6565-2a2d-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 0","player_id":0,"productID":0,"time":"2025-05-06T11:54:54.65862+08:00","trace_id":"deae6565-2a2d-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:54:54.659322+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 263, isReg: true","player_id":0,"productID":0,"time":"2025-05-06T11:54:54.694617+08:00","trace_id":"deae6565-2a2d-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:263 - product_id1","time":"2025-05-06T11:54:54.724471+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:128","func":"RegisterRecord","level":"debug","msg":"LoginRecord In uid:263 - product_id1","time":"2025-05-06T11:54:54.72446+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"LIXIAODONG\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"3440X1440\"  device_name:\"LIXIAODONG\"  device_code:\"99d107ae01d15e93540c9d251c44ca28e54acdf00\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:56:32.201757+08:00","trace_id":"18d57250-2a2e-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 9","player_id":0,"productID":0,"time":"2025-05-06T11:56:32.220802+08:00","trace_id":"18d57250-2a2e-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:56:32.221464+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 9, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:56:32.241564+08:00","trace_id":"18d57250-2a2e-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:9 - product_id1","time":"2025-05-06T11:56:32.273635+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"0.1.16\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"Xiaomi 2410DPN6CC\"  device_brand:\"Xiaomi 15 Pro\"  os:\"Android OS 15 / API-35 (AQ3A.240812.002/OS2.0.101.0.VOBCNXM)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2400X1080\"  device_name:\"Xiaomi 15 Pro\"  device_code:\"28e5457af474e932ec81f6efbe2b4990\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_ANDROID  account_info:{account:\"13538250023lhX\"  password:\"32167lyX\"}]","player_id":0,"productID":0,"time":"2025-05-06T11:58:58.470024+08:00","trace_id":"70040f20-2a2e-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:87","func":"Login","level":"info","msg":"账号登录:13538250023lhX - playerId : 227","player_id":0,"productID":0,"time":"2025-05-06T11:58:58.487216+08:00","trace_id":"70040f20-2a2e-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:58:58.503183+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_PASSWORD login success - playerId: 227, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T11:58:58.53748+08:00","trace_id":"70040f20-2a2e-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:227 - product_id1","time":"2025-05-06T11:58:58.559233+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:263  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:247}","time":"2025-05-06T11:59:01.237344+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:263","time":"2025-05-06T11:59:01.237537+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T11:59:02.46462+08:00","trace_id":"72653f55-2a2e-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 0","player_id":0,"productID":0,"time":"2025-05-06T11:59:02.483462+08:00","trace_id":"72653f55-2a2e-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T11:59:02.48398+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 264, isReg: true","player_id":0,"productID":0,"time":"2025-05-06T11:59:02.518447+08:00","trace_id":"72653f55-2a2e-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:128","func":"RegisterRecord","level":"debug","msg":"LoginRecord In uid:264 - product_id1","time":"2025-05-06T11:59:02.539996+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:264 - product_id1","time":"2025-05-06T11:59:02.540031+08:00"}
