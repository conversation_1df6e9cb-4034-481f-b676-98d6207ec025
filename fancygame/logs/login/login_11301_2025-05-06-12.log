{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:256  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:1621}","time":"2025-05-06T12:01:57.355343+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:256","time":"2025-05-06T12:01:57.356853+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:03:17.553992+08:00","trace_id":"0a710951-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 264","player_id":0,"productID":0,"time":"2025-05-06T12:03:17.575848+08:00","trace_id":"0a710951-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:03:17.576381+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 264, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:03:17.605028+08:00","trace_id":"0a710951-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:264 - product_id1","time":"2025-05-06T12:03:17.639611+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:03:41.521329+08:00","trace_id":"18ba2c04-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 264","player_id":0,"productID":0,"time":"2025-05-06T12:03:41.538726+08:00","trace_id":"18ba2c04-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:03:41.539392+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 264, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:03:41.565379+08:00","trace_id":"18ba2c04-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:264 - product_id1","time":"2025-05-06T12:03:41.58686+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:264  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:19}","time":"2025-05-06T12:04:00.93424+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:264","time":"2025-05-06T12:04:00.935147+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:04:01.536016+08:00","trace_id":"24a11568-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 264","player_id":0,"productID":0,"time":"2025-05-06T12:04:01.552459+08:00","trace_id":"24a11568-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:04:01.553035+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 264, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:04:01.579926+08:00","trace_id":"24a11568-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:264 - product_id1","time":"2025-05-06T12:04:01.603786+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-VAKR0QU\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-VAKR0QU\"  device_code:\"fa8d29a13a7c730d60ca79b83d6dc6ec6aeab0171\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:04:25.23654+08:00","trace_id":"32c8a744-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 22","player_id":0,"productID":0,"time":"2025-05-06T12:04:25.253232+08:00","trace_id":"32c8a744-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:04:25.254007+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 22, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:04:25.279838+08:00","trace_id":"32c8a744-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:22 - product_id1","time":"2025-05-06T12:04:25.301069+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:04:36.335547+08:00","trace_id":"39663db9-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 264","player_id":0,"productID":0,"time":"2025-05-06T12:04:36.357965+08:00","trace_id":"39663db9-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:04:36.358628+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 264, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:04:36.38984+08:00","trace_id":"39663db9-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:264 - product_id1","time":"2025-05-06T12:04:36.416445+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"LIXIAODONG\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"3440X1440\"  device_name:\"LIXIAODONG\"  device_code:\"99d107ae01d15e93540c9d251c44ca28e54acdf00\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:05:02.055753+08:00","trace_id":"48bab80a-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 9","player_id":0,"productID":0,"time":"2025-05-06T12:05:02.075017+08:00","trace_id":"48bab80a-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:05:02.075918+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 9, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:05:02.104015+08:00","trace_id":"48bab80a-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:9 - product_id1","time":"2025-05-06T12:05:02.125978+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-GB3Q0LI\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-GB3Q0LI\"  device_code:\"bfe71c711da67372cfe9cd1a5aabdc138f76827e1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:05:13.155527+08:00","trace_id":"4f58720a-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 190","player_id":0,"productID":0,"time":"2025-05-06T12:05:13.172936+08:00","trace_id":"4f58720a-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:05:13.173769+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 190, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:05:13.20125+08:00","trace_id":"4f58720a-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:190 - product_id1","time":"2025-05-06T12:05:13.22561+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:06:46.976234+08:00","trace_id":"87440d39-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 264","player_id":0,"productID":0,"time":"2025-05-06T12:06:46.994192+08:00","trace_id":"87440d39-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:06:46.995527+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 264, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:06:47.024326+08:00","trace_id":"87440d39-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:264 - product_id1","time":"2025-05-06T12:06:47.04951+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-VAKR0QU\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-VAKR0QU\"  device_code:\"fa8d29a13a7c730d60ca79b83d6dc6ec6aeab0171\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:07:07.803731+08:00","trace_id":"939df683-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 22","player_id":0,"productID":0,"time":"2025-05-06T12:07:07.822278+08:00","trace_id":"939df683-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:07:07.82286+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 22, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:07:07.847794+08:00","trace_id":"939df683-2a2f-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:22 - product_id1","time":"2025-05-06T12:07:07.869697+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:9  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:269}","time":"2025-05-06T12:09:30.146026+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:9","time":"2025-05-06T12:09:30.147188+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"LIXIAODONG\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"3440X1440\"  device_name:\"LIXIAODONG\"  device_code:\"99d107ae01d15e93540c9d251c44ca28e54acdf00\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:10:29.386904+08:00","trace_id":"0bd566ca-2a30-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 9","player_id":0,"productID":0,"time":"2025-05-06T12:10:29.406121+08:00","trace_id":"0bd566ca-2a30-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:10:29.406715+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 9, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:10:29.439304+08:00","trace_id":"0bd566ca-2a30-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:9 - product_id1","time":"2025-05-06T12:10:29.461328+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:190  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:556}","time":"2025-05-06T12:14:29.958862+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:190","time":"2025-05-06T12:14:29.959981+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-N8380UN\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-N8380UN\"  device_code:\"a28e11d0d26ef9c80538bc67ecc94fbdd852486c0\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:15:41.899777+08:00","trace_id":"c61b059d-2a30-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 1","player_id":0,"productID":0,"time":"2025-05-06T12:15:41.920987+08:00","trace_id":"c61b059d-2a30-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:15:41.921911+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 1, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:15:41.949583+08:00","trace_id":"c61b059d-2a30-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:1 - product_id1","time":"2025-05-06T12:15:41.970737+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-VAKR0QU\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-VAKR0QU\"  device_code:\"fa8d29a13a7c730d60ca79b83d6dc6ec6aeab0171\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:17:24.865634+08:00","trace_id":"037a52a1-2a31-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 22","player_id":0,"productID":0,"time":"2025-05-06T12:17:24.884418+08:00","trace_id":"037a52a1-2a31-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:17:24.88512+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 22, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:17:24.908045+08:00","trace_id":"037a52a1-2a31-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:22 - product_id1","time":"2025-05-06T12:17:24.929156+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:9  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:633}","time":"2025-05-06T12:21:02.24935+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:9","time":"2025-05-06T12:21:02.250771+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-VAKR0QU\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-VAKR0QU\"  device_code:\"fa8d29a13a7c730d60ca79b83d6dc6ec6aeab0171\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:21:05.318221+08:00","trace_id":"86dd536a-2a31-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 22","player_id":0,"productID":0,"time":"2025-05-06T12:21:05.333953+08:00","trace_id":"86dd536a-2a31-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/location/ip.go:43","func":"GetLocationByIP","level":"error","msg":"Error opening GeoLite2-City database: open ../GeoLite2-City.mmdb: no such file or directory current path: /Users/<USER>/go/src/fancygame/loginsrv/cmd geoPath: ../GeoLite2-City.mmdb","time":"2025-05-06T12:21:05.334519+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 22, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:21:05.370241+08:00","trace_id":"86dd536a-2a31-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:22 - product_id1","time":"2025-05-06T12:21:05.39409+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-VAKR0QU\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-VAKR0QU\"  device_code:\"fa8d29a13a7c730d60ca79b83d6dc6ec6aeab0171\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\\n\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T12:22:37.068455+08:00","trace_id":"bd76e80b-2a31-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:65","func":"Login","level":"info","msg":"游客登录 - playerID : 22","player_id":0,"productID":0,"time":"2025-05-06T12:22:37.087864+08:00","trace_id":"bd76e80b-2a31-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login.go:145","func":"DefaultLoginProcess","level":"info","msg":"LT_VISITOR login success - playerId: 22, isReg: false","player_id":0,"productID":0,"time":"2025-05-06T12:22:37.114575+08:00","trace_id":"bd76e80b-2a31-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:74","func":"LoginRecord","level":"debug","msg":"LoginRecord In uid:22 - product_id1","time":"2025-05-06T12:22:37.13516+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:153","func":"populateEndpoints","level":"info","msg":"[Consul resolver] Watch has been finished","time":"2025-05-06T12:52:37.141201+08:00"}
