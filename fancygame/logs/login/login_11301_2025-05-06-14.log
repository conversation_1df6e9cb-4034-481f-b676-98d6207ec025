{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:6  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:22}","time":"2025-05-06T14:13:04.980743+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:6","time":"2025-05-06T14:13:04.986232+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:21  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:661}","time":"2025-05-06T14:25:02.959332+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:21","time":"2025-05-06T14:25:02.963239+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-N8380UN\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-N8380UN\"  device_code:\"a28e11d0d26ef9c80538bc67ecc94fbdd852486c0\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:31:20.909871+08:00","trace_id":"b95e70e3-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:31:30.931077+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-GB3Q0LI\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-GB3Q0LI\"  device_code:\"bfe71c711da67372cfe9cd1a5aabdc138f76827e1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:31:31.587857+08:00","trace_id":"bfbcbf8e-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:31:35.944659+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-GB3Q0LI\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-GB3Q0LI\"  device_code:\"bfe71c711da67372cfe9cd1a5aabdc138f76827e1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:31:38.760163+08:00","trace_id":"c4030bfe-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:31:40.95868+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-N8380UN\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-N8380UN\"  device_code:\"a28e11d0d26ef9c80538bc67ecc94fbdd852486c0\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:31:42.276488+08:00","trace_id":"c61bb0dd-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:31:45.99177+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-6NL19QH\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-6NL19QH\"  device_code:\"meijun1006\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:31:48.346024+08:00","trace_id":"c9b9d455-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:31:51.044148+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:31:56.060854+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-N8380UN\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-N8380UN\"  device_code:\"a28e11d0d26ef9c80538bc67ecc94fbdd852486c0\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:31:56.815708+08:00","trace_id":"cec62cde-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:32:01.125532+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-N8380UN\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-N8380UN\"  device_code:\"a28e11d0d26ef9c80538bc67ecc94fbdd852486c0\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:32:03.46107+08:00","trace_id":"d2bc2133-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:32:06.659081+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:32:12.171302+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:32:18.175539+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-6NL19QH\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-6NL19QH\"  device_code:\"meijun1006\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:32:21.025195+08:00","trace_id":"dd34500b-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:32:24.178856+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:32:30.183179+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-HGTU3P5\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-HGTU3P5\"  device_code:\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY  account_info:{account:\"jjy222\"  password:\"jjY222\"}]","player_id":0,"productID":0,"time":"2025-05-06T14:32:34.1744+08:00","trace_id":"e50ac43b-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:32:36.187562+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:32:42.190578+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-GB3Q0LI\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-GB3Q0LI\"  device_code:\"bfe71c711da67372cfe9cd1a5aabdc138f76827e1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:32:45.831297+08:00","trace_id":"ebfd8623-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:32:48.194068+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:32:54.197834+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"LIXIAODONG\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"3440X1440\"  device_name:\"LIXIAODONG\"  device_code:\"99d107ae01d15e93540c9d251c44ca28e54acdf00\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:32:59.381189+08:00","trace_id":"f410e6b2-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:33:00.201872+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:33:06.205777+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"0.1.16\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"Samsung SM-S9080\"  device_brand:\"SM-S9080\"  os:\"Android OS 12 / API-32 (V417IR/276)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"SM-S9080\"  device_code:\"c0b1fbfad33fc974e55f09a8bb8fa09a\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_ANDROID  account_info:{account:\"Liang123\"  password:\"Liang123\"}]","player_id":0,"productID":0,"time":"2025-05-06T14:33:09.142905+08:00","trace_id":"f9e28f41-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:33:12.210006+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"0.1.16\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"Samsung SM-S9080\"  device_brand:\"SM-S9080\"  os:\"Android OS 12 / API-32 (V417IR/276)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"SM-S9080\"  device_code:\"c0b1fbfad33fc974e55f09a8bb8fa09a\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_ANDROID  account_info:{account:\"Liang123\"  password:\"Liang123\"}]","player_id":0,"productID":0,"time":"2025-05-06T14:33:16.408401+08:00","trace_id":"fe372c72-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:33:18.214632+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"0.1.16\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"Samsung SM-S9080\"  device_brand:\"SM-S9080\"  os:\"Android OS 12 / API-32 (V417IR/276)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"SM-S9080\"  device_code:\"c0b1fbfad33fc974e55f09a8bb8fa09a\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_ANDROID  account_info:{account:\"Liang123\"  password:\"Liang123\"}]","player_id":0,"productID":0,"time":"2025-05-06T14:33:23.607127+08:00","trace_id":"********-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:33:24.219317+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:33:30.222794+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"0.1.16\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"Samsung SM-S9080\"  device_brand:\"SM-S9080\"  os:\"Android OS 12 / API-32 (V417IR/276)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"SM-S9080\"  device_code:\"c0b1fbfad33fc974e55f09a8bb8fa09a\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_ANDROID  account_info:{account:\"Liang123\"  password:\"Liang123\"}]","player_id":0,"productID":0,"time":"2025-05-06T14:33:30.851023+08:00","trace_id":"06d2e4ec-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:33:36.226824+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-GB3Q0LI\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-GB3Q0LI\"  device_code:\"bfe71c711da67372cfe9cd1a5aabdc138f76827e1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:33:36.788433+08:00","trace_id":"0a5ce601-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf652\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:33:40.110096+08:00","trace_id":"0c57c867-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:33:42.231122+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:33:48.235127+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-6NL19QH\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-6NL19QH\"  device_code:\"meijun1006\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:33:48.679683+08:00","trace_id":"115f33ef-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:33:54.239838+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-6NL19QH\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-6NL19QH\"  device_code:\"meijun1006\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:33:56.674635+08:00","trace_id":"16375f8a-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:34:00.244619+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-6NL19QH\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-6NL19QH\"  device_code:\"meijun1006\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:34:03.717462+08:00","trace_id":"1a69fa68-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:34:06.24858+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:34:12.252622+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:34:18.256833+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:34:24.260861+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:34:30.263672+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:34:36.267091+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"0.1.16\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"Samsung SM-S9080\"  device_brand:\"SM-S9080\"  os:\"Android OS 12 / API-32 (V417IR/276)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"SM-S9080\"  device_code:\"c0b1fbfad33fc974e55f09a8bb8fa09a\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_ANDROID  account_info:{account:\"Liang123\"  password:\"Liang123\"}]","player_id":0,"productID":0,"time":"2025-05-06T14:34:36.277877+08:00","trace_id":"2dd24368-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:34:42.269478+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:34:48.273796+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:34:54.278248+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:35:00.283047+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:35:06.288594+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:35:12.298634+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:35:18.305532+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:35:24.312072+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:35:30.317805+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:35:36.323518+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:35:42.331822+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:35:48.337243+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:35:54.341424+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:36:00.346248+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:36:06.34915+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:36:12.352817+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:36:18.355589+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:36:24.360076+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:36:30.364657+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:36:36.370747+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:36:42.374596+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:36:48.379682+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:36:54.383546+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:37:00.388018+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:37:06.392302+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:37:12.397221+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"HUAWEI YAL-AL50\"  device_brand:\"HONOR 20S\"  os:\"Android OS 10 / API-29 (HUAWEIYAL-AL50/103.0.0.165C00)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"1560X720\"  device_name:\"HONOR 20S\"  device_code:\"0accc6ba0b21e6657e134ecd91b9cee8\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_ANDROID  account_info:{account:\"qwe112233\"  password:\"Aa123456\"}]","player_id":0,"productID":0,"time":"2025-05-06T14:37:15.946329+08:00","trace_id":"8cfad873-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:37:18.401494+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:37:24.406023+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:37:30.410007+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:37:36.413715+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:37:42.417629+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:37:48.422002+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:37:54.426728+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:38:00.430556+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:38:06.433848+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:38:12.437578+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:38:18.441852+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:38:24.445887+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:38:30.44932+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:38:36.453079+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_PASSWORD  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"HUAWEI YAL-AL50\"  device_brand:\"HONOR 20S\"  os:\"Android OS 10 / API-29 (HUAWEIYAL-AL50/103.0.0.165C00)\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"1560X720\"  device_name:\"HONOR 20S\"  device_code:\"0accc6ba0b21e6657e134ecd91b9cee8\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_ANDROID  account_info:{account:\"qwe112233\"  password:\"Aa123456\"}]","player_id":0,"productID":0,"time":"2025-05-06T14:38:37.277468+08:00","trace_id":"bd751420-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:38:42.458127+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:38:48.462113+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:38:54.467298+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:39:00.471136+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:39:06.474229+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:39:12.477915+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:39:18.481455+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:39:24.485882+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:39:30.489339+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:39:36.493429+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:39:42.496744+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:39:48.50059+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:39:54.504968+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:40:00.509218+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:40:06.513411+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:40:25.572801+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:40:31.577596+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:40:37.581245+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:40:43.585705+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:40:49.589719+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:40:55.593761+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-GB3Q0LI\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-GB3Q0LI\"  device_code:\"bfe71c711da67372cfe9cd1a5aabdc138f76827e1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:44:42.901801+08:00","trace_id":"9762bd87-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-6NL19QH\"  os:\"Windows 11  (10.0.22631) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-6NL19QH\"  device_code:\"meijun1006\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:45:41.338522+08:00","trace_id":"ba378004-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:45:58.636913+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"WYH\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"WYH\"  device_code:\"9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:45:59.128125+08:00","trace_id":"c4d20b19-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:46:04.641751+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:46:10.645434+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:46:16.650058+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:46:22.654836+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-F3EE70T\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"3840X2160\"  device_name:\"DESKTOP-F3EE70T\"  device_code:\"1ac8899de878bc1fcd9a65c1e548154fb171450f1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:46:24.025433+08:00","trace_id":"d3a8e134-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:46:28.658159+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:46:34.662595+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:46:40.667323+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:46:46.671223+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:46:52.674958+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:46:58.679368+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:47:04.683267+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:47:10.686683+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:47:16.691036+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:47:22.695066+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:47:28.699143+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:47:34.702707+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:47:40.705973+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:47:46.709825+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:47:52.713961+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:47:58.717039+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:48:04.722257+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:48:10.725569+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:48:16.730208+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:48:22.733249+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:48:28.73674+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:48:34.74074+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:48:40.745687+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-F3EE70T\"  os:\"Windows 11  (10.0.26100) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"3840X2160\"  device_name:\"DESKTOP-F3EE70T\"  device_code:\"1ac8899de878bc1fcd9a65c1e548154fb171450f1\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:48:42.533077+08:00","trace_id":"2637acec-2a46-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:48:46.750228+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:48:52.754053+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:48:58.758242+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:04.763303+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:10.76751+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:16.770738+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:22.774908+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:28.778338+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:34.782677+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:40.786925+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:46.791307+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:52.794337+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:53.797675+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:54.852119+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:55.856025+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:56.859042+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:57.86228+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:58.863588+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:49:59.866032+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:00.868883+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:01.873173+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:02.875256+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:03.876799+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:04.880772+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:05.884589+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:06.888245+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:07.89084+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:08.895489+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:09.897723+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:15.901566+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:16.904971+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:17.925337+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:18.928815+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:19.931555+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:20.934559+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:21.937865+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:22.948816+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:23.951592+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:24.954868+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:25.957834+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:26.960755+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:27.963902+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:28.966951+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:29.969825+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:30.972114+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:31.975106+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:32.978433+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:33.981685+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:34.983855+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:40.988351+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:41.992044+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:42.99539+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:43.99866+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:45.001519+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:46.004685+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:47.007734+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:48.011082+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:49.014692+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:50.017971+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:51.02103+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:52.024694+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:53.02749+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:54.030689+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:55.034411+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:56.036483+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:57.0399+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:58.046272+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:50:59.049111+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:05.052081+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:06.056054+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:07.059554+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:08.062371+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:09.064856+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:10.067123+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:11.071066+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:12.073275+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:13.075969+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:14.077352+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:15.080599+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:16.083643+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:17.08788+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:18.09061+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:19.094879+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:20.098116+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:21.100592+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:22.103448+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:23.11752+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:24.123027+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:30.1257+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:31.130333+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:32.13219+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:33.134394+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:34.13743+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:35.140736+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:36.143258+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:37.146457+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:38.150237+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:39.152989+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:40.156168+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:41.159478+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:42.16239+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:43.165843+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:44.169322+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:45.172538+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:46.175786+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:47.178065+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:48.180844+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:49.184585+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:55.18897+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:56.193252+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:57.194958+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:58.197333+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:51:59.201022+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:00.204462+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:01.208525+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:02.211641+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:03.215085+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:04.218582+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:05.221677+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:06.225189+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:07.228079+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:08.231118+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:09.234658+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:10.236984+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:11.240652+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:12.244095+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:13.247113+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:14.250721+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:20.256806+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:21.260639+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:22.263488+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:23.266531+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:24.269445+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:25.272941+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:26.276225+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:27.278719+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:28.281788+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:29.285258+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:30.288065+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:31.29084+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:32.293374+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:33.295782+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:34.300285+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:35.302452+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:36.304144+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:37.307558+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:38.310512+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:39.31304+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:45.316151+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:46.320128+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:47.322763+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:48.32639+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:49.329923+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:50.332989+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:51.336941+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:52.339901+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:53.34236+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:54.345675+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:55.348919+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:56.352356+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:57.354619+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:58.357567+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:52:59.360569+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:00.363082+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:01.366167+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:02.369905+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:03.373027+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:04.375627+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/services/login.go:43","func":"Login","level":"info","msg":"登录请求[LoginReq: login_type:LT_VISITOR  client_version:\"1.0.1\"  product_id:1  channel_id:CT_MASTER  device_info:{device_model:\"System Product Name (ASUS)\"  device_brand:\"DESKTOP-VAKR0QU\"  os:\"Windows 10  (10.0.19045) 64bit\"  os_language:\"ChineseSimplified\"  app_language:LT_ZH_CN  resolution:\"2560X1440\"  device_name:\"DESKTOP-VAKR0QU\"  device_code:\"fa8d29a13a7c730d60ca79b83d6dc6ec6aeab0171\"}  network:NT_LOCAL  bundle_name:\"com.QiHuan.FishingGame\"  ip:\"*************\"  platform:PT_UNITY]","player_id":0,"productID":0,"time":"2025-05-06T14:53:08.002916+08:00","trace_id":"c472f5fc-2a46-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:10.379199+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:11.382013+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:12.413642+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:13.418288+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:14.421442+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:15.424272+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:16.427358+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:17.429509+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:18.43286+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:19.436389+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:20.440042+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:21.443233+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:22.448823+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:23.453763+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:24.456812+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:25.458961+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:26.462123+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:27.46551+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:28.468558+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:29.471226+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:35.475341+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:36.476986+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:37.4793+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:38.481154+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:39.497511+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:40.499564+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:41.508324+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:42.509966+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:43.513443+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:44.516714+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:45.518967+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:46.522688+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:47.525251+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:48.528811+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:49.531304+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:50.534674+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:51.537518+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:52.540525+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:53.543333+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:53:54.546073+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:00.549778+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:01.552757+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:02.555412+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:03.558093+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:04.560486+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:05.563745+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:06.566506+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:07.570037+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:08.573897+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:09.577142+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:10.580757+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:11.584797+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:12.58819+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:13.590806+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:14.594649+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:15.596313+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:16.599526+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:17.602309+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:18.605101+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:19.608408+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:25.612811+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:26.616661+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:27.619834+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:28.623396+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:29.626404+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:30.629324+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:31.631527+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:32.635085+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:33.638288+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:34.640645+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:35.644436+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:36.64708+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:37.649882+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:38.653443+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:39.655681+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:40.658142+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:41.661849+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:42.664737+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:43.668457+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:44.672302+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:50.675558+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:51.682423+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:52.68515+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:53.687771+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:54.690917+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:55.693835+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:56.696235+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:57.70031+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:58.7035+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:54:59.706303+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:00.709334+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:01.712332+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:02.715178+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:03.71845+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:04.721817+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:05.725915+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:06.729214+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:07.731738+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:08.734392+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:09.741829+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:11.781493+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:12.788059+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:13.79824+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:14.804507+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:15.81022+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:16.81754+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:17.824501+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:18.830218+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:19.838256+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:20.843689+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/grpc_resolver_consul/consul.go:82","func":"func1","level":"error","msg":"[Consul resolver] Couldn't fetch endpoints. target={service='user' healthy='false' tag=''}","time":"2025-05-06T14:55:21.849773+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993113+08:00","trace_id":"c4d20b19-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.99363+08:00","trace_id":"0a5ce601-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败bfe71c711da67372cfe9cd1a5aabdc138f76827e1","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993868+08:00","trace_id":"0a5ce601-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993698+08:00","trace_id":"cec62cde-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败a28e11d0d26ef9c80538bc67ecc94fbdd852486c0","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.994128+08:00","trace_id":"cec62cde-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993192+08:00","trace_id":"bfbcbf8e-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败bfe71c711da67372cfe9cd1a5aabdc138f76827e1","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.994343+08:00","trace_id":"bfbcbf8e-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.992937+08:00","trace_id":"c472f5fc-2a46-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993148+08:00","trace_id":"115f33ef-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993134+08:00","trace_id":"1a69fa68-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993189+08:00","trace_id":"b95e70e3-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993471+08:00","trace_id":"c61bb0dd-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993518+08:00","trace_id":"c4030bfe-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败bfe71c711da67372cfe9cd1a5aabdc138f76827e1","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.995176+08:00","trace_id":"c4030bfe-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败fa8d29a13a7c730d60ca79b83d6dc6ec6aeab0171","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.994829+08:00","trace_id":"c472f5fc-2a46-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:108","func":"RpcGetPlayerIdByAccount","level":"error","msg":"rpc get player id by account:Liang123 failed : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993297+08:00","trace_id":"f9e28f41-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993282+08:00","trace_id":"d3a8e134-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败1ac8899de878bc1fcd9a65c1e548154fb171450f1","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.995704+08:00","trace_id":"d3a8e134-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:81","func":"Login","level":"error","msg":"RPC query account:account:\"Liang123\"  password:\"f6e331b9a5b1eb79994c4a84922119f3487380d152e025c06002a8b3032ff808\" err:rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.99578+08:00","trace_id":"f9e28f41-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993267+08:00","trace_id":"16375f8a-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败meijun1006","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.996043+08:00","trace_id":"16375f8a-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993071+08:00","trace_id":"d2bc2133-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败a28e11d0d26ef9c80538bc67ecc94fbdd852486c0","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.996182+08:00","trace_id":"d2bc2133-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.99295+08:00","trace_id":"c9b9d455-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败meijun1006","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.996299+08:00","trace_id":"c9b9d455-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.992996+08:00","trace_id":"f410e6b2-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.992936+08:00","trace_id":"ba378004-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993061+08:00","trace_id":"ebfd8623-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993101+08:00","trace_id":"dd34500b-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.9935+08:00","trace_id":"0c57c867-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败9442572fa0a1abc40d3c316f6fbabe0ee8a6cf652","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.99661+08:00","trace_id":"0c57c867-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败99d107ae01d15e93540c9d251c44ca28e54acdf00","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.996459+08:00","trace_id":"f410e6b2-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败a28e11d0d26ef9c80538bc67ecc94fbdd852486c0","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.995004+08:00","trace_id":"b95e70e3-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败9442572fa0a1abc40d3c316f6fbabe0ee8a6cf653","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993842+08:00","trace_id":"c4d20b19-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:108","func":"RpcGetPlayerIdByAccount","level":"error","msg":"rpc get player id by account:jjy222 failed : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993326+08:00","trace_id":"e50ac43b-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:108","func":"RpcGetPlayerIdByAccount","level":"error","msg":"rpc get player id by account:Liang123 failed : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993348+08:00","trace_id":"fe372c72-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:81","func":"Login","level":"error","msg":"RPC query account:account:\"Liang123\"  password:\"f6e331b9a5b1eb79994c4a84922119f3487380d152e025c06002a8b3032ff808\" err:rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.998174+08:00","trace_id":"fe372c72-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:108","func":"RpcGetPlayerIdByAccount","level":"error","msg":"rpc get player id by account:qwe112233 failed : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993338+08:00","trace_id":"bd751420-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:108","func":"RpcGetPlayerIdByAccount","level":"error","msg":"rpc get player id by account:Liang123 failed : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993405+08:00","trace_id":"2dd24368-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993423+08:00","trace_id":"2637acec-2a46-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:108","func":"RpcGetPlayerIdByAccount","level":"error","msg":"rpc get player id by account:qwe112233 failed : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993358+08:00","trace_id":"8cfad873-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:40","func":"RpcGetUserInfoByDeviceCode","level":"error","msg":"rpc get err : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993739+08:00","trace_id":"9762bd87-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:108","func":"RpcGetPlayerIdByAccount","level":"error","msg":"rpc get player id by account:Liang123 failed : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993314+08:00","trace_id":"********-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/rpc_user/rpc_usersrv.go:108","func":"RpcGetPlayerIdByAccount","level":"error","msg":"rpc get player id by account:Liang123 failed : rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.993322+08:00","trace_id":"06d2e4ec-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:81","func":"Login","level":"error","msg":"RPC query account:account:\"jjy222\"  password:\"8ece653b87208b3c1b64d752dba58ac963a31393ea20a5650bcaa9f9451d739e\" err:rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.997992+08:00","trace_id":"e50ac43b-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:81","func":"Login","level":"error","msg":"RPC query account:account:\"Liang123\"  password:\"f6e331b9a5b1eb79994c4a84922119f3487380d152e025c06002a8b3032ff808\" err:rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.998835+08:00","trace_id":"********-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:81","func":"Login","level":"error","msg":"RPC query account:account:\"Liang123\"  password:\"f6e331b9a5b1eb79994c4a84922119f3487380d152e025c06002a8b3032ff808\" err:rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.998943+08:00","trace_id":"06d2e4ec-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:81","func":"Login","level":"error","msg":"RPC query account:account:\"qwe112233\"  password:\"c4318372f98f4c46ed3a32c16ee4d7a76c832886d887631c0294b3314f34edf1\" err:rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.998503+08:00","trace_id":"bd751420-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败meijun1006","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.996484+08:00","trace_id":"ba378004-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败a28e11d0d26ef9c80538bc67ecc94fbdd852486c0","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.995096+08:00","trace_id":"c61bb0dd-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败meijun1006","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.995085+08:00","trace_id":"1a69fa68-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败meijun1006","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.995081+08:00","trace_id":"115f33ef-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:81","func":"Login","level":"error","msg":"RPC query account:account:\"Liang123\"  password:\"f6e331b9a5b1eb79994c4a84922119f3487380d152e025c06002a8b3032ff808\" err:rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.998537+08:00","trace_id":"2dd24368-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败1ac8899de878bc1fcd9a65c1e548154fb171450f1","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.998543+08:00","trace_id":"2637acec-2a46-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败bfe71c711da67372cfe9cd1a5aabdc138f76827e1","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.996485+08:00","trace_id":"ebfd8623-2a43-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_account_impl.go:81","func":"Login","level":"error","msg":"RPC query account:account:\"qwe112233\"  password:\"c4318372f98f4c46ed3a32c16ee4d7a76c832886d887631c0294b3314f34edf1\" err:rpc error: code = Unavailable desc = no children to pick from","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.998603+08:00","trace_id":"8cfad873-2a44-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败bfe71c711da67372cfe9cd1a5aabdc138f76827e1","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.998608+08:00","trace_id":"9762bd87-2a45-11f0-918c-0800273ab96d"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/logic/login_visitor_impl.go:59","func":"Login","level":"error","msg":"RPC获取游客态玩家数据失败meijun1006","player_id":0,"productID":0,"time":"2025-05-06T14:55:31.996574+08:00","trace_id":"dd34500b-2a43-11f0-918c-0800273ab96d"}
