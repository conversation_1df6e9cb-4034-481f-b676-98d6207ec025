{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/login","file_name":"login_11301","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-07T18:30:41.025608+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {login} run beginning...","time":"2025-05-07T18:30:41.026233+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-07T18:30:41.026253+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-07T18:30:41.026268+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/login","time":"2025-05-07T18:30:41.026284+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/cmd/main.go:28","func":"Init","level":"info","msg":"login服务Init","time":"2025-05-07T18:30:41.026298+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/location_block","time":"2025-05-07T18:30:41.396015+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/real_name_auth","time":"2025-05-07T18:30:41.738597+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/block_strategy","time":"2025-05-07T18:30:42.107656+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/white_list","time":"2025-05-07T18:30:42.501657+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-07T18:30:42.501707+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-07T18:30:42.501879+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-07T18:30:42.501913+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_login initial word success config= {Broker:[************:9092] GroupID: Topic:r_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-07T18:30:42.501943+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic r_register initial word success config= {Broker:[************:9092] GroupID: Topic:r_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-07T18:30:42.501971+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: consumer_channel_login_broker","time":"2025-05-07T18:30:42.521376+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-05-07T18:30:42.537914+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[login]服务初始化完成","time":"2025-05-07T18:30:42.537974+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21301","time":"2025-05-07T18:30:42.53813+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-05-07T18:30:42.538149+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/cmd/main.go:58","func":"Start","level":"info","msg":"login服务启动成功","time":"2025-05-07T18:30:42.538243+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to 192.168.1.58:8500\n","time":"2025-05-07T18:30:42.538524+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : 192.168.1.58:8500","time":"2025-05-07T18:30:42.557775+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000a929a0}","time":"2025-05-07T18:30:42.557859+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(login)注册完成","time":"2025-05-07T18:30:42.557921+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:31  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:11}  str_data:{key:4103  value:\"2001,2105,3601,3601,3205,2017,2023,2015,2001,2007,2045,2053,2053,3205,3501,3513,3501,3513,2097,2017\"}","time":"2025-05-07T18:31:20.301834+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:31","time":"2025-05-07T18:31:20.302741+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:65  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:161}","time":"2025-05-07T18:34:02.995268+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:65","time":"2025-05-07T18:34:02.996867+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:65  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:138}","time":"2025-05-07T18:36:39.728707+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:65","time":"2025-05-07T18:36:39.72989+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:63  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:771}","time":"2025-05-07T18:48:11.433169+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:63","time":"2025-05-07T18:48:11.434719+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:61  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:245}","time":"2025-05-07T18:51:28.853561+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:61","time":"2025-05-07T18:51:28.855115+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:61  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:83}","time":"2025-05-07T18:53:05.609549+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:61","time":"2025-05-07T18:53:05.610326+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:61  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:30}","time":"2025-05-07T18:53:35.357485+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:61","time":"2025-05-07T18:53:35.358589+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:65  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:1235}","time":"2025-05-07T18:57:32.057874+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:65","time":"2025-05-07T18:57:32.059304+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/pubsub/sub.go:33","func":"HandleLogoutEvent","level":"info","msg":"HandleLoginEvent:player_id:61  product_id:1  channel_id:1001  event_type:ET_LOGOUT  int_data:{key:4101  value:286}","time":"2025-05-07T18:58:21.432325+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/loginsrv/internal/repo/record/log.go:190","func":"LogoutRecord","level":"debug","msg":"LogoutRecord In uid:61","time":"2025-05-07T18:58:21.433989+08:00"}
