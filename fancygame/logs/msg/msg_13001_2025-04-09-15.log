{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/msg","file_name":"msg_13001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-04-09T15:22:16.211322+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {msg} run beginning...","time":"2025-04-09T15:22:16.211982+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-04-09T15:22:16.212+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-04-09T15:22:16.212011+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/msg","time":"2025-04-09T15:22:16.212022+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/msgsrv/cmd/main.go:24","func":"Init","level":"info","msg":"msg服务Init","time":"2025-04-09T15:22:16.212034+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[msg]服务初始化完成","time":"2025-04-09T15:22:16.21208+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/msgsrv/cmd/main.go:35","func":"Start","level":"info","msg":"msg服务启动成功","time":"2025-04-09T15:22:16.212174+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:23001","time":"2025-04-09T15:22:16.212248+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to 192.168.1.58:8500\n","time":"2025-04-09T15:22:16.212595+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : 192.168.1.58:8500","time":"2025-04-09T15:22:16.228144+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140003be2c0}","time":"2025-04-09T15:22:16.228198+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(msg)注册完成","time":"2025-04-09T15:22:16.228223+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/msg","file_name":"msg_13001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-04-09T15:28:02.945495+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {msg} run beginning...","time":"2025-04-09T15:28:02.946703+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-04-09T15:28:02.946733+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-04-09T15:28:02.946747+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/msg","time":"2025-04-09T15:28:02.946763+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/msgsrv/cmd/main.go:24","func":"Init","level":"info","msg":"msg服务Init","time":"2025-04-09T15:28:02.946777+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[msg]服务初始化完成","time":"2025-04-09T15:28:02.946847+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:23001","time":"2025-04-09T15:28:02.947026+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/msgsrv/cmd/main.go:35","func":"Start","level":"info","msg":"msg服务启动成功","time":"2025-04-09T15:28:02.946994+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to 192.168.1.58:8500\n","time":"2025-04-09T15:28:02.947591+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : 192.168.1.58:8500","time":"2025-04-09T15:28:02.959783+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400062a000}","time":"2025-04-09T15:28:02.959857+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(msg)注册完成","time":"2025-04-09T15:28:02.959879+08:00"}
