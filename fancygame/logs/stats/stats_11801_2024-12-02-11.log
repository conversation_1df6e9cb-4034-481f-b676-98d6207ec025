{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/stats","file_name":"stats_11801","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2024-12-02T11:42:34.842567+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:92","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {stats} run beginning...","time":"2024-12-02T11:42:34.843111+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:93","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2024-12-02T11:42:34.843168+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2024-12-02T11:42:34.843186+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/stats","time":"2024-12-02T11:42:34.843204+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:24","func":"Init","level":"info","msg":"stats服务Init","time":"2024-12-02T11:42:34.843221+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_ADD, channel: stats","time":"2024-12-02T11:42:34.862055+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:42:34.876342+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_REDUCE, channel: stats","time":"2024-12-02T11:42:34.89144+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:42:34.904122+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_FISH_GET, channel: stats","time":"2024-12-02T11:42:34.930667+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:42:34.94539+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: stats","time":"2024-12-02T11:42:34.96288+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:42:34.97968+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_TRIP_SETTLE, channel: stats","time":"2024-12-02T11:42:34.999372+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:42:35.015242+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:130","func":"Run","level":"info","msg":"[stats]服务初始化完成","time":"2024-12-02T11:42:35.015423+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:35","func":"Start","level":"info","msg":"stats服务启动成功","time":"2024-12-02T11:42:35.015575+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:164","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21801","time":"2024-12-02T11:42:35.015713+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2024-12-02T11:42:35.018797+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2024-12-02T11:42:35.030534+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000784000}","time":"2024-12-02T11:42:35.03084+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(stats)注册完成","time":"2024-12-02T11:42:35.030981+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:3082003}  int_data:{key:1002  value:3}  int_data:{key:1003  value:308}  int_data:{key:1004  value:11}  int_data:{key:1005  value:1}","time":"2024-12-02T11:45:18.557542+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:305101003}  int_data:{key:1002  value:3}  int_data:{key:1003  value:305}  int_data:{key:1004  value:11}  int_data:{key:1005  value:1}","time":"2024-12-02T11:45:18.627951+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:3014001}  int_data:{key:1002  value:3}  int_data:{key:1003  value:301}  int_data:{key:1004  value:11}  int_data:{key:1005  value:1}","time":"2024-12-02T11:45:18.628072+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:3031001}  int_data:{key:1002  value:3}  int_data:{key:1003  value:303}  int_data:{key:1004  value:11}  int_data:{key:1005  value:1}","time":"2024-12-02T11:45:18.628312+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:101}  int_data:{key:1002  value:1}  int_data:{key:1003  value:101}  int_data:{key:1004  value:11}  int_data:{key:1005  value:100000}","time":"2024-12-02T11:45:18.627983+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:102}  int_data:{key:1002  value:1}  int_data:{key:1003  value:102}  int_data:{key:1004  value:11}  int_data:{key:1005  value:300}","time":"2024-12-02T11:45:18.627992+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:3021001}  int_data:{key:1002  value:3}  int_data:{key:1003  value:302}  int_data:{key:1004  value:11}  int_data:{key:1005  value:1}","time":"2024-12-02T11:45:18.628035+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/stats","time":"2024-12-02T11:45:18.762282+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_REDUCE  int_data:{key:1001  value:101}  int_data:{key:1002  value:1}  int_data:{key:1003  value:101}  int_data:{key:1004  value:7}  int_data:{key:1005  value:10}","time":"2024-12-02T11:45:25.98843+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:148","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2024-12-02T11:46:49.037322+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:80","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2024-12-02T11:46:49.046432+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/mysql/mysql.go:95","func":"init","level":"info","msg":"Mysql 配置列表加载完成","time":"2024-12-02T11:46:49.049807+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:104}  int_data:{key:1002  value:1}  int_data:{key:1003  value:104}  int_data:{key:1004  value:8}  int_data:{key:1005  value:414}","time":"2024-12-02T11:46:49.252881+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_FISH_GET  int_data:{key:3001  value:301020000}  int_data:{key:3003  value:101030017}  int_data:{key:3004  value:159}  int_data:{key:3006  value:18}  int_data:{key:3007  value:0}  int_data:{key:3008  value:0}  int_data:{key:3009  value:1}  int_data:{key:3010  value:101020024}","time":"2024-12-02T11:46:49.399675+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_TRIP_SETTLE  int_data:{key:5004  value:306}","time":"2024-12-02T11:47:07.816648+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/operation.go:169","func":"func3","level":"info","msg":"接收到结束信号interrupt，服务将退出","time":"2024-12-02T11:52:12.623849+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:177","func":"Run","level":"info","msg":"服务开始关闭...","time":"2024-12-02T11:52:12.624317+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2024-12-02T11:52:12.633455+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:41","func":"Stop","level":"info","msg":"stats服务关闭中...","time":"2024-12-02T11:52:12.633516+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:234","func":"func2","level":"info","msg":"服务已被反注册，退出健康检查","time":"2024-12-02T11:52:12.633476+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2024-12-02T11:52:12.640763+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:73","func":"Stop","level":"info","msg":"准备停止 nsq 的所有 consumer, 数量: 5","time":"2024-12-02T11:52:12.641382+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:142","func":"func1","level":"info","msg":"关闭 intranet 服务完成","time":"2024-12-02T11:52:12.641397+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:77","func":"Stop","level":"info","msg":"停止 nsq 的所有 consumer 完成","time":"2024-12-02T11:52:12.64159+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:192","func":"Run","level":"info","msg":"server closed...","time":"2024-12-02T11:52:12.641615+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:164","func":"StopLog","level":"info","msg":"log stop","time":"2024-12-02T11:52:12.641629+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/stats","file_name":"stats_11801","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2024-12-02T11:52:21.907967+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:92","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {stats} run beginning...","time":"2024-12-02T11:52:21.908619+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:93","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2024-12-02T11:52:21.908666+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2024-12-02T11:52:21.908682+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/stats","time":"2024-12-02T11:52:21.908701+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:24","func":"Init","level":"info","msg":"stats服务Init","time":"2024-12-02T11:52:21.908718+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_ADD, channel: stats","time":"2024-12-02T11:52:21.928795+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:52:21.942362+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_REDUCE, channel: stats","time":"2024-12-02T11:52:21.956069+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:52:21.970214+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_FISH_GET, channel: stats","time":"2024-12-02T11:52:21.986522+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:52:22.002336+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: stats","time":"2024-12-02T11:52:22.01675+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:52:22.031111+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_TRIP_SETTLE, channel: stats","time":"2024-12-02T11:52:22.049733+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:52:22.065418+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:130","func":"Run","level":"info","msg":"[stats]服务初始化完成","time":"2024-12-02T11:52:22.06547+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:35","func":"Start","level":"info","msg":"stats服务启动成功","time":"2024-12-02T11:52:22.065554+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:164","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21801","time":"2024-12-02T11:52:22.065709+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2024-12-02T11:52:22.067172+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2024-12-02T11:52:22.079116+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400082a000}","time":"2024-12-02T11:52:22.079174+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(stats)注册完成","time":"2024-12-02T11:52:22.079238+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:148","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2024-12-02T11:53:10.561899+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:80","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2024-12-02T11:53:10.58347+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:104}  int_data:{key:1002  value:1}  int_data:{key:1003  value:104}  int_data:{key:1004  value:8}  int_data:{key:1005  value:536}","time":"2024-12-02T11:53:10.652765+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_FISH_GET  int_data:{key:3001  value:301020000}  int_data:{key:3003  value:101030043}  int_data:{key:3004  value:6392}  int_data:{key:3006  value:67}  int_data:{key:3007  value:0}  int_data:{key:3008  value:0}  int_data:{key:3009  value:3}  int_data:{key:3010  value:101020005}","time":"2024-12-02T11:53:14.433049+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/stats","time":"2024-12-02T11:53:15.172168+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/mysql/mysql.go:95","func":"init","level":"info","msg":"Mysql 配置列表加载完成","time":"2024-12-02T11:53:15.17944+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_TRIP_SETTLE  int_data:{key:5004  value:402}","time":"2024-12-02T11:53:25.988301+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_TRIP_SETTLE  int_data:{key:5004  value:402}","time":"2024-12-02T11:57:34.811077+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/stats","file_name":"stats_11801","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2024-12-02T11:59:55.440707+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:92","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {stats} run beginning...","time":"2024-12-02T11:59:55.441125+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:93","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2024-12-02T11:59:55.44117+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2024-12-02T11:59:55.441184+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/stats","time":"2024-12-02T11:59:55.441202+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:24","func":"Init","level":"info","msg":"stats服务Init","time":"2024-12-02T11:59:55.441216+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_ADD, channel: stats","time":"2024-12-02T11:59:55.45949+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:59:55.473893+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_REDUCE, channel: stats","time":"2024-12-02T11:59:55.490038+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:59:55.504092+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_FISH_GET, channel: stats","time":"2024-12-02T11:59:55.519287+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:59:55.533582+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: stats","time":"2024-12-02T11:59:55.550206+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:59:55.565871+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_TRIP_SETTLE, channel: stats","time":"2024-12-02T11:59:55.583033+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T11:59:55.599642+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:130","func":"Run","level":"info","msg":"[stats]服务初始化完成","time":"2024-12-02T11:59:55.600129+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:35","func":"Start","level":"info","msg":"stats服务启动成功","time":"2024-12-02T11:59:55.60037+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:164","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21801","time":"2024-12-02T11:59:55.600448+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2024-12-02T11:59:55.603369+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2024-12-02T11:59:55.620307+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000598000}","time":"2024-12-02T11:59:55.620687+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(stats)注册完成","time":"2024-12-02T11:59:55.620842+08:00"}
