{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_REDUCE  int_data:{key:1001  value:101}  int_data:{key:1002  value:1}  int_data:{key:1003  value:101}  int_data:{key:1004  value:7}  int_data:{key:1005  value:10}","time":"2024-12-02T12:01:13.886748+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/stats","time":"2024-12-02T12:01:14.008657+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:148","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2024-12-02T12:02:09.751795+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:80","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2024-12-02T12:02:09.761271+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:102}  int_data:{key:1002  value:1}  int_data:{key:1003  value:102}  int_data:{key:1004  value:11}  int_data:{key:1005  value:200}","time":"2024-12-02T12:02:09.928458+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_FISH_GET  int_data:{key:3001  value:301020000}  int_data:{key:3003  value:101030022}  int_data:{key:3004  value:220}  int_data:{key:3006  value:22}  int_data:{key:3007  value:0}  int_data:{key:3008  value:0}  int_data:{key:3009  value:2}  int_data:{key:3010  value:101020023}","time":"2024-12-02T12:02:09.950702+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/mysql/mysql.go:95","func":"init","level":"info","msg":"Mysql 配置列表加载完成","time":"2024-12-02T12:02:09.951506+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_TRIP_SETTLE  int_data:{key:5004  value:352}","time":"2024-12-02T12:02:33.521532+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_REDUCE  int_data:{key:1001  value:101}  int_data:{key:1002  value:1}  int_data:{key:1003  value:101}  int_data:{key:1004  value:7}  int_data:{key:1005  value:10}","time":"2024-12-02T12:03:11.67456+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:104}  int_data:{key:1002  value:1}  int_data:{key:1003  value:104}  int_data:{key:1004  value:8}  int_data:{key:1005  value:304}","time":"2024-12-02T12:06:55.044603+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_FISH_GET  int_data:{key:3001  value:301020000}  int_data:{key:3003  value:101030021}  int_data:{key:3004  value:70}  int_data:{key:3006  value:16}  int_data:{key:3007  value:0}  int_data:{key:3008  value:0}  int_data:{key:3009  value:1}  int_data:{key:3010  value:101020023}","time":"2024-12-02T12:06:55.061146+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_TRIP_SETTLE  int_data:{key:5004  value:224}","time":"2024-12-02T12:07:08.599314+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/operation.go:169","func":"func3","level":"info","msg":"接收到结束信号interrupt，服务将退出","time":"2024-12-02T12:34:00.997452+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:177","func":"Run","level":"info","msg":"服务开始关闭...","time":"2024-12-02T12:34:00.999634+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:234","func":"func2","level":"info","msg":"服务已被反注册，退出健康检查","time":"2024-12-02T12:34:01.010145+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2024-12-02T12:34:01.010107+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:41","func":"Stop","level":"info","msg":"stats服务关闭中...","time":"2024-12-02T12:34:01.010423+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2024-12-02T12:34:01.019516+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:142","func":"func1","level":"info","msg":"关闭 intranet 服务完成","time":"2024-12-02T12:34:01.020482+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:73","func":"Stop","level":"info","msg":"准备停止 nsq 的所有 consumer, 数量: 5","time":"2024-12-02T12:34:01.020712+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:77","func":"Stop","level":"info","msg":"停止 nsq 的所有 consumer 完成","time":"2024-12-02T12:34:01.021087+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:192","func":"Run","level":"info","msg":"server closed...","time":"2024-12-02T12:34:01.021102+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:164","func":"StopLog","level":"info","msg":"log stop","time":"2024-12-02T12:34:01.021115+08:00"}
