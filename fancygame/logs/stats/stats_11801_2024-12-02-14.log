{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/stats","file_name":"stats_11801","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2024-12-02T14:46:24.875075+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:92","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {stats} run beginning...","time":"2024-12-02T14:46:24.876594+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:93","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2024-12-02T14:46:24.87664+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2024-12-02T14:46:24.876652+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/stats","time":"2024-12-02T14:46:24.876667+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:24","func":"Init","level":"info","msg":"stats服务Init","time":"2024-12-02T14:46:24.876685+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_ADD, channel: stats","time":"2024-12-02T14:46:24.893064+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T14:46:24.908511+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_REDUCE, channel: stats","time":"2024-12-02T14:46:24.924134+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T14:46:24.938078+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_FISH_GET, channel: stats","time":"2024-12-02T14:46:24.954184+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T14:46:24.967168+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: stats","time":"2024-12-02T14:46:24.983843+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T14:46:24.998875+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:262","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_TRIP_SETTLE, channel: stats","time":"2024-12-02T14:46:25.015071+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:194","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 12","time":"2024-12-02T14:46:25.03118+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:130","func":"Run","level":"info","msg":"[stats]服务初始化完成","time":"2024-12-02T14:46:25.031405+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:35","func":"Start","level":"info","msg":"stats服务启动成功","time":"2024-12-02T14:46:25.031673+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:164","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21801","time":"2024-12-02T14:46:25.031863+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2024-12-02T14:46:25.032612+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2024-12-02T14:46:25.045027+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140004d42c0}","time":"2024-12-02T14:46:25.04531+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(stats)注册完成","time":"2024-12-02T14:46:25.045417+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_REDUCE  int_data:{key:1001  value:101}  int_data:{key:1002  value:1}  int_data:{key:1003  value:101}  int_data:{key:1004  value:7}  int_data:{key:1005  value:10}","time":"2024-12-02T14:47:13.249476+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/stats","time":"2024-12-02T14:47:13.37668+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:148","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2024-12-02T14:47:55.383791+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:80","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2024-12-02T14:47:55.394535+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/mysql/mysql.go:95","func":"init","level":"info","msg":"Mysql 配置列表加载完成","time":"2024-12-02T14:47:55.401107+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:101}  int_data:{key:1002  value:1}  int_data:{key:1003  value:101}  int_data:{key:1004  value:8}  int_data:{key:1005  value:96}","time":"2024-12-02T14:48:17.966795+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_REDUCE  int_data:{key:1001  value:101}  int_data:{key:1002  value:1}  int_data:{key:1003  value:101}  int_data:{key:1004  value:7}  int_data:{key:1005  value:10}","time":"2024-12-02T14:49:16.226822+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:102}  int_data:{key:1002  value:1}  int_data:{key:1003  value:102}  int_data:{key:1004  value:11}  int_data:{key:1005  value:300}","time":"2024-12-02T14:49:53.790122+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_FISH_GET  int_data:{key:3001  value:301020000}  int_data:{key:3003  value:101030033}  int_data:{key:3004  value:144}  int_data:{key:3006  value:20}  int_data:{key:3007  value:0}  int_data:{key:3008  value:0}  int_data:{key:3009  value:1}  int_data:{key:3010  value:101020001}","time":"2024-12-02T14:49:53.8196+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:104}  int_data:{key:1002  value:1}  int_data:{key:1003  value:104}  int_data:{key:1004  value:8}  int_data:{key:1005  value:100}","time":"2024-12-02T14:50:40.659219+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_FISH_GET  int_data:{key:3001  value:301020000}  int_data:{key:3003  value:101030056}  int_data:{key:3004  value:160}  int_data:{key:3006  value:20}  int_data:{key:3007  value:0}  int_data:{key:3008  value:0}  int_data:{key:3009  value:1}  int_data:{key:3010  value:101020012}","time":"2024-12-02T14:50:40.676631+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_ITEM_ADD  int_data:{key:1001  value:101}  int_data:{key:1002  value:1}  int_data:{key:1003  value:101}  int_data:{key:1004  value:8}  int_data:{key:1005  value:700}","time":"2024-12-02T14:51:49.058858+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/internal/pubsub/sub.go:48","func":"HandleEvent","level":"debug","msg":"Handler CommonEvent:player_id:9000019  product_id:1  channel_id:1001  event_type:ET_TRIP_SETTLE  int_data:{key:5004  value:700}","time":"2024-12-02T14:51:49.110294+08:00"}
