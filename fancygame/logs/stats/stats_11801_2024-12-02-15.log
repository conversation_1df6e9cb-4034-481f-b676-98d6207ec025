{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/operation.go:169","func":"func3","level":"info","msg":"接收到结束信号interrupt，服务将退出","time":"2024-12-02T15:02:15.210683+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:177","func":"Run","level":"info","msg":"服务开始关闭...","time":"2024-12-02T15:02:15.212421+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2024-12-02T15:02:15.229178+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:234","func":"func2","level":"info","msg":"服务已被反注册，退出健康检查","time":"2024-12-02T15:02:15.2292+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:41","func":"Stop","level":"info","msg":"stats服务关闭中...","time":"2024-12-02T15:02:15.229291+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2024-12-02T15:02:15.237973+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:142","func":"func1","level":"info","msg":"关闭 intranet 服务完成","time":"2024-12-02T15:02:15.238475+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:73","func":"Stop","level":"info","msg":"准备停止 nsq 的所有 consumer, 数量: 5","time":"2024-12-02T15:02:15.238519+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:77","func":"Stop","level":"info","msg":"停止 nsq 的所有 consumer 完成","time":"2024-12-02T15:02:15.238793+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:192","func":"Run","level":"info","msg":"server closed...","time":"2024-12-02T15:02:15.238811+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:164","func":"StopLog","level":"info","msg":"log stop","time":"2024-12-02T15:02:15.238827+08:00"}
