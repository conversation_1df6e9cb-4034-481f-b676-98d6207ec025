{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/stats","file_name":"stats_11801","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-03-31T11:43:26.613632+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {stats} run beginning...","time":"2025-03-31T11:43:26.613875+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-03-31T11:43:26.613897+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-03-31T11:43:26.613907+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/stats","time":"2025-03-31T11:43:26.613915+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:24","func":"Init","level":"info","msg":"stats服务Init","time":"2025-03-31T11:43:26.613923+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_ADD, channel: stats","time":"2025-03-31T11:43:26.655992+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:43:26.672532+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_REDUCE, channel: stats","time":"2025-03-31T11:43:26.688341+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:43:26.705605+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_FISH_GET, channel: stats","time":"2025-03-31T11:43:26.721697+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:43:26.736417+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: stats","time":"2025-03-31T11:43:26.751674+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:43:26.766423+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_TRIP_SETTLE, channel: stats","time":"2025-03-31T11:43:26.781722+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:43:26.796597+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[stats]服务初始化完成","time":"2025-03-31T11:43:26.79675+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21801","time":"2025-03-31T11:43:26.79694+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:35","func":"Start","level":"info","msg":"stats服务启动成功","time":"2025-03-31T11:43:26.796857+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-03-31T11:43:26.797647+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-03-31T11:43:26.811928+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000706dc0}","time":"2025-03-31T11:43:26.812171+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(stats)注册完成","time":"2025-03-31T11:43:26.812258+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/operation.go:169","func":"func3","level":"info","msg":"接收到结束信号interrupt，服务将退出","time":"2025-03-31T11:46:30.560934+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:183","func":"Run","level":"info","msg":"服务开始关闭...","time":"2025-03-31T11:46:30.56132+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2025-03-31T11:46:30.583943+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:41","func":"Stop","level":"info","msg":"stats服务关闭中...","time":"2025-03-31T11:46:30.584027+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:234","func":"func2","level":"info","msg":"服务已被反注册，退出健康检查","time":"2025-03-31T11:46:30.583957+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2025-03-31T11:46:30.591417+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:73","func":"Stop","level":"info","msg":"准备停止 nsq 的所有 consumer, 数量: 5","time":"2025-03-31T11:46:30.591544+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:144","func":"func1","level":"info","msg":"关闭 intranet 服务完成","time":"2025-03-31T11:46:30.591553+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:77","func":"Stop","level":"info","msg":"停止 nsq 的所有 consumer 完成","time":"2025-03-31T11:46:30.592008+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:198","func":"Run","level":"info","msg":"server closed...","time":"2025-03-31T11:46:30.592041+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:164","func":"StopLog","level":"info","msg":"log stop","time":"2025-03-31T11:46:30.59208+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/stats","file_name":"stats_11801","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-03-31T11:46:43.332699+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {stats} run beginning...","time":"2025-03-31T11:46:43.333089+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-03-31T11:46:43.333129+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-03-31T11:46:43.333139+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/stats","time":"2025-03-31T11:46:43.333149+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:24","func":"Init","level":"info","msg":"stats服务Init","time":"2025-03-31T11:46:43.333159+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_ADD, channel: stats","time":"2025-03-31T11:46:43.349635+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:46:43.364183+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_REDUCE, channel: stats","time":"2025-03-31T11:46:43.382274+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:46:43.410541+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_FISH_GET, channel: stats","time":"2025-03-31T11:46:43.428142+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:46:43.444905+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: stats","time":"2025-03-31T11:46:43.45962+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:46:43.474981+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_TRIP_SETTLE, channel: stats","time":"2025-03-31T11:46:43.490792+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:46:43.505344+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[stats]服务初始化完成","time":"2025-03-31T11:46:43.505398+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:35","func":"Start","level":"info","msg":"stats服务启动成功","time":"2025-03-31T11:46:43.50546+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21801","time":"2025-03-31T11:46:43.505498+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-03-31T11:46:43.505738+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-03-31T11:46:43.516576+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140006a0160}","time":"2025-03-31T11:46:43.516637+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(stats)注册完成","time":"2025-03-31T11:46:43.516663+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/operation.go:169","func":"func3","level":"info","msg":"接收到结束信号interrupt，服务将退出","time":"2025-03-31T11:46:45.198145+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:183","func":"Run","level":"info","msg":"服务开始关闭...","time":"2025-03-31T11:46:45.198263+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2025-03-31T11:46:45.212917+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:41","func":"Stop","level":"info","msg":"stats服务关闭中...","time":"2025-03-31T11:46:45.212949+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:234","func":"func2","level":"info","msg":"服务已被反注册，退出健康检查","time":"2025-03-31T11:46:45.212948+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2025-03-31T11:46:45.223281+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:73","func":"Stop","level":"info","msg":"准备停止 nsq 的所有 consumer, 数量: 5","time":"2025-03-31T11:46:45.223379+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:144","func":"func1","level":"info","msg":"关闭 intranet 服务完成","time":"2025-03-31T11:46:45.223388+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:77","func":"Stop","level":"info","msg":"停止 nsq 的所有 consumer 完成","time":"2025-03-31T11:46:45.223482+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:198","func":"Run","level":"info","msg":"server closed...","time":"2025-03-31T11:46:45.223495+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:164","func":"StopLog","level":"info","msg":"log stop","time":"2025-03-31T11:46:45.223509+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/stats","file_name":"stats_11801","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-03-31T11:48:19.385318+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {stats} run beginning...","time":"2025-03-31T11:48:19.385908+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-03-31T11:48:19.385931+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-03-31T11:48:19.385941+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/stats","time":"2025-03-31T11:48:19.385949+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:24","func":"Init","level":"info","msg":"stats服务Init","time":"2025-03-31T11:48:19.385958+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_ADD, channel: stats","time":"2025-03-31T11:48:19.402857+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:48:19.417073+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_REDUCE, channel: stats","time":"2025-03-31T11:48:19.434098+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:48:19.448516+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_FISH_GET, channel: stats","time":"2025-03-31T11:48:19.463458+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:48:19.478252+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: stats","time":"2025-03-31T11:48:19.496253+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:48:19.511308+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_TRIP_SETTLE, channel: stats","time":"2025-03-31T11:48:19.526281+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:48:19.542072+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[stats]服务初始化完成","time":"2025-03-31T11:48:19.542177+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:35","func":"Start","level":"info","msg":"stats服务启动成功","time":"2025-03-31T11:48:19.542234+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21801","time":"2025-03-31T11:48:19.542264+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-03-31T11:48:19.542901+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-03-31T11:48:19.553119+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000459760}","time":"2025-03-31T11:48:19.553171+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(stats)注册完成","time":"2025-03-31T11:48:19.553216+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/operation.go:169","func":"func3","level":"info","msg":"接收到结束信号interrupt，服务将退出","time":"2025-03-31T11:48:21.143958+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:183","func":"Run","level":"info","msg":"服务开始关闭...","time":"2025-03-31T11:48:21.144009+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2025-03-31T11:48:21.151103+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:41","func":"Stop","level":"info","msg":"stats服务关闭中...","time":"2025-03-31T11:48:21.151127+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:234","func":"func2","level":"info","msg":"服务已被反注册，退出健康检查","time":"2025-03-31T11:48:21.151117+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2025-03-31T11:48:21.158873+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:73","func":"Stop","level":"info","msg":"准备停止 nsq 的所有 consumer, 数量: 5","time":"2025-03-31T11:48:21.158942+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:144","func":"func1","level":"info","msg":"关闭 intranet 服务完成","time":"2025-03-31T11:48:21.158958+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:77","func":"Stop","level":"info","msg":"停止 nsq 的所有 consumer 完成","time":"2025-03-31T11:48:21.15902+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:198","func":"Run","level":"info","msg":"server closed...","time":"2025-03-31T11:48:21.159032+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:164","func":"StopLog","level":"info","msg":"log stop","time":"2025-03-31T11:48:21.159043+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/stats","file_name":"stats_11801","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-03-31T11:50:03.736573+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {stats} run beginning...","time":"2025-03-31T11:50:03.736953+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-03-31T11:50:03.736983+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-03-31T11:50:03.736993+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/stats","time":"2025-03-31T11:50:03.737034+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:24","func":"Init","level":"info","msg":"stats服务Init","time":"2025-03-31T11:50:03.737049+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_ADD, channel: stats","time":"2025-03-31T11:50:03.752881+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:50:03.767528+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_REDUCE, channel: stats","time":"2025-03-31T11:50:03.783809+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:50:03.797785+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_FISH_GET, channel: stats","time":"2025-03-31T11:50:03.813408+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:50:03.829162+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: stats","time":"2025-03-31T11:50:03.847474+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:50:03.86465+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_TRIP_SETTLE, channel: stats","time":"2025-03-31T11:50:03.88296+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:50:03.898845+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[stats]服务初始化完成","time":"2025-03-31T11:50:03.898878+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:35","func":"Start","level":"info","msg":"stats服务启动成功","time":"2025-03-31T11:50:03.899022+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21801","time":"2025-03-31T11:50:03.899056+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-03-31T11:50:03.89934+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-03-31T11:50:03.911098+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400040c160}","time":"2025-03-31T11:50:03.911148+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(stats)注册完成","time":"2025-03-31T11:50:03.911193+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/operation.go:169","func":"func3","level":"info","msg":"接收到结束信号interrupt，服务将退出","time":"2025-03-31T11:52:01.87163+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:183","func":"Run","level":"info","msg":"服务开始关闭...","time":"2025-03-31T11:52:01.872334+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2025-03-31T11:52:01.885311+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:41","func":"Stop","level":"info","msg":"stats服务关闭中...","time":"2025-03-31T11:52:01.885798+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:234","func":"func2","level":"info","msg":"服务已被反注册，退出健康检查","time":"2025-03-31T11:52:01.885562+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2025-03-31T11:52:01.898371+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:73","func":"Stop","level":"info","msg":"准备停止 nsq 的所有 consumer, 数量: 5","time":"2025-03-31T11:52:01.898548+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:144","func":"func1","level":"info","msg":"关闭 intranet 服务完成","time":"2025-03-31T11:52:01.898568+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:77","func":"Stop","level":"info","msg":"停止 nsq 的所有 consumer 完成","time":"2025-03-31T11:52:01.898763+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:198","func":"Run","level":"info","msg":"server closed...","time":"2025-03-31T11:52:01.898804+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:164","func":"StopLog","level":"info","msg":"log stop","time":"2025-03-31T11:52:01.898817+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/stats","file_name":"stats_11801","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-03-31T11:52:07.369558+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {stats} run beginning...","time":"2025-03-31T11:52:07.369939+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-03-31T11:52:07.369973+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-03-31T11:52:07.369983+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/stats","time":"2025-03-31T11:52:07.369991+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:24","func":"Init","level":"info","msg":"stats服务Init","time":"2025-03-31T11:52:07.37+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_ADD, channel: stats","time":"2025-03-31T11:52:07.389417+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:52:07.403894+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_ITEM_REDUCE, channel: stats","time":"2025-03-31T11:52:07.420461+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:52:07.436828+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_FISH_GET, channel: stats","time":"2025-03-31T11:52:07.455051+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:52:07.471124+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_LOGOUT, channel: stats","time":"2025-03-31T11:52:07.495174+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:52:07.51168+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:261","func":"createTopicChannel","level":"info","msg":"创建topic和channel 成功，topic: dev_ET_TRIP_SETTLE, channel: stats","time":"2025-03-31T11:52:07.530285+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:193","func":"Subscribe","level":"info","msg":"订阅成功，concurrent: 10","time":"2025-03-31T11:52:07.550737+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[stats]服务初始化完成","time":"2025-03-31T11:52:07.550905+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21801","time":"2025-03-31T11:52:07.551128+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:35","func":"Start","level":"info","msg":"stats服务启动成功","time":"2025-03-31T11:52:07.551028+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-03-31T11:52:07.551859+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-03-31T11:52:07.563059+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140005a8c60}","time":"2025-03-31T11:52:07.563308+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(stats)注册完成","time":"2025-03-31T11:52:07.563443+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/operation.go:169","func":"func3","level":"info","msg":"接收到结束信号interrupt，服务将退出","time":"2025-03-31T11:52:08.86701+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:183","func":"Run","level":"info","msg":"服务开始关闭...","time":"2025-03-31T11:52:08.867083+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2025-03-31T11:52:08.873985+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/statssrv/cmd/main.go:41","func":"Stop","level":"info","msg":"stats服务关闭中...","time":"2025-03-31T11:52:08.87404+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:234","func":"func2","level":"info","msg":"服务已被反注册，退出健康检查","time":"2025-03-31T11:52:08.874005+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:320","func":"DeregisterServer","level":"info","msg":"反注册服务成功","time":"2025-03-31T11:52:08.881724+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:73","func":"Stop","level":"info","msg":"准备停止 nsq 的所有 consumer, 数量: 5","time":"2025-03-31T11:52:08.881774+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:144","func":"func1","level":"info","msg":"关闭 intranet 服务完成","time":"2025-03-31T11:52:08.881815+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/nsqx/nsq.go:77","func":"Stop","level":"info","msg":"停止 nsq 的所有 consumer 完成","time":"2025-03-31T11:52:08.881843+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:198","func":"Run","level":"info","msg":"server closed...","time":"2025-03-31T11:52:08.881856+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:164","func":"StopLog","level":"info","msg":"log stop","time":"2025-03-31T11:52:08.881896+08:00"}
