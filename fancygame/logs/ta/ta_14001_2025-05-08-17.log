{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-08T17:42:23.955975+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-05-08T17:42:23.956316+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-08T17:42:23.956353+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-08T17:42:23.956367+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-05-08T17:42:23.95638+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-05-08T17:42:23.956441+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:32","func":"Init","level":"info","msg":"ta服务Init","time":"2025-05-08T17:42:23.956484+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:42:23.95659+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:42:23.95673+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:42:23.956779+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:42:23.956822+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-05-08T17:42:23.956935+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-05-08T17:42:23.957101+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:56","func":"StartShuShu","level":"fatal","msg":"create dir error mkdir /data: read-only file system","time":"2025-05-08T17:42:23.957203+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-08T17:42:41.073424+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-05-08T17:42:41.073731+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-08T17:42:41.073769+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-08T17:42:41.073782+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-05-08T17:42:41.073793+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-05-08T17:42:41.073853+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:32","func":"Init","level":"info","msg":"ta服务Init","time":"2025-05-08T17:42:41.073901+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:42:41.073968+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:42:41.074054+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:42:41.074082+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:42:41.074127+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-05-08T17:42:41.074215+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:63","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-05-08T17:42:41.074292+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-05-08T17:42:41.07434+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-08T17:42:41.074716+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-08T17:42:41.105519+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140004cc000}","time":"2025-05-08T17:42:41.105587+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-05-08T17:42:41.105607+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:44","func":"PlayerOpenReport","level":"info","msg":"Received report from player {\"TableName\":\"r_launch_step\",\"ProductID\":1,\"ChannelType\":1001,\"Platform\":1,\"AppLanguage\":2,\"Country\":\"ChineseSimplified\",\"AccType\":0,\"AppVersion\":\"1.0.1\",\"NowDate\":\"20250508\",\"TimeStampValue\":\"1746697397\",\"PlayerId\":0,\"LaunchStepId\":1,\"LaunchStepDuration\":0,\"LaunchFailDetail\":null,\"DeviceCode\":\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}","time":"2025-05-08T17:43:17.693941+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:44","func":"PlayerOpenReport","level":"info","msg":"Received report from player {\"requestId\":\"e6b4051d-073f-4ae0-a26c-2efd59736eec\",\"code\":200,\"msg\":\"成功\",\"data\":{\"datetime\":\"2025-05-08 17:44:15\",\"timestamp\":1746697455,\"timezone\":\"CST\"}}te\":\"20250508\",\"TimeStampValue\":\"1746697455\",\"PlayerId\":0,\"LaunchStepId\":1,\"LaunchStepDuration\":0,\"LaunchFailDetail\":null,\"DeviceCode\":\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}","time":"2025-05-08T17:44:15.64135+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-08T17:44:53.794426+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-05-08T17:44:53.794607+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-08T17:44:53.794643+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-08T17:44:53.794657+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-05-08T17:44:53.794669+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-05-08T17:44:53.794725+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:32","func":"Init","level":"info","msg":"ta服务Init","time":"2025-05-08T17:44:53.794768+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:44:53.794856+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:44:53.794967+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:44:53.795012+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-08T17:44:53.795044+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-05-08T17:44:53.795143+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:63","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-05-08T17:44:53.795204+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-05-08T17:44:53.79524+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-08T17:44:53.795637+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-08T17:44:53.811063+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000522000}","time":"2025-05-08T17:44:53.811113+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-05-08T17:44:53.811132+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:45","func":"PlayerOpenReport","level":"info","msg":"Received report from player {\"TableName\":\"r_launch_step\",\"ProductID\":1,\"ChannelType\":1001,\"Platform\":1,\"AppLanguage\":2,\"Country\":\"ChineseSimplified\",\"AccType\":0,\"AppVersion\":\"1.0.1\",\"NowDate\":\"20250508\",\"TimeStampValue\":\"1746697512\",\"PlayerId\":0,\"LaunchStepId\":1,\"LaunchStepDuration\":0,\"LaunchFailDetail\":null,\"DeviceCode\":\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}","time":"2025-05-08T17:45:12.438474+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:62","func":"PlayerOpenReport","level":"info","msg":"Received report from player 0: {TableName:r_launch_step DefaultHeader:1|1001|1|2|ChineseSimplified|0|1.0.1 NowDate:20250508 TimeStampValue:1746697512 PlayerId:0 LaunchStepId:1 LaunchStepDuration:0 LaunchFailDetail: DeviceCode:2ef3ac771d4548395e9f1b2c29e3102c4a952d52}","time":"2025-05-08T17:45:12.442497+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:45","func":"PlayerOpenReport","level":"info","msg":"Received report from player {\"requestId\":\"764face2-bd58-4234-b170-c148dada6105\",\"code\":200,\"msg\":\"成功\",\"data\":{\"datetime\":\"2025-05-08 17:45:30\",\"timestamp\":1746697530,\"timezone\":\"CST\"}}te\":\"20250508\",\"TimeStampValue\":\"1746697529\",\"PlayerId\":0,\"LaunchStepId\":1,\"LaunchStepDuration\":0,\"LaunchFailDetail\":null,\"DeviceCode\":\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}","time":"2025-05-08T17:45:30.424399+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:62","func":"PlayerOpenReport","level":"info","msg":"Received report from player 0: {TableName: DefaultHeader:0|0|0|0||0| NowDate: TimeStampValue: PlayerId:0 LaunchStepId:0 LaunchStepDuration:0 LaunchFailDetail: DeviceCode:}","time":"2025-05-08T17:45:30.42483+08:00"}
