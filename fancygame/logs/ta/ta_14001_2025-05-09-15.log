{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-09T15:15:47.996788+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-05-09T15:15:47.997178+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-09T15:15:47.997214+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-09T15:15:47.997226+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-05-09T15:15:47.997238+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-05-09T15:15:47.997288+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:15:47.997375+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:33","func":"Init","level":"info","msg":"ta服务Init","time":"2025-05-09T15:15:47.997431+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:15:47.997477+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:15:47.997532+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:15:47.997559+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:15:47.997586+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-05-09T15:15:47.997653+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:64","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-05-09T15:15:47.997724+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-05-09T15:15:47.997741+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-09T15:15:47.998123+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-09T15:15:48.019582+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140005cc000}","time":"2025-05-09T15:15:48.019651+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-05-09T15:15:48.019677+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:45","func":"PlayerOpenReport","level":"info","msg":"Received report from player {\"TableName\":\"r_launch_step\",\"ProductID\":1,\"ChannelType\":1001,\"Platform\":1,\"AppLanguage\":2,\"Country\":\"ChineseSimplified\",\"AccType\":0,\"AppVersion\":\"1.0.1\",\"NowDate\":\"20250509\",\"TimeStampValue\":\"1746775023\",\"PlayerId\":0,\"LaunchStepId\":1,\"LaunchStepDuration\":0,\"LaunchFailDetail\":null,\"DeviceCode\":\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}","time":"2025-05-09T15:17:05.734518+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:62","func":"PlayerOpenReport","level":"info","msg":"Received report from player 0: {TableName:r_launch_step DefaultHeader:1|1001|1|2|ChineseSimplified|0|1.0.1 NowDate:20250509 TimeStampValue:1746775023 PlayerId:0 LaunchStepId:1 LaunchStepDuration:0 LaunchFailDetail: DeviceCode:2ef3ac771d4548395e9f1b2c29e3102c4a952d52}","time":"2025-05-09T15:17:05.736933+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-09T15:22:21.0903+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-05-09T15:22:21.090484+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-09T15:22:21.09051+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-09T15:22:21.090522+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-05-09T15:22:21.090534+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-05-09T15:22:21.090586+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:22:21.090678+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:33","func":"Init","level":"info","msg":"ta服务Init","time":"2025-05-09T15:22:21.090737+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:22:21.090775+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:22:21.090865+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:22:21.090906+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:22:21.090938+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-05-09T15:22:21.09101+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:64","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-05-09T15:22:21.091045+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-05-09T15:22:21.091167+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-09T15:22:21.091582+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-09T15:22:21.106687+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000620000}","time":"2025-05-09T15:22:21.106751+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-05-09T15:22:21.106778+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:45","func":"PlayerOpenReport","level":"info","msg":"Received report from player {\"TableName\":\"r_launch_step\",\"ProductID\":1,\"ChannelType\":1001,\"Platform\":1,\"AppLanguage\":2,\"Country\":\"ChineseSimplified\",\"AccType\":0,\"AppVersion\":\"1.0.1\",\"NowDate\":\"20250509\",\"TimeStampValue\":\"1746775388\",\"PlayerId\":0,\"LaunchStepId\":1,\"LaunchStepDuration\":0,\"LaunchFailDetail\":null,\"DeviceCode\":\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}","time":"2025-05-09T15:23:10.602925+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:62","func":"PlayerOpenReport","level":"info","msg":"Received report from player 0: {TableName:r_launch_step DefaultHeader:1|1001|1|2|ChineseSimplified|0|1.0.1 NowDate:20250509 TimeStampValue:1746775388 PlayerId:0 LaunchStepId:1 LaunchStepDuration:0 LaunchFailDetail: DeviceCode:2ef3ac771d4548395e9f1b2c29e3102c4a952d52}","time":"2025-05-09T15:23:10.604362+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:45","func":"PlayerOpenReport","level":"info","msg":"Received report from player {\"TableName\":\"r_launch_step\",\"ProductID\":1,\"ChannelType\":1001,\"Platform\":1,\"AppLanguage\":2,\"Country\":\"ChineseSimplified\",\"AccType\":0,\"AppVersion\":\"1.0.1\",\"NowDate\":\"20250509\",\"TimeStampValue\":\"1746775474\",\"PlayerId\":0,\"LaunchStepId\":1,\"LaunchStepDuration\":0,\"LaunchFailDetail\":null,\"DeviceCode\":\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}","time":"2025-05-09T15:24:36.641175+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:62","func":"PlayerOpenReport","level":"info","msg":"Received report from player 0: {TableName:r_launch_step DefaultHeader:1|1001|1|2|ChineseSimplified|0|1.0.1 NowDate:20250509 TimeStampValue:1746775474 PlayerId:0 LaunchStepId:1 LaunchStepDuration:0 LaunchFailDetail: DeviceCode:2ef3ac771d4548395e9f1b2c29e3102c4a952d52}","time":"2025-05-09T15:24:36.643221+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:45","func":"PlayerOpenReport","level":"info","msg":"Received report from player {\"TableName\":\"r_launch_step\",\"ProductID\":1,\"ChannelType\":1001,\"Platform\":1,\"AppLanguage\":2,\"Country\":\"ChineseSimplified\",\"AccType\":0,\"AppVersion\":\"1.0.1\",\"NowDate\":\"20250509\",\"TimeStampValue\":\"1746775498\",\"PlayerId\":0,\"LaunchStepId\":1,\"LaunchStepDuration\":0,\"LaunchFailDetail\":null,\"DeviceCode\":\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}","time":"2025-05-09T15:25:00.402767+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:62","func":"PlayerOpenReport","level":"info","msg":"Received report from player 0: {TableName:r_launch_step DefaultHeader:1|1001|1|2|ChineseSimplified|0|1.0.1 NowDate:20250509 TimeStampValue:1746775498 PlayerId:0 LaunchStepId:1 LaunchStepDuration:0 LaunchFailDetail: DeviceCode:2ef3ac771d4548395e9f1b2c29e3102c4a952d52}","time":"2025-05-09T15:25:00.404564+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-09T15:25:21.874194+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-05-09T15:25:21.875312+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-09T15:25:21.87535+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-09T15:25:21.875365+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-05-09T15:25:21.875381+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-05-09T15:25:21.875424+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:25:21.875592+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:33","func":"Init","level":"info","msg":"ta服务Init","time":"2025-05-09T15:25:21.87566+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:25:21.875701+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:25:21.875768+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:25:21.87581+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:25:21.875843+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-05-09T15:25:21.875945+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-05-09T15:25:21.876027+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:64","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-05-09T15:25:21.876027+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-09T15:25:21.876517+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-09T15:25:21.896048+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000598000}","time":"2025-05-09T15:25:21.896115+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-05-09T15:25:21.89614+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/proc/ta_service.go:45","func":"DataReportReq","level":"info","msg":"数据上报成功 msg: 77|1|9889","player_id":77,"productID":1,"time":"2025-05-09T15:25:35.610936+08:00","trace_id":"cbcebb24-2ca6-11f0-9826-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/proc/ta_service.go:45","func":"DataReportReq","level":"info","msg":"数据上报成功 msg: 77|2|43137","player_id":77,"productID":1,"time":"2025-05-09T15:26:18.731404+08:00","trace_id":"e5845def-2ca6-11f0-9826-080027d1b278"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-09T15:26:39.291952+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-05-09T15:26:39.292143+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-09T15:26:39.292174+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-09T15:26:39.292185+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-05-09T15:26:39.292195+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-05-09T15:26:39.292254+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:26:39.292354+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:33","func":"Init","level":"info","msg":"ta服务Init","time":"2025-05-09T15:26:39.292422+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:26:39.292466+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:26:39.292585+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:26:39.292615+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-05-09T15:26:39.292643+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-05-09T15:26:39.292719+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:64","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-05-09T15:26:39.292806+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-05-09T15:26:39.292857+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-09T15:26:39.293254+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-09T15:26:39.307787+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000638000}","time":"2025-05-09T15:26:39.307844+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-05-09T15:26:39.30787+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/proc/ta_service.go:45","func":"DataReportReq","level":"info","msg":"数据上报成功 msg: report_type:DRT_NOVICE_SCENE  report_data:\"77|3|25289\"","player_id":77,"productID":1,"time":"2025-05-09T15:26:44.077353+08:00","trace_id":"f4971500-2ca6-11f0-9826-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/proc/ta_service.go:45","func":"DataReportReq","level":"info","msg":"数据上报成功 msg: report_type:DRT_NOVICE_SCENE  report_data:\"77|4|1901\"","player_id":77,"productID":1,"time":"2025-05-09T15:26:45.922147+08:00","trace_id":"f5b93044-2ca6-11f0-9826-080027d1b278"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:45","func":"PlayerOpenReport","level":"info","msg":"Received report from player {\"TableName\":\"r_launch_step\",\"ProductID\":1,\"ChannelType\":1001,\"Platform\":1,\"AppLanguage\":2,\"Country\":\"ChineseSimplified\",\"AccType\":0,\"AppVersion\":\"1.0.1\",\"NowDate\":\"20250509\",\"TimeStampValue\":\"1746776802\",\"PlayerId\":0,\"LaunchStepId\":1,\"LaunchStepDuration\":0,\"LaunchFailDetail\":null,\"DeviceCode\":\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}","time":"2025-05-09T15:46:44.565451+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:62","func":"PlayerOpenReport","level":"info","msg":"Received report from player 0: {TableName:r_launch_step DefaultHeader:1|1001|1|2|ChineseSimplified|0|1.0.1 NowDate:20250509 TimeStampValue:1746776802 PlayerId:0 LaunchStepId:1 LaunchStepDuration:0 LaunchFailDetail: DeviceCode:2ef3ac771d4548395e9f1b2c29e3102c4a952d52}","time":"2025-05-09T15:46:44.570743+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:45","func":"PlayerOpenReport","level":"info","msg":"Received report from player {\"TableName\":\"r_launch_step\",\"ProductID\":1,\"ChannelType\":1001,\"Platform\":1,\"AppLanguage\":2,\"Country\":\"ChineseSimplified\",\"AccType\":0,\"AppVersion\":\"1.0.1\",\"NowDate\":\"20250509\",\"TimeStampValue\":\"1746776829\",\"PlayerId\":0,\"LaunchStepId\":1,\"LaunchStepDuration\":0,\"LaunchFailDetail\":null,\"DeviceCode\":\"2ef3ac771d4548395e9f1b2c29e3102c4a952d52\"}","time":"2025-05-09T15:47:10.856764+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/apis/player_open_report.go:62","func":"PlayerOpenReport","level":"info","msg":"Received report from player 0: {TableName:r_launch_step DefaultHeader:1|1001|1|2|ChineseSimplified|0|1.0.1 NowDate:20250509 TimeStampValue:1746776829 PlayerId:0 LaunchStepId:1 LaunchStepDuration:0 LaunchFailDetail: DeviceCode:2ef3ac771d4548395e9f1b2c29e3102c4a952d52}","time":"2025-05-09T15:47:10.857517+08:00"}
