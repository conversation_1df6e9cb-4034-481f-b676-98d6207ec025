{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-10T15:16:30.86625+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-10T15:16:30.8677+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-10T15:16:30.867761+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-10T15:16:30.867917+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-10T15:16:30.867933+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-10T15:16:31.388306+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-10T15:16:31.395967+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-10T15:16:31.39859+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [1001 2001]","time":"2025-06-10T15:16:31.398591+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:40","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-10T15:16:31.398704+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-10T15:16:31.398791+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-10T15:16:31.398959+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-10T15:16:31.399027+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-10T15:16:31.399143+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-10T15:16:31.399179+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-10T15:16:31.399207+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-10T15:16:31.399308+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-10T15:16:31.399383+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:56","func":"StartShuShu","level":"fatal","msg":"create dir error mkdir /data: read-only file system","time":"2025-06-10T15:16:31.399513+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-10T15:16:33.304484+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-10T15:16:33.304662+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-10T15:16:33.304692+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-10T15:16:33.304703+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-10T15:16:33.304714+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-10T15:16:33.782649+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-10T15:16:33.790965+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-10T15:16:33.818539+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [2001 1001]","time":"2025-06-10T15:16:33.818583+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:40","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-10T15:16:33.818601+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-10T15:16:33.818679+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-10T15:16:33.818791+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_login initial word success config= {Broker:[************:9092] GroupID: Topic:acc_login SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-10T15:16:33.818853+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_register initial word success config= {Broker:[************:9092] GroupID: Topic:acc_register SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-10T15:16:33.818967+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_logout initial word success config= {Broker:[************:9092] GroupID: Topic:acc_logout SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-10T15:16:33.819008+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:26","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic acc_item initial word success config= {Broker:[************:9092] GroupID: Topic:acc_item SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e}","time":"2025-06-10T15:16:33.819043+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-10T15:16:33.819123+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-10T15:16:33.819203+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:56","func":"StartShuShu","level":"fatal","msg":"create dir error mkdir /data: read-only file system","time":"2025-06-10T15:16:33.819258+08:00"}
