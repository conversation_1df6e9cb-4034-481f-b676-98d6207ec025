{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:09:50.213646+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:09:50.213874+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:09:50.213899+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:09:50.213911+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:09:50.213928+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:09:50.700357+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:09:50.701633+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [1001 2001]","time":"2025-06-12T16:09:50.726717+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:09:50.726785+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:09:50.726737+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:09:50.726842+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:09:50.726974+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID: Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:3s}","time":"2025-06-12T16:09:50.72703+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:09:50.727119+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: IC6lVBxLZg_1749715790727","time":"2025-06-12T16:09:50.727235+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:09:50.727235+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:09:50.727367+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:09:50.727384+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:09:50.727399+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:09:50.749065+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400024a840}","time":"2025-06-12T16:09:50.749089+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:09:50.749103+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 1","time":"2025-06-12T16:09:50.769048+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-06-12T16:09:50.769132+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-06-12T16:09:50.782279+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 1","time":"2025-06-12T16:09:51.787499+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:1","time":"2025-06-12T16:09:51.787777+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 2","time":"2025-06-12T16:09:51.787886+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 2","time":"2025-06-12T16:09:52.810943+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:2","time":"2025-06-12T16:09:52.811246+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 3","time":"2025-06-12T16:09:52.811384+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 3","time":"2025-06-12T16:09:53.824544+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:3","time":"2025-06-12T16:09:53.824871+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 4","time":"2025-06-12T16:09:53.825028+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 4","time":"2025-06-12T16:09:54.837519+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:4","time":"2025-06-12T16:09:54.837749+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 5","time":"2025-06-12T16:09:54.837854+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 5","time":"2025-06-12T16:09:55.845786+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:5","time":"2025-06-12T16:09:55.846156+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 6","time":"2025-06-12T16:09:55.846259+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 6","time":"2025-06-12T16:09:56.863307+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:6","time":"2025-06-12T16:09:56.863524+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 7","time":"2025-06-12T16:09:56.863587+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 7","time":"2025-06-12T16:09:57.872364+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:7","time":"2025-06-12T16:09:57.872567+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 8","time":"2025-06-12T16:09:57.872641+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:10:16.986857+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:10:16.987152+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:10:16.987192+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:10:16.987206+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:10:16.987218+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:10:17.468619+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:10:17.471979+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [2001 1001]","time":"2025-06-12T16:10:17.509346+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:10:17.509414+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:10:17.509371+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:10:17.509477+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:10:17.509615+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID: Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:3s}","time":"2025-06-12T16:10:17.509682+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:10:17.509783+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:10:17.509856+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: kXjweZOQfO_1749715817509","time":"2025-06-12T16:10:17.509873+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:10:17.510023+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:10:17.510124+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:10:17.510155+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:10:17.525405+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000622580}","time":"2025-06-12T16:10:17.525453+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:10:17.525467+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 1","time":"2025-06-12T16:10:17.554203+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-06-12T16:10:17.554281+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-06-12T16:10:17.565196+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:56","func":"ProcessLoginMessageTest","level":"error","msg":"ProcessLoginMessageTest 检测到重复消费! data: 1","time":"2025-06-12T16:10:17.56901+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:11:09.004563+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:11:09.004914+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:11:09.004954+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:11:09.004976+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:11:09.004987+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:11:09.450696+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:11:09.450768+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:11:09.480736+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [2001 1001]","time":"2025-06-12T16:11:09.480734+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:11:09.480833+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:11:09.480898+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:11:09.481015+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID: Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:3s}","time":"2025-06-12T16:11:09.481075+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:11:09.481174+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:11:09.48127+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: 98WgkMjDHE_1749715869481","time":"2025-06-12T16:11:09.481337+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:11:09.481448+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:11:09.481461+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:11:09.481493+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:11:09.507633+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140002ca840}","time":"2025-06-12T16:11:09.507682+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:11:09.507801+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 1","time":"2025-06-12T16:11:09.522883+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-06-12T16:11:09.522973+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-06-12T16:11:09.532973+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 1","time":"2025-06-12T16:11:10.537869+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:1","time":"2025-06-12T16:11:10.538168+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 1, error: unavailable when GroupID is not set","time":"2025-06-12T16:11:10.53826+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 2","time":"2025-06-12T16:11:10.538331+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 2","time":"2025-06-12T16:11:11.546545+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:2","time":"2025-06-12T16:11:11.546798+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 2, error: unavailable when GroupID is not set","time":"2025-06-12T16:11:11.546895+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 3","time":"2025-06-12T16:11:11.546956+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 3","time":"2025-06-12T16:11:12.559212+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:3","time":"2025-06-12T16:11:12.559317+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 3, error: unavailable when GroupID is not set","time":"2025-06-12T16:11:12.559341+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 4","time":"2025-06-12T16:11:12.559362+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 4","time":"2025-06-12T16:11:13.571308+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:4","time":"2025-06-12T16:11:13.571571+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 4, error: unavailable when GroupID is not set","time":"2025-06-12T16:11:13.57167+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 5","time":"2025-06-12T16:11:13.571779+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 5","time":"2025-06-12T16:11:14.583828+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:5","time":"2025-06-12T16:11:14.584072+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 5, error: unavailable when GroupID is not set","time":"2025-06-12T16:11:14.58418+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 6","time":"2025-06-12T16:11:14.58424+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 6","time":"2025-06-12T16:11:15.596999+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:39","func":"func1","level":"trace","msg":"[login_test_topic] consume key:6","time":"2025-06-12T16:11:15.597169+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 6, error: unavailable when GroupID is not set","time":"2025-06-12T16:11:15.597221+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 7","time":"2025-06-12T16:11:15.597266+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:11:27.664219+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:11:27.664528+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:11:27.664565+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:11:27.664578+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:11:27.664595+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:11:28.135159+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:11:28.138808+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:11:28.192474+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [2001 1001]","time":"2025-06-12T16:11:28.192566+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:11:28.192596+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:11:28.192696+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:11:28.192881+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID: Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:3s}","time":"2025-06-12T16:11:28.192945+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:11:28.193099+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:11:28.193192+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: sog7B9Os_f_1749715888193","time":"2025-06-12T16:11:28.193242+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:11:28.193478+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:11:28.193498+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:11:28.19354+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:11:28.207892+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140002c6dc0}","time":"2025-06-12T16:11:28.207924+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:11:28.20794+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 1","time":"2025-06-12T16:11:28.244009+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-06-12T16:11:28.24405+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-06-12T16:11:28.256482+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:56","func":"ProcessLoginMessageTest","level":"error","msg":"ProcessLoginMessageTest 检测到重复消费! data: 1","time":"2025-06-12T16:11:28.260682+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:12:14.366726+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:12:14.367036+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:12:14.367087+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:12:14.367103+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:12:14.367117+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:12:14.814438+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:12:14.834907+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [2001 1001]","time":"2025-06-12T16:12:15.103371+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:12:15.103372+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:12:15.103434+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:12:15.103541+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:12:15.103743+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID: Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:12:15.103826+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:12:15.103971+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:12:15.104058+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: Te7I-5Ds_b_1749715935104","time":"2025-06-12T16:12:15.104086+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:12:15.104314+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:12:15.104338+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:12:15.104432+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:12:15.120166+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14001f08000}","time":"2025-06-12T16:12:15.120195+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:12:15.120211+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 1","time":"2025-06-12T16:12:15.150114+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-06-12T16:12:15.150158+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-06-12T16:12:15.161652+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 1","time":"2025-06-12T16:12:16.166425+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:37","func":"func1","level":"trace","msg":"[login_test_topic] consume key:1","time":"2025-06-12T16:12:16.166758+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 1, error: unavailable when GroupID is not set","time":"2025-06-12T16:12:16.166851+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 2","time":"2025-06-12T16:12:16.166945+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 2","time":"2025-06-12T16:12:17.176496+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:37","func":"func1","level":"trace","msg":"[login_test_topic] consume key:2","time":"2025-06-12T16:12:17.176669+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 2, error: unavailable when GroupID is not set","time":"2025-06-12T16:12:17.176703+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 3","time":"2025-06-12T16:12:17.176731+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 3","time":"2025-06-12T16:12:18.18555+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:37","func":"func1","level":"trace","msg":"[login_test_topic] consume key:3","time":"2025-06-12T16:12:18.185783+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 3, error: unavailable when GroupID is not set","time":"2025-06-12T16:12:18.185867+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 4","time":"2025-06-12T16:12:18.185932+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 4","time":"2025-06-12T16:12:19.19603+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:37","func":"func1","level":"trace","msg":"[login_test_topic] consume key:4","time":"2025-06-12T16:12:19.196307+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 4, error: unavailable when GroupID is not set","time":"2025-06-12T16:12:19.196437+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 5","time":"2025-06-12T16:12:19.196506+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 5","time":"2025-06-12T16:12:20.208669+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:37","func":"func1","level":"trace","msg":"[login_test_topic] consume key:5","time":"2025-06-12T16:12:20.208915+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:117","func":"func1","level":"error","msg":"commit message failed, topic: login_test_topic, key: 5, error: unavailable when GroupID is not set","time":"2025-06-12T16:12:20.209009+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 6","time":"2025-06-12T16:12:20.209105+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:12:36.714811+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:12:36.715139+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:12:36.715182+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:12:36.715196+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:12:36.715209+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:12:37.187685+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:12:37.19866+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:12:37.212074+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [2001 1001]","time":"2025-06-12T16:12:37.212073+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:12:37.21215+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:12:37.212231+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:12:37.212349+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID: Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:12:37.212417+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:12:37.212535+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:12:37.212596+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: Ri7DgrELO__1749715957212","time":"2025-06-12T16:12:37.212684+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:12:37.212852+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:12:37.212899+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:12:37.212913+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:12:37.227514+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400024f080}","time":"2025-06-12T16:12:37.227538+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:12:37.227552+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 1","time":"2025-06-12T16:12:37.25326+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-06-12T16:12:37.253341+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-06-12T16:12:37.264056+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:56","func":"ProcessLoginMessageTest","level":"error","msg":"ProcessLoginMessageTest 检测到重复消费! data: 1","time":"2025-06-12T16:12:37.26822+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:13:59.510858+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:13:59.511202+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:13:59.511233+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:13:59.511247+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:13:59.511268+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:13:59.959067+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:14:00.001022+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:14:00.025358+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [1001 2001]","time":"2025-06-12T16:14:00.02542+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:14:00.025437+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:14:00.025496+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:14:00.025639+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID: Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:14:00.025693+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:14:00.025828+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:14:00.025902+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: FF3wWqeBFJ_1749716040025","time":"2025-06-12T16:14:00.025928+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:14:00.026264+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:14:00.026462+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:14:00.026564+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:14:00.053018+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14003980000}","time":"2025-06-12T16:14:00.053047+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:14:00.053065+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 1","time":"2025-06-12T16:14:00.073683+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-06-12T16:14:00.073763+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-06-12T16:14:00.086292+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 1","time":"2025-06-12T16:14:01.092534+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:37","func":"func1","level":"trace","msg":"[login_test_topic] consume key:1","time":"2025-06-12T16:14:01.092773+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 2","time":"2025-06-12T16:14:01.092855+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 2","time":"2025-06-12T16:14:02.100675+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:37","func":"func1","level":"trace","msg":"[login_test_topic] consume key:2","time":"2025-06-12T16:14:02.100822+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 3","time":"2025-06-12T16:14:02.100896+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 3","time":"2025-06-12T16:14:03.1099+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:37","func":"func1","level":"trace","msg":"[login_test_topic] consume key:3","time":"2025-06-12T16:14:03.110113+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 4","time":"2025-06-12T16:14:03.110189+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:62","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest 首次处理数据: 4","time":"2025-06-12T16:14:04.117276+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/pubsub/consumer.go:37","func":"func1","level":"trace","msg":"[login_test_topic] consume key:4","time":"2025-06-12T16:14:04.117496+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 5","time":"2025-06-12T16:14:04.117585+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:14:17.698883+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:14:17.699256+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:14:17.699289+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:14:17.699302+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:14:17.699318+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:14:18.140636+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:14:18.152151+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [2001 1001]","time":"2025-06-12T16:14:18.159308+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:14:18.159311+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:14:18.159362+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:14:18.159489+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:14:18.15962+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID: Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:14:18.159677+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:14:18.159782+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:14:18.159849+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: 3BRQGf2EpE_1749716058159","time":"2025-06-12T16:14:18.160006+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:14:18.16013+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:14:18.160147+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:14:18.160154+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:14:18.173331+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400024cc60}","time":"2025-06-12T16:14:18.17335+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:14:18.173363+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:43","func":"ProcessLoginMessageTest","level":"info","msg":"ProcessLoginMessageTest: 1","time":"2025-06-12T16:14:18.202576+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:150","func":"initConfigs","level":"info","msg":"Redis 配置加载完成","time":"2025-06-12T16:14:18.202648+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/redisfactory/factory.go:82","func":"createClient","level":"info","msg":"Conn RedisSrv Success : ip=************:6379, PoolSize=200, MaxRetries=1, PoolTimeout=4.00s,","time":"2025-06-12T16:14:18.21454+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/logic/login_reporter.go:56","func":"ProcessLoginMessageTest","level":"error","msg":"ProcessLoginMessageTest 检测到重复消费! data: 1","time":"2025-06-12T16:14:18.218878+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:18:03.840945+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:18:03.841286+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:18:03.841324+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:18:03.841342+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:18:03.841357+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:18:04.308509+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:18:04.311266+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:18:04.312364+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [1001 2001]","time":"2025-06-12T16:18:04.312365+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:18:04.312411+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:18:04.312489+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:18:04.31265+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID:tasrv_test Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:18:04.312711+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:18:04.312954+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:18:04.313094+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: fkQkvAG7Sq_1749716284313","time":"2025-06-12T16:18:04.31319+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:18:04.313273+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:18:04.313369+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:18:04.313387+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:18:04.329385+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400198c000}","time":"2025-06-12T16:18:04.329405+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:18:04.329418+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:18:59.017086+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:18:59.017409+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:18:59.017447+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:18:59.01746+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:18:59.017472+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:18:59.456909+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:18:59.473339+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:18:59.484448+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [1001 2001]","time":"2025-06-12T16:18:59.484447+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:18:59.484546+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:18:59.484657+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:18:59.484793+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID:tasrv_test1 Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:18:59.484867+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:18:59.485079+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: pbQqxkPJ5D_1749716339485","time":"2025-06-12T16:18:59.485168+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:18:59.485175+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:18:59.485322+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:18:59.485335+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:18:59.485422+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:18:59.499923+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400014a160}","time":"2025-06-12T16:18:59.49994+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:18:59.499955+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:24:14.396664+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:24:14.397002+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:24:14.397036+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:24:14.397053+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:24:14.397065+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:24:14.929817+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:24:14.939732+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:24:14.978369+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [1001 2001]","time":"2025-06-12T16:24:14.978431+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:24:14.978453+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:24:14.978528+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:24:14.978682+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID:tasrv_test1 Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:24:14.978736+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:24:14.978936+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:24:14.979045+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: 6anHvUkTV1_1749716654979","time":"2025-06-12T16:24:14.979139+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:24:14.979246+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:24:14.979284+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:24:14.979316+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:24:14.993391+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400024edc0}","time":"2025-06-12T16:24:14.993459+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:24:14.993481+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:29:16.847025+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:29:16.847308+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:29:16.847346+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:29:16.847362+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:29:16.847374+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:29:17.295666+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:29:17.298911+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:29:17.301139+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [2001 1001]","time":"2025-06-12T16:29:17.301137+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:29:17.301192+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:29:17.301277+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:29:17.301451+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID:tasrv_test1 Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:29:17.30151+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:29:17.301762+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:29:17.301903+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: d8aRfYRSt6_1749716957301","time":"2025-06-12T16:29:17.30193+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:29:17.302097+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:29:17.302118+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:29:17.302103+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:29:17.316231+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140004ae420}","time":"2025-06-12T16:29:17.316254+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:29:17.31627+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/ta","file_name":"ta_14001","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-06-12T16:29:30.312376+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {ta} run beginning...","time":"2025-06-12T16:29:30.312543+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-06-12T16:29:30.312591+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-06-12T16:29:30.312605+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/ta","time":"2025-06-12T16:29:30.312618+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"warning","msg":"consul config ret:key not found path:1/1002/data_report","time":"2025-06-12T16:29:30.766531+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/2001/data_report","time":"2025-06-12T16:29:30.769426+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/cmodel/data_report_cfg.go:129","func":"LoadAllDataReportCfg","level":"info","msg":"successfully loaded DataReport config for channels: [2001 1001]","time":"2025-06-12T16:29:30.770373+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:39","func":"Init","level":"info","msg":"ta服务Init","time":"2025-06-12T16:29:30.770422+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/data_report","time":"2025-06-12T16:29:30.770389+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:31","func":"Init","level":"info","msg":"init reportSwitch {Track:true UserSet:true UserSetOnce:true}","time":"2025-06-12T16:29:30.770573+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_producer.go:25","func":"NewKafkaSender","level":"info","msg":"kafka sender topic  initial word success config= {Broker:[************:9092] GroupID: Topic: SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:29:30.770757+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/kafka/event/kafka_consumer.go:27","func":"NewKafkaReceiver","level":"info","msg":"kafka receiver topic login_test_topic initial word success config= {Broker:[************:9092] GroupID:tasrv_test1 Topic:login_test_topic SSLCertFile: SSLKeyFile: SASLMechanism: Username: Password: Algorithm: Timeout:10s UseSSL:false SSLInsecureSkipVerify:false Balancer:\u003cnil\u003e CommitInterval:0s}","time":"2025-06-12T16:29:30.770829+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[ta]服务初始化完成","time":"2025-06-12T16:29:30.771035+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:24001","time":"2025-06-12T16:29:30.7711+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:67","func":"StartShuShu","level":"info","msg":"te prefix: -YG8KpgVx8_1749716970771","time":"2025-06-12T16:29:30.771197+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-06-12T16:29:30.771379+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/internal/services/shushu_server.go:81","func":"StartShuShu","level":"info","msg":"time zone CST, offset: 8","time":"2025-06-12T16:29:30.771421+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/tasrv/cmd/main.go:72","func":"Start","level":"info","msg":"ta服务启动成功","time":"2025-06-12T16:29:30.771439+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-06-12T16:29:30.787032+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x140005249a0}","time":"2025-06-12T16:29:30.787094+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(ta)注册完成","time":"2025-06-12T16:29:30.787117+08:00"}
