{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/webapi","file_name":"webapi_11101","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T14:46:17.213255+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {webapi} run beginning...","time":"2025-05-06T14:46:17.213476+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T14:46:17.2135+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T14:46:17.213513+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/webapi","time":"2025-05-06T14:46:17.213526+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/cmd/main.go:30","func":"Init","level":"info","msg":"webapi服务Init","time":"2025-05-06T14:46:17.213538+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/webapi","file_name":"webapi_11101","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T14:47:23.498592+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {webapi} run beginning...","time":"2025-05-06T14:47:23.498745+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T14:47:23.498774+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T14:47:23.498789+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/webapi","time":"2025-05-06T14:47:23.498839+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/cmd/main.go:30","func":"Init","level":"info","msg":"webapi服务Init","time":"2025-05-06T14:47:23.498895+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/webapi","file_name":"webapi_11101","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T14:47:40.328764+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {webapi} run beginning...","time":"2025-05-06T14:47:40.328937+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T14:47:40.328952+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T14:47:40.328963+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/webapi","time":"2025-05-06T14:47:40.328975+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/cmd/main.go:30","func":"Init","level":"info","msg":"webapi服务Init","time":"2025-05-06T14:47:40.328987+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/webapi","file_name":"webapi_11101","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T14:49:49.715031+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {webapi} run beginning...","time":"2025-05-06T14:49:49.715214+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T14:49:49.715241+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T14:49:49.715252+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/webapi","time":"2025-05-06T14:49:49.715262+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/cmd/main.go:30","func":"Init","level":"info","msg":"webapi服务Init","time":"2025-05-06T14:49:49.715272+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"error","msg":"consul config err:get list fail: Unexpected response code: 502 () path:1/1001/app_address_info","time":"2025-05-06T14:49:54.718993+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/cmd/main.go:39","func":"Init","level":"error","msg":"初始化配置失败:failed to init InitAppAddressInfoCfg: get list fail: Unexpected response code: 502 ()","time":"2025-05-06T14:49:54.719428+08:00"}
{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/webapi","file_name":"webapi_11101","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T14:51:01.864212+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {webapi} run beginning...","time":"2025-05-06T14:51:01.864578+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T14:51:01.864595+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T14:51:01.864608+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/webapi","time":"2025-05-06T14:51:01.864621+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/cmd/main.go:30","func":"Init","level":"info","msg":"webapi服务Init","time":"2025-05-06T14:51:01.864634+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"error","msg":"consul config err:get list fail: Unexpected response code: 502 () path:1/1001/app_address_info","time":"2025-05-06T14:51:06.868163+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/cmd/main.go:39","func":"Init","level":"error","msg":"初始化配置失败:failed to init InitAppAddressInfoCfg: get list fail: Unexpected response code: 502 ()","time":"2025-05-06T14:51:06.86845+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[webapi]服务初始化完成","time":"2025-05-06T14:51:06.868543+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21101","time":"2025-05-06T14:51:06.869+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T14:51:06.869507+08:00"}
{"file":"/Users/<USER>/go/src/base/fancy-common/pkg/repo/consul_config/config.go:64","func":"GetConfig","level":"error","msg":"consul config err:get list fail: Unexpected response code: 502 () path:1/1001/app_update_info","time":"2025-05-06T14:51:11.871783+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:73","func":"RegisterService","level":"error","msg":"Service Register error\nUnexpected response code: 502 ()","time":"2025-05-06T14:51:11.872182+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T14:51:11.873244+08:00"}
{"error":"service Register error","file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:297","func":"registerToConsul","level":"error","msg":"registerToConsul : 注册失败","time":"2025-05-06T14:51:11.873321+08:00"}
{"error":"registerToConsul : 注册失败","file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:195","func":"StartServer","level":"error","msg":"注册失败","time":"2025-05-06T14:51:11.873383+08:00"}
