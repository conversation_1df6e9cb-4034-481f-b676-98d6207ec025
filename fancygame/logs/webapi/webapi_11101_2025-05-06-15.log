{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/webapi","file_name":"webapi_11101","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-06T15:05:47.866406+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {webapi} run beginning...","time":"2025-05-06T15:05:47.86663+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-06T15:05:47.866659+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-06T15:05:47.866675+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/webapi","time":"2025-05-06T15:05:47.866686+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/cmd/main.go:30","func":"Init","level":"info","msg":"webapi服务Init","time":"2025-05-06T15:05:47.866697+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/app_address_info","time":"2025-05-06T15:05:48.163636+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/app_update_info","time":"2025-05-06T15:05:48.448409+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:117","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/app_resource_info","time":"2025-05-06T15:05:49.009842+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[webapi]服务初始化完成","time":"2025-05-06T15:05:49.009836+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21101","time":"2025-05-06T15:05:49.010086+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-06T15:05:49.010324+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-06T15:05:49.026524+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x1400040e580}","time":"2025-05-06T15:05:49.026579+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(webapi)注册完成","time":"2025-05-06T15:05:49.026618+08:00"}
