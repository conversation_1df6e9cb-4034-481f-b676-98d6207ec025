{"fields.level":"trace","file":"/Users/<USER>/go/src/base/frameworks/lib/logdog/logger.go:160","file_dir":"../../logs/webapi","file_name":"webapi_11101","func":"SetWriteFile","level":"debug","msg":"########setup log success########","outputErr":true,"time":"2025-05-07T18:01:16.191162+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:94","func":"setupLog","level":"info","msg":" - [tcgo]using srvname: {webapi} run beginning...","time":"2025-05-07T18:01:16.191387+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:95","func":"setupLog","level":"info","msg":" - [tcgo]using logLevel:  # trace","time":"2025-05-07T18:01:16.191414+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:96","func":"setupLog","level":"info","msg":" - [tcgo]using printJson:  # false","time":"2025-05-07T18:01:16.191426+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:97","func":"setupLog","level":"info","msg":" - [tcgo]using logdir:  # ../../logs/webapi","time":"2025-05-07T18:01:16.191437+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/cmd/main.go:30","func":"Init","level":"info","msg":"webapi服务Init","time":"2025-05-07T18:01:16.191458+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/app_address_info","time":"2025-05-07T18:01:16.55508+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/app_update_info","time":"2025-05-07T18:01:16.920119+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/confd/client.go:118","func":"watcherLoop","level":"trace","msg":"watcher start : 1/1001/app_resource_info","time":"2025-05-07T18:01:17.221103+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:132","func":"Run","level":"info","msg":"[webapi]服务初始化完成","time":"2025-05-07T18:01:17.221101+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/driver/driver.go:166","func":"func3","level":"info","msg":"开启 http 服务，addr: 0.0.0.0:21101","time":"2025-05-07T18:01:17.22131+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/lib/ha/consul/consul_register.go:71","func":"RegisterService","level":"trace","msg":"registing to ************:8500\n","time":"2025-05-07T18:01:17.221469+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:294","func":"registerToConsul","level":"info","msg":"registerToConsul : ************:8500","time":"2025-05-07T18:01:17.252231+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:198","func":"StartServer","level":"trace","msg":"Srv-Consul Status : \u0026{0x14000996000}","time":"2025-05-07T18:01:17.252285+08:00"}
{"file":"/Users/<USER>/go/src/base/frameworks/kit/rpc/server.go:277","func":"StartServer","level":"info","msg":"RPC服务(webapi)注册完成","time":"2025-05-07T18:01:17.252315+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/internal/apis/app_info.go:83","func":"AppInfoRequest","level":"trace","msg":"recv version \u0026{0.1.14 ChineseSimplified 1001 1 3}, ","time":"2025-05-07T18:01:19.328029+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/internal/apis/app_info.go:127","func":"AppInfoRequest","level":"info","msg":"updateInfo:\u0026{0 0.0.1 false false https://www.google.com/ download_msg 0.1.0}, addrInfo:\u0026{0 192.168.1.61:31201 192.168.1.51:8080 /resources http://192.168.1.51:8080/configs/test  }, resInfo:\u0026{0   false false}","time":"2025-05-07T18:01:19.328401+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/internal/apis/app_info.go:83","func":"AppInfoRequest","level":"trace","msg":"recv version \u0026{0.1.14 ChineseSimplified 1001 1 3}, ","time":"2025-05-07T18:29:24.811647+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/internal/apis/app_info.go:127","func":"AppInfoRequest","level":"info","msg":"updateInfo:\u0026{0 0.0.1 false false https://www.google.com/ download_msg 0.1.0}, addrInfo:\u0026{0 192.168.1.61:31201 192.168.1.51:8080 /resources http://192.168.1.51:8080/configs/test  }, resInfo:\u0026{0   false false}","time":"2025-05-07T18:29:24.815827+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/internal/apis/app_info.go:83","func":"AppInfoRequest","level":"trace","msg":"recv version \u0026{0.1.14 ChineseSimplified 1001 1 3}, ","time":"2025-05-07T18:31:00.18786+08:00"}
{"file":"/Users/<USER>/go/src/fancygame/webapisrv/internal/apis/app_info.go:127","func":"AppInfoRequest","level":"info","msg":"updateInfo:\u0026{0 0.0.1 false false https://www.google.com/ download_msg 0.1.0}, addrInfo:\u0026{0 192.168.1.61:31201 192.168.1.51:8080 /resources http://192.168.1.51:8080/configs/test  }, resInfo:\u0026{0   false false}","time":"2025-05-07T18:31:00.188347+08:00"}
