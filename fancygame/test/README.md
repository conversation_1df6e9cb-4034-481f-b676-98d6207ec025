# 位置跟踪服务

一个简单易用的地理位置获取和跟踪服务，专为手机设备优化。

## 功能特性

- 📱 **手机优化**: 响应式界面，专门针对移动设备优化
- 📍 **精确定位**: 使用浏览器原生地理定位API获取精确位置
- 🔒 **安全可靠**: HTTPS支持，安全的位置数据传输
- 💾 **数据存储**: 位置数据本地文件存储，无需数据库
- 🎯 **一键操作**: 用户只需点击链接即可完成位置上报
- 📊 **管理界面**: 提供位置记录查看和管理功能

## 快速开始

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 创建数据目录

```bash
mkdir -p data
```

### 3. 启动服务

```bash
go run cmd/main.go
```

服务将在端口8080启动。

### 4. 访问应用

- **位置获取**: `http://localhost:8080/`
- **管理界面**: `http://localhost:8080/admin.html`

## 部署说明

地理定位功能需要HTTPS环境。生产环境建议使用Nginx反向代理配置SSL证书。

## API接口

### 位置上报
```
POST /api/location
```

### 获取位置历史
```
GET /api/locations
```