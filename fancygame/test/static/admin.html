<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位置记录管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
        }

        .refresh-btn:hover {
            background: #0056b3;
        }

        .stats {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #666;
        }

        .content {
            padding: 20px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #999;
        }

        .location-list {
            display: grid;
            gap: 15px;
        }

        .location-item {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }

        .location-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .location-id {
            font-weight: bold;
            color: #333;
        }

        .location-time {
            color: #666;
            font-size: 12px;
        }

        .location-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            font-size: 14px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
        }

        .detail-label {
            font-weight: 500;
            color: #555;
        }

        .detail-value {
            color: #333;
            font-family: monospace;
        }

        .location-actions {
            margin-top: 10px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 5px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            text-decoration: none;
            display: inline-block;
            white-space: nowrap;
            transition: all 0.2s ease;
        }

        .map-btn {
            background: #28a745;
            color: white;
            flex: 1;
            text-align: center;
            min-width: 100px;
        }

        .map-btn:hover {
            background: #1e7e34;
            transform: translateY(-1px);
        }

        .copy-btn {
            background: #6c757d;
            color: white;
            flex: 1;
            text-align: center;
            min-width: 100px;
        }

        .copy-btn:hover {
            background: #545b62;
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .stats {
                justify-content: center;
            }

            .location-details {
                grid-template-columns: 1fr;
            }

            .location-actions {
                flex-direction: column;
                gap: 6px;
            }

            .action-btn {
                min-width: auto;
                flex: none;
                padding: 8px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📍 位置记录管理</h1>
            <p>查看和管理所有位置上报记录</p>
        </div>

        <div class="controls">
            <button class="refresh-btn" onclick="loadLocations()">🔄 刷新数据</button>
            <div class="stats">
                <span>总记录数: <strong id="totalCount">-</strong></span>
                <span>最新更新: <strong id="lastUpdate">-</strong></span>
            </div>
        </div>

        <div class="content">
            <div id="loading" class="loading" style="display: none;">
                正在加载位置记录...
            </div>
            
            <div id="noData" class="no-data" style="display: none;">
                暂无位置记录
            </div>
            
            <div id="locationList" class="location-list"></div>
        </div>
    </div>

    <script>
        let locations = [];

        // 页面加载时获取数据
        window.addEventListener('load', loadLocations);

        async function loadLocations() {
            const loading = document.getElementById('loading');
            const noData = document.getElementById('noData');
            const locationList = document.getElementById('locationList');
            
            loading.style.display = 'block';
            noData.style.display = 'none';
            locationList.innerHTML = '';

            try {
                const response = await fetch('/api/locations');
                const data = await response.json();
                
                locations = data || [];
                updateStats();
                renderLocations();
                
            } catch (error) {
                console.error('加载位置记录失败:', error);
                noData.innerHTML = '❌ 加载数据失败';
                noData.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        }

        function updateStats() {
            document.getElementById('totalCount').textContent = locations.length;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
        }

        function renderLocations() {
            const locationList = document.getElementById('locationList');
            const noData = document.getElementById('noData');
            
            if (locations.length === 0) {
                noData.style.display = 'block';
                return;
            }

            // 按时间倒序排列
            const sortedLocations = [...locations].sort((a, b) => 
                new Date(b.timestamp) - new Date(a.timestamp)
            );

            locationList.innerHTML = sortedLocations.map(location => `
                <div class="location-item">
                    <div class="location-header">
                        <span class="location-id">#${location.id}</span>
                        <span class="location-time">${formatTime(location.timestamp)}</span>
                    </div>
                    
                    <div class="location-details">
                        <div class="detail-item">
                            <span class="detail-label">纬度:</span>
                            <span class="detail-value">${location.latitude.toFixed(6)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">经度:</span>
                            <span class="detail-value">${location.longitude.toFixed(6)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">精度:</span>
                            <span class="detail-value">${location.accuracy ? Math.round(location.accuracy) + 'm' : '未知'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">IP地址:</span>
                            <span class="detail-value">${location.ip_address || '未知'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">设备类型:</span>
                            <span class="detail-value">${location.device_info?.device_type || '未知'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">操作系统:</span>
                            <span class="detail-value">${location.device_info?.operating_system || '未知'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">浏览器:</span>
                            <span class="detail-value">${location.device_info?.browser || '未知'}</span>
                        </div>
                    </div>
                    
                    ${location.device_info ? `
                        <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; border: 1px solid #e9ecef;">
                            <div style="font-weight: bold; margin-bottom: 8px; color: #495057;">📱 详细设备信息</div>
                            <div style="font-size: 12px; line-height: 1.4;">
                                ${location.device_info.screen_resolution ? `屏幕分辨率: ${location.device_info.screen_resolution}<br>` : ''}
                                ${location.device_info.language ? `语言设置: ${location.device_info.language}<br>` : ''}
                                ${location.device_info.timezone ? `时区: ${location.device_info.timezone}<br>` : ''}
                                <div style="margin-top: 5px; color: #6c757d; word-break: break-all;">
                                    User-Agent: ${location.device_info.user_agent || location.user_agent || '未知'}
                                </div>
                            </div>
                        </div>
                    ` : location.user_agent ? `
                        <div class="detail-item" style="margin-top: 10px;">
                            <span class="detail-label">设备信息:</span>
                            <span class="detail-value" style="font-size: 12px; word-break: break-all;">
                                ${location.user_agent}
                            </span>
                        </div>
                    ` : ''}
                    
                    <div class="location-actions">
                        <a href="https://uri.amap.com/marker?position=${location.longitude},${location.latitude}&name=位置点&src=myapp" 
                           target="_blank" class="action-btn map-btn">🗺️ 高德地图查看</a>
                        <a href="https://www.google.com/maps?q=${location.latitude},${location.longitude}" 
                           target="_blank" class="action-btn map-btn" style="background: #4285f4;">🗺️ 谷歌地图查看</a>
                        <button class="action-btn copy-btn" 
                                onclick="copyCoordinates(${location.latitude}, ${location.longitude})">
                            📋 复制坐标
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        function copyCoordinates(lat, lon) {
            const text = `${lat},${lon}`;
            navigator.clipboard.writeText(text).then(() => {
                // 简单的复制成功提示
                const originalText = event.target.textContent;
                event.target.textContent = '✅ 已复制';
                setTimeout(() => {
                    event.target.textContent = originalText;
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制: ' + text);
            });
        }

        // 自动刷新 (可选)
        // setInterval(loadLocations, 30000); // 每30秒自动刷新
    </script>
</body>
</html> 