<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>位置获取</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px 30px;
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        .title {
            color: #333;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 14px;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .device-info {
            margin-top: 15px;
            background: #f0f8ff;
            border-radius: 10px;
            padding: 15px;
            font-size: 12px;
            color: #666;
            border: 1px solid #e0f0ff;
        }

        .device-info strong {
            color: #333;
        }

        .auto-submit-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
            font-size: 14px;
            text-align: center;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            line-height: 1.5;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.loading {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .location-info {
            margin-top: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            font-size: 12px;
            color: #666;
        }

        .location-info strong {
            color: #333;
        }

        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #383d41;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            margin-top: 30px;
            color: #999;
            font-size: 12px;
        }

        /* 移动设备优化 */
        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 20px;
            }
            
            .device-info {
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">📍 位置自动上报</h1>
        <p class="subtitle">正在自动获取并上报您的位置信息...</p>
        
        <div id="status" class="status">
            <span class="spinner"></span>正在获取位置信息...
        </div>
        <div id="locationInfo" class="location-info" style="display: none;"></div>
        <div id="deviceInfo" class="device-info" style="display: none;"></div>
        
        <div class="footer">
            🔒 您的位置和设备信息将被安全处理<br>
            <a href="/admin.html" target="_blank" style="color: #667eea; text-decoration: none; font-size: 12px;">
                📊 查看管理界面
            </a>
        </div>
    </div>

    <script>
        const status = document.getElementById('status');
        const locationInfo = document.getElementById('locationInfo');
        const deviceInfo = document.getElementById('deviceInfo');

        // 页面加载后自动获取位置并上报
        window.addEventListener('load', function() {
            // 检查是否为HTTPS
            if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
                showStatus('error', '⚠️ 地理定位需要HTTPS协议，请使用HTTPS访问');
                return;
            }
            
            // 先显示设备信息
            showDeviceInfo();
            
            // 延迟1秒后自动开始获取位置
            setTimeout(getLocationAndSubmit, 1000);
        });

        function getLocationAndSubmit() {
            if (!navigator.geolocation) {
                showStatus('error', '❌ 您的浏览器不支持地理定位功能');
                return;
            }

            const options = {
                enableHighAccuracy: true,    // 启用高精度
                timeout: 15000,             // 15秒超时
                maximumAge: 0               // 不使用缓存
            };

            navigator.geolocation.getCurrentPosition(
                onLocationSuccess,
                onLocationError,
                options
            );
        }

        function onLocationSuccess(position) {
            const { latitude, longitude, accuracy } = position.coords;
            
            // 显示位置信息
            showLocationInfo(latitude, longitude, accuracy);
            
            // 更新状态为正在上报
            showStatus('loading', '<span class="spinner"></span>正在上报位置信息...');
            
            // 立即上报数据
            submitLocationData(latitude, longitude, accuracy);
        }

        function onLocationError(error) {
            let message;
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    message = '❌ 需要您的位置权限才能继续，请刷新页面并允许定位';
                    break;
                case error.POSITION_UNAVAILABLE:
                    message = '❌ 位置信息不可用，请检查GPS或网络';
                    break;
                case error.TIMEOUT:
                    message = '❌ 获取位置超时，请刷新页面重试';
                    break;
                default:
                    message = '❌ 获取位置信息失败，请刷新页面重试';
                    break;
            }
            
            showStatus('error', message);
        }

        function submitLocationData(latitude, longitude, accuracy) {
            // 收集设备信息
            const deviceData = getDeviceInfo();
            
            // 组合位置和设备数据
            const submitData = {
                latitude: latitude,
                longitude: longitude,
                accuracy: accuracy,
                device_info: deviceData
            };

            // 发送到服务器
            fetch('/api/location', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(submitData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus('success', `✅ 位置上报成功！记录ID: ${data.id}`);
                    
                    // 3秒后显示感谢信息
                    setTimeout(() => {
                        showStatus('success', '🎉 上报完成！感谢您的配合！<br><small>可以在<a href="/admin.html" target="_blank" style="color: inherit;">管理界面</a>查看位置记录</small>');
                    }, 3000);
                } else {
                    showStatus('error', `❌ 上报失败: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('上报失败:', error);
                showStatus('error', '❌ 网络错误，上报失败');
            });
        }

        function getDeviceInfo() {
            const nav = navigator;
            const screen = window.screen;
            
            // 获取设备类型
            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(nav.userAgent);
            
            // 获取操作系统
            let os = '未知';
            if (nav.userAgent.indexOf('Windows') > -1) os = 'Windows';
            else if (nav.userAgent.indexOf('Mac') > -1) os = 'macOS';
            else if (nav.userAgent.indexOf('Android') > -1) os = 'Android';
            else if (nav.userAgent.indexOf('iPhone') > -1 || nav.userAgent.indexOf('iPad') > -1) os = 'iOS';
            else if (nav.userAgent.indexOf('Linux') > -1) os = 'Linux';
            
            // 获取浏览器
            let browser = '未知';
            if (nav.userAgent.indexOf('Chrome') > -1) browser = 'Chrome';
            else if (nav.userAgent.indexOf('Firefox') > -1) browser = 'Firefox';
            else if (nav.userAgent.indexOf('Safari') > -1) browser = 'Safari';
            else if (nav.userAgent.indexOf('Edge') > -1) browser = 'Edge';

            return {
                user_agent: nav.userAgent,
                device_type: isMobile ? '移动设备' : '桌面设备',
                operating_system: os,
                browser: browser,
                screen_resolution: `${screen.width}x${screen.height}`,
                language: nav.language,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                timestamp: new Date().toISOString()
            };
        }

        function showDeviceInfo() {
            const deviceData = getDeviceInfo();
            
            deviceInfo.innerHTML = `
                <strong>📱 设备信息</strong><br>
                设备类型: ${deviceData.device_type}<br>
                操作系统: ${deviceData.operating_system}<br>
                浏览器: ${deviceData.browser}<br>
                屏幕分辨率: ${deviceData.screen_resolution}<br>
                语言: ${deviceData.language}
            `;
            deviceInfo.style.display = 'block';
        }

        function showStatus(type, message) {
            status.className = `status ${type}`;
            status.innerHTML = message;
            status.style.display = 'block';
        }

        function showLocationInfo(lat, lon, accuracy) {
            const accuracyText = accuracy ? `${Math.round(accuracy)}米` : '未知';
            
            locationInfo.innerHTML = `
                <strong>📍 位置信息</strong><br>
                纬度: ${lat.toFixed(6)}<br>
                经度: ${lon.toFixed(6)}<br>
                精度: ${accuracyText}
            `;
            locationInfo.style.display = 'block';
        }
    </script>
</body>
</html> 