#!/bin/bash

echo "=== 位置跟踪服务 API 测试 ==="
echo

# 测试服务器是否运行
echo "1. 检查服务器状态..."
if curl -s http://localhost:8080/ > /dev/null; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行，请先启动: go run cmd/main.go"
    exit 1
fi

echo

# 测试位置上报API
echo "2. 测试位置上报API..."
response=$(curl -s -X POST http://localhost:8080/api/location \
    -H "Content-Type: application/json" \
    -d '{"latitude": 39.9042, "longitude": 116.4074, "accuracy": 5.0}')

if echo "$response" | grep -q '"success":true'; then
    echo "✅ 位置上报成功: $response"
else
    echo "❌ 位置上报失败: $response"
fi

echo

# 测试获取位置历史API
echo "3. 测试位置历史API..."
locations=$(curl -s http://localhost:8080/api/locations)
count=$(echo "$locations" | jq '. | length' 2>/dev/null)

if [ "$count" -gt 0 ] 2>/dev/null; then
    echo "✅ 位置历史获取成功，共 $count 条记录"
    echo "最新记录:"
    echo "$locations" | jq '.[0]' 2>/dev/null || echo "$locations"
else
    echo "❌ 位置历史获取失败或无数据"
fi

echo

# 检查数据文件
echo "4. 检查数据文件..."
if [ -f "data/locations.log" ]; then
    lines=$(wc -l < data/locations.log)
    echo "✅ 数据文件存在，包含 $lines 行记录"
else
    echo "❌ 数据文件不存在"
fi

echo
echo "=== 测试完成 ==="
echo "访问地址:"
echo "- 位置获取页面: http://localhost:8080/"
echo "- 管理界面: http://localhost:8080/admin.html" 