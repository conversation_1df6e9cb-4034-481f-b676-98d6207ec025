-- fancy_general.t_rod_rig_info definition

CREATE TABLE if not exists `t_rod_rig_info` (
  `player_id` bigint(20) NOT NULL COMMENT '玩家ID',
  `name` varchar(256) NOT NULL COMMENT '名称',
  `rig_id` int(10) NOT NULL COMMENT '钓组id',
  `rod_id` bigint(20) NOT NULL COMMENT '鱼竿杆ID',
  `reel_id` bigint(20) NOT NULL COMMENT '鱼轮ID',
  `line_id` bigint(20) NOT NULL COMMENT '主线ID',
  `leader_id` bigint(20) NOT NULL COMMENT '子线ID',
  `baits_id` bigint(20) NOT NULL COMMENT '鱼饵ID',
  `hooks_id` bigint(20) NOT NULL COMMENT '鱼钩ID',
  `floats_id` bigint(20) NOT NULL COMMENT '浮标ID',
  `ext_info` varchar(256) DEFAULT NULL COMMENT '扩展信息',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`player_id`, `rig_id`),
  KEY `rig_id` (`rig_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;