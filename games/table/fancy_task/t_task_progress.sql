-- fancy_task.t_task_progress definition

CREATE TABLE if not exists `t_task_progress` (
  `player_id` bigint NOT NULL COMMENT '玩家ID',
  `category` int NOT NULL COMMENT '任务类型',
  `sub_id` bigint NOT NULL COMMENT '子任务id',
  `rewarded` text COMMENT '已奖励',
  `score` int DEFAULT NULL COMMENT '分数',
  `update_at` datetime DEFAULT NULL COMMENT '更新时间',
  `delete_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`player_id`,`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE t_task_progress DROP PRIMARY KEY;
ALTER TABLE t_task_progress ADD PRIMARY KEY (player_id, category, sub_id);