syntax = "proto3";
package app_info;
option go_package = "/appInfoPB";

import "common.proto";
import "enum.proto";
import "errors.proto";

// AppInfoReq App信息
message AppInfoReq {
    string                  app_version  = 1;            // 客户端版本
    string                  app_language = 2;            // 语言
    common.PRODUCT_ID       product_id   = 3;            // 产品ID
    common.CHANNEL_TYPE     channel      = 4;            // 渠道
    common.PLATFORM_TYPE    platform     = 5;            // 平台类型
}

message AppInfoRsp {
    common.Result          ret            = 1;
    common.AppUpdateInfo   info_update    = 2;
    common.AppResourceInfo info_resource  = 3;
    common.AppAddressInfo  info_address   = 4;
}
