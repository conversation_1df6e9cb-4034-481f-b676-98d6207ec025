package dao

import (
	"context"
	"statssrv/internal/model"
	"statssrv/internal/test"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func TestSyncRule(t *testing.T) {
	test.InitSql()
	engine, _ := GetStatsEngine()
	err := engine.Sync2(&model.TStatsRule{})
	if err != nil {
		t.Fatal(err)
	}
}

func TestUpdatePlayerRule(t *testing.T) {
	test.InitSql()
	ctx := context.Background()
	playerId := uint64(1)
	rules := []*model.TStatsRule{
		{
			PlayerId: playerId,
			Typ:      1,
			Target:   1,
			Field:    1,
			Val:      1,
			AddType:  commonPB.SUM_ADD_SA_ADD,
		},
		{
			PlayerId: playerId,
			Typ:      1,
			Target:   2,
			Field:    1,
			Val:      13,
			AddType:  commonPB.SUM_ADD_SA_MAX,
		},
		{
			PlayerId: playerId,
			Typ:      1,
			Target:   3,
			Field:    1,
			Val:      3,
			AddType:  commonPB.SUM_ADD_SA_VAL,
		},
		{
			PlayerId: playerId,
			Typ:      1,
			Target:   4,
			Field:    1,
			Val:      7,
			AddType:  commonPB.SUM_ADD_SA_UNI_ADD,
		},
	}

	engine, err := GetStatsEngine()
	if err != nil {
		t.Fatal(err)
	}

	session := engine.NewSession()
	defer session.Close()

	err = UpdatePlayerStatsRule(ctx, session, playerId, rules)
	if err != nil {
		t.Fatal(err)
	}
}
