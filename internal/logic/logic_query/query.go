package logicQuery

import (
	"context"
	"usersrv/internal/dao/dao_device"
	daoExtend "usersrv/internal/dao/dao_extend"
	"usersrv/internal/dao/dao_info"
	"usersrv/internal/dao/dao_master"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// QueryPlayerInfoById 根据玩家id查询玩家信息
func QueryPlayerInfoById(ctx context.Context, productId int32, playerId uint64) (*model.PlayerMaster, *model.PlayerInfo, error) {
	var err error
	entry := logx.NewLogEntry(ctx)

	pMaster, err := dao_master.Get(ctx, productId, playerId)
	if err != nil {
		return nil, nil, err
	}

	pPlayer, err := dao_info.Get(ctx, productId, playerId)
	if err != nil {
		return nil, nil, err
	}

	entry.Debugf("GetPlayerByUid, uid:%d, get player info from mysql ok", playerId)

	return pMaster, pPlayer, err
}

// QueryPlayerDeviceInfo 查询玩家设备信息
func QueryPlayerDeviceInfo(ctx context.Context, productId int32, playerId uint64) (*model.PlayerDevices, error) {
	entry := logx.NewLogEntry(ctx)

	playerDevice, err := dao_device.Get(ctx, productId, playerId)
	if err != nil {
		return nil, err
	}

	entry.Debugf("query player:%d, device info:%v success", playerId, *playerDevice)

	return playerDevice, nil
}


// QueryPlayerExtend 查询玩家扩展信息
func QueryPlayerExtend(ctx context.Context, productId int32, playerId uint64) (*model.PlayerExtend, error) {

	entry := logx.NewLogEntry(ctx)
	playerExtend, err := daoExtend.QueryPlayerExtend(ctx, productId, playerId)
	if err != nil {
		entry.Warnf("QueryPlayerExtend, productId:%d, playerId:%d, not exist:%+v", productId, playerId, err)
		return nil, err
	}

	entry.Debugf("QueryPlayerExtend, productId:%d, playerId:%d, playerExtend:%+v", productId, playerId, *playerExtend)

	return playerExtend, nil
}

// BatchQueryPlayerRichInfo 批量查询玩家全量信息接口
func BatchQueryPlayerRichInfo(ctx context.Context, productId int32, playerIds []uint64) (map[uint64]*model.PlayerMaster, map[uint64]*model.PlayerInfo, error) {
	var err error
	entry := logx.NewLogEntry(ctx)

	pMasters, err := dao_master.BatchGet(ctx, productId, playerIds)
	if err != nil {
		return nil, nil, err
	}

	pPlayers, err := dao_info.BatchGet(ctx, productId, playerIds)
	if err != nil {
		return nil, nil, err
	}

	entry.Debugf("BatchGetPlayerByUid, uid:%v, get player info from mysql ok", playerIds)

	return pMasters, pPlayers, err
}

// BatchQueryPlayerBriefInfo 批量查询玩家基础信息
func BatchQueryPlayerBriefInfo(ctx context.Context, productId int32, playerIds []uint64) (map[uint64]*model.PlayerInfo, error) {
	return dao_info.BatchGet(ctx, productId, playerIds)
}