package model

import (
	"encoding/json"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

const TableNameStatsRule = "t_stats_rule"

type TStatsRule struct {
	Id       int64            `xorm:"pk autoincr comment('id')" json:"id"`
	PlayerId uint64           `xorm:"comment('玩家id') index(index_player)" json:"player_id"`
	Typ      int32            `xorm:"comment('类型') index(index_player)" json:"typ"`
	Field    int64            `xorm:"comment('字段') index(index_player)" json:"field"`
	Target   int64            `xorm:"comment('目标') index(index_player)" json:"target"`
	AddType  commonPB.SUM_ADD `xorm:"comment('统计类型') index(index_player)" json:"add_type"`
	Val      int64            `xorm:"comment('数值')" json:"val"`
	Extra    []int64          `xorm:"comment('拓展信息')" json:"extra"`
	UpdateAt int64            `xorm:"comment('更新时间') updated" json:"update_at"`
}

func (t *TStatsRule) TableName() string {
	return TableNameStatsRule
}

func (t *TStatsRule) ToHash() ([]byte, error) {
	js, err := json.Marshal(t)
	return js, err
}

func (t *TStatsRule) FromHash(hash string) error {
	err := json.Unmarshal([]byte(hash), t)
	return err
}

func (t *TStatsRule) ToProto() *commonPB.StatsRuleInfo {
	return &commonPB.StatsRuleInfo{
		Typ:     t.Typ,
		Field:   t.Field,
		Target:  t.Target,
		AddType: t.AddType,
		Val:     t.Val,
	}
}

func (t *TStatsRule) HashKey() string {
	return fmt.Sprintf("%d:%d:%d:%d", t.Typ, t.Field, t.Target, t.AddType)
}
