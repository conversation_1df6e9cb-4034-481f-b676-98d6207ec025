package pubsub

// 初始化监听器
import (
	"context"
	"statssrv/internal/services"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mq"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"google.golang.org/protobuf/proto"
)

func InitSubscribe() {
	// 初始化监听器
	serverName := viper.GetString(dict.ConfigRpcServerName)
	// 构建监听列表
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_ITEM_ADD.String(), serverName, HandleEvent))
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_ITEM_REDUCE.String(), serverName, HandleEvent))
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_FISH_GET.String(), serverName, HandleEvent))
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_LOGOUT.String(), serverName, HandleEvent))
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_TRIP_SETTLE.String(), serverName, HandleEvent))
}

func initPanic(err error) {
	if err != nil {
		panic(err)
	}
}

func EventPlayerCtx(event *commonPB.EventCommon) context.Context {
	return interceptor.NewRpcClientCtx(
		interceptor.WithPlayerId(event.GetPlayerId()),
		interceptor.WithProductId(event.GetProductId()),
		interceptor.WithChannelType(event.GetChannelId()),
	)
}

func HandleEvent(body []byte) {
	event := &commonPB.EventCommon{}
	if err := proto.Unmarshal(body, event); err != nil {
		logrus.Errorf("Handler CommonEvent Unmarshal fail:%s", err)
		return
	}
	logrus.Debugf("Handler CommonEvent:%s", event)
	ctx := EventPlayerCtx(event)
	if len(event.IntData) == 0 {
		logrus.Errorf("fail data:%+v", event)
		return
	}

	// TODO 暂时固定productid = 1
	// GM 加道具获取不到productID
	// event.ProductId = 1

	services.GetStatsServiceInstance().EventUpdate(ctx, event.PlayerId, event)
	services.GetStatsServiceInstance().EventRuleDo(ctx, event.PlayerId, event)
}
