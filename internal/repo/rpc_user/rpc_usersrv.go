package rpc_user

import (
	"context"
	"errors"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
)

var (
	RpcErr = errors.New("rpc get err")
)

// RpcGetUserInfoByDeviceCode RPC到UserSrv根据DeviceCode获取用户信息
func RpcGetUserInfoByDeviceCode(ctx context.Context, deviceCode string, productID int32) (uint64, error) {
	entry := logx.NewLogEntry(ctx)
	cc, err := grpcHa.GetConnection(dict_common.UserSrv)
	if err != nil {
		entry.Errorf("rpc get err : %v", err)
		return 0, RpcErr
	}

	userRpcClient := userRpc.NewUserServiceClient(cc)

	reqRpc := &userRpc.GetPlayerIdByDeviceReq{
		ProductId:  productID,
		DeviceCode: deviceCode,
	}

	hRet, errRet := userRpcClient.GetPlayerIdByDevice(ctx, reqRpc)
	if errRet != nil {
		entry.Errorf("rpc get err : %v", errRet)
		return 0, RpcErr
	}
	if hRet == nil {
		return 0, nil
	}

	return hRet.PlayerId, errRet
}

// RpcCreatePlayer RPC到UserSrv创建用户
func RpcCreatePlayer(ctx context.Context, req *loginRpc.LoginReq, region *commonPB.RegionInfo) (*userRpc.CreatePlayerRsp, error) {
	entry := logx.NewLogEntry(ctx)
	cc, err := grpcHa.GetConnection(dict_common.UserSrv)
	if err != nil {
		entry.Errorf("rpc get err : %v", err)
		return nil, RpcErr
	}

	userRpcClient := userRpc.NewUserServiceClient(cc)

	reqRpc := &userRpc.CreatePlayerReq{
		ClientVersion: req.ClientVersion,
		ProductId:     req.ProductId,
		ChannelId:     req.ChannelId,
		DeviceInfo:    req.GetDeviceInfo(),
		ThirdToken:    req.ThirdToken,
		Network:       req.Network,
		BundleName:    req.BundleName,
		Platform:      req.Platform,
		CreateType:    req.LoginType,
		AccountInfo:   req.GetAccountInfo(),
		ThirdInfo:     req.GetThirdInfo(),
		Region:        region,
	}

	return userRpcClient.CreatePlayer(ctx, reqRpc)
}

// RpcGetPlayerIdByAccount rpc 获取指定账号的玩家id
func RpcGetPlayerIdByAccount(ctx context.Context, productId int32, account string) (uint64, error) {
	entry := logx.NewLogEntry(ctx)
	if productId == 0 || account == "" {
		return 0, fmt.Errorf("product id or account is empty")
	}

	rpcCli := crpc_user.GetUserRpcInstance().GetUserRpcClient()
	if rpcCli == nil {
		entry.Errorf("user rpc client nil")
		return 0, fmt.Errorf("user rpc client nil")
	}

	rpcReq := &userRpc.GetPlayerIdByAccountReq{
		ProductId: productId,
		AccountInfo: &commonPB.AccountInfo{
			Account: account,
		},
	}

	rpcRsp, err := rpcCli.GetPlayerIdByAccount(ctx, rpcReq)
	if rpcRsp == nil || err != nil {
		entry.Errorf("rpc get player id by account:%s failed : %v", account, err)
		return 0, err
	}

	return rpcRsp.GetPlayerId(), nil
}

// RpcGetPlayerIdByAccountAndPwd 获取指定账号密码的玩家id
func RpcGetPlayerIdByAccountAndPwd(ctx context.Context, productId int32, accountInfo *commonPB.AccountInfo) (uint64, error) {
	entry := logx.NewLogEntry(ctx)
	if productId == 0 || accountInfo == nil {
		return 0, fmt.Errorf("product id or account info is empty")
	}

	rpcCli := crpc_user.GetUserRpcInstance().GetUserRpcClient()
	if rpcCli == nil {
		entry.Errorf("user rpc client nil")
		return 0, fmt.Errorf("user rpc client nil")
	}

	rpcReq := &userRpc.GetPlayerIdByAccountReq{
		ProductId:   productId,
		AccountInfo: accountInfo,
	}

	rpcRsp, err := rpcCli.GetPlayerIdByAccount(ctx, rpcReq)
	if rpcRsp == nil || err != nil {
		entry.Errorf("rpc get player id by account:%+v failed : %v", accountInfo, err)
		return 0, err
	}

	return rpcRsp.GetPlayerId(), nil
}

// RpcDeleteAccount 删除账号
func RpcDeleteAccount(ctx context.Context, productId int32, playerId uint64) (*commonPB.Result, error) {
	ret := protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)
	entry := logx.NewLogEntry(ctx)
	if productId == 0 || playerId <= 0 {
		ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM)
		return ret, fmt.Errorf("product id or playerId info is empty")
	}

	rpcCli := crpc_user.GetUserRpcInstance().GetUserRpcClient()
	if rpcCli == nil {
		entry.Errorf("user rpc client nil")
		return ret, fmt.Errorf("user rpc client nil")
	}

	rpcReq := &userRpc.DeleteAccountReq{
		ProductId: productId,
		PlayerId:  playerId,
	}

	rpcRsp, err := rpcCli.DeleteAccount(ctx, rpcReq)
	if rpcRsp == nil || err != nil {
		entry.Errorf("rpc delete account:%d failed : %v", playerId, err)
		return ret, err
	}

	return rpcRsp.Ret, err
}

// RpcQueryPlayerAgeInfo 根据玩家id查询玩家年龄信息
func RpcQueryPlayerAgeInfo(ctx context.Context, productId int32, playerId uint64) (commonPB.USER_AGE, error) {
	entry := logx.NewLogEntry(ctx)
	if productId == 0 || playerId <= 0 {
		return commonPB.USER_AGE_UA_UNKNOWN, fmt.Errorf("product id or playerId info is empty")
	}

	rpcReq := &userRpc.PlayerAgeQueryReq{
		ProductId: productId,
		PlayerId:  playerId,
	}

	rsp, err := crpc_user.RpcQueryPlayerAgeInfo(ctx, rpcReq)
	if err != nil || rsp == nil {
		entry.Errorf("rpc query player:%d age failed : %v", playerId, err)
		return commonPB.USER_AGE_UA_UNKNOWN, fmt.Errorf("rpc query player age failed : %v", err)
	}

	entry.Debugf("rpc query player:%d age success : %v", playerId, rsp)

	return rsp.GetAge(), nil
}

// GetPlayerIdByOpenId 根据openId和产品id查询玩家id
func GetPlayerIdByOpenId(ctx context.Context, productId int32, loginType commonPB.LOGIN_TYPE, openId string) (uint64, error) {
	entry := logx.NewLogEntry(ctx)
	if productId == 0 || openId == "" {
		return 0, fmt.Errorf("product id or openId is empty")
	}

	rpcCli := crpc_user.GetUserRpcInstance().GetUserRpcClient()
	if rpcCli == nil {
		entry.Errorf("user rpc client nil")
		return 0, fmt.Errorf("user rpc client nil")
	}

	rpcReq := &userRpc.GetPlayerIdByOpenIdReq{
		ProductId: productId,
		LoginType: loginType,
		OpenId:    openId,
	}

	rpcRsp, err := rpcCli.GetPlayerIdByOpenId(ctx, rpcReq)
	if rpcRsp == nil || err != nil {
		entry.Errorf("rpc get player id by account:%s failed : %v", openId, err)
		return 0, err
	}

	return rpcRsp.GetPlayerId(), nil
}
