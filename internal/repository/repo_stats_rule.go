package repository

import (
	"context"
	"errors"
	"statssrv/config"
	"statssrv/internal/dao"
	"statssrv/internal/model"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis"
	"github.com/ldy105cn/xorm"
)

// GetPlayerStatsRule 获取玩家统计规则
func GetPlayerStatsRule(ctx context.Context, playerId uint64) ([]*model.TStatsRule, error) {
	entry := logx.NewLogEntry(ctx)
	hash, err := redisx.GetGeneralCli().HGetAllWithNil(ctx, config.KeyCacheStatsRule(playerId)).Result()
	ret := make([]*model.TStatsRule, 0)
	// 数据为nil有可能过期了
	if err != nil && !errors.Is(err, redis.Nil) {
		// 数据有空标记
		if errors.Is(err, redisx.Empty) {
			return ret, nil
		}
		// 非正常错误
		return nil, err
	}
	// 加载缓存数据
	if len(hash) == 0 {
		ret, err := dao.FindPlayerStatsRule(ctx, playerId)
		if err != nil {
			return nil, err
		}
		// 确实没有数据
		if len(ret) == 0 {
			redisx.GetGeneralCli().HSetNil(ctx, config.KeyCacheStatsRule(playerId))
			return ret, nil
		} else {
			err = setPlayerStatsRuleCache(ctx, playerId, ret)
			if err != nil {
				return nil, err
			}
		}
		return ret, nil
	}

	for _, v := range hash {
		rule := &model.TStatsRule{}
		err := rule.FromHash(v)
		if err != nil {
			entry.Warnf("StatsRule.FromHash.err:%v playerId:%+v v:%+v", err, playerId, v)
			return nil, err
		}
		ret = append(ret, rule)
	}
	return ret, nil
}

// UpdatePlayerStatsRule 更新玩家统计规则
func UpdatePlayerStatsRule(ctx context.Context, playerId uint64, rules []*model.TStatsRule) error {

	engine, err := dao.GetStatsEngine()
	if err != nil {
		return err
	}

	delPlayerStatsRuleCache(ctx, playerId)

	_, err = engine.Transaction(func(s *xorm.Session) (interface{}, error) {
		return nil, dao.UpdatePlayerStatsRule(ctx, s, playerId, rules)
	})

	// 延时双删
	time.AfterFunc(1*time.Second, func() {
		delPlayerStatsRuleCache(ctx, playerId)
	})

	return err
}

// 设置统计规则缓存
func setPlayerStatsRuleCache(ctx context.Context, playerId uint64, rules []*model.TStatsRule) error {
	key := config.KeyCacheStatsRule(playerId)

	hash := make([]string, 0)
	for _, rule := range rules {
		str, _ := rule.ToHash()
		hash = append(hash, rule.HashKey(), string(str))
	}
	pipe := redisx.GetGeneralCli().Pipeline()
	pipe.HSet(ctx, key, hash)
	pipe.Expire(ctx, key, config.RedisKeyCacheStatsRuleExpire)
	_, err := pipe.Exec(ctx)
	return err
}

// 删除统计规则缓存
func delPlayerStatsRuleCache(ctx context.Context, playerId uint64) error {
	key := config.KeyCacheStatsRule(playerId)
	return redisx.GetGeneralCli().Del(ctx, key).Err()
}
