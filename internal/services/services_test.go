package services

import (
	"testing"

	statsRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/statsrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/testx"
	"github.com/sirupsen/logrus"
)

func TestStatsService_GetStatsRules(t *testing.T) {
	testx.Init()
	ctx := testx.TestCtx(1, 1)
	service := GetStatsServiceInstance()
	rsp, err := service.GetStatsRules(ctx, &statsRpc.GetStatsRulesReq{
		PlayerId: 14,
		Typ:      2,
		Field:    0,
		Target:   0,
		AddType:  0,
	})
	if err != nil {
		t.<PERSON><PERSON>("GetStatsRules.err:%v", err)
	}
	logrus.Infof("GetStatsRules.rsp:%+v", rsp)
}
