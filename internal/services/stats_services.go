package services

import (
	"context"
	"statssrv/internal/dao/dao_stats"
	"statssrv/internal/dao/db_mgr"
	"statssrv/internal/logic/l_stats"
	"statssrv/internal/repository"
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	statsRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/statsrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"github.com/ldy105cn/xorm"
)

type StatsService struct{}

var (
	statsOnce              = &sync.Once{}
	statsSingletonInstance *StatsService
)

func GetStatsServiceInstance() *StatsService {
	if statsSingletonInstance != nil {
		return statsSingletonInstance
	}

	statsOnce.Do(func() {
		statsSingletonInstance = &StatsService{}
		statsSingletonInstance.Init()
	})
	return statsSingletonInstance
}

func (s *StatsService) Init() {
}

// EventUpdate 事件更新处理函数
func (s *StatsService) EventUpdate(ctx context.Context, playerId uint64, event *commonPB.EventCommon) {
	entry := logx.NewLogEntry(ctx)

	// 扫表检查
	bigCfg := cmodel.GetAllStats(consul_config.WithGrpcCtx(ctx))

	// 构造变化数据
	arr, err := l_stats.ScanCfg(event, bigCfg)
	if err != nil {
		entry.Errorf("scan cfg err %v", err)
		return
	}

	if len(arr) == 0 {
		return
	}

	// 提交到数据库
	engine, err := db_mgr.GetStatsSession()
	if err != nil {
		entry.Errorf("db_mgr session faild:%v", err)
		return
	}

	_, err = engine.Transaction(func(s *xorm.Session) (interface{}, error) {
		err := dao_stats.Modify(ctx, s, event.GetProductId(), event.GetPlayerId(), arr)
		if err != nil {
			s.Rollback()
			return nil, err
		}

		return nil, nil
	})
	if err != nil {
		entry.Errorf("db_mgr transaction fail:%v", err)
		return
	}
}

// GetStatsList 获取统计数据
func (s *StatsService) GetStatsList(ctx context.Context, req *statsRpc.GetStatsListReq) (*statsRpc.GetStatsListRsp, error) {
	rsp := &statsRpc.GetStatsListRsp{
		Ret: protox.DefaultResult(),
	}

	list, err := dao_stats.Get(ctx, req.GetProductId(), req.GetPlayerId())
	if err != nil {
		return nil, err
	}

	rtnList := make([]*commonPB.StatInfo, 0)
	for _, one := range list {
		rtnList = append(rtnList, one.ToProto())
	}

	rsp.List = rtnList
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp, nil
}

// EventRuleDo 事件规则处理
func (s *StatsService) EventRuleDo(ctx context.Context, playerId uint64, event *commonPB.EventCommon) {
	entry := logx.NewLogEntry(ctx)
	_ = entry

	bigCfg := cmodel.GetAllStatsRule()

	// 构造变化数据
	arr, err := l_stats.ScanRuleCheck(event, bigCfg)
	if err != nil {
		entry.Errorf("scan cfg err %v", err)
		return
	}

	if len(arr) == 0 {
		return
	}

	err = repository.UpdatePlayerStatsRule(ctx, playerId, arr)
	if err != nil {
		entry.Errorf("update player stats rule fail:%v", err)
		return
	}
}

// GetStatsRules 获取统计规则
func (s *StatsService) GetStatsRules(ctx context.Context, req *statsRpc.GetStatsRulesReq) (*statsRpc.GetStatsRulesRsp, error) {
	rsp := &statsRpc.GetStatsRulesRsp{
		Ret: protox.DefaultResult(),
	}

	rules, err := l_stats.GetStatsRules(ctx, req.GetPlayerId(), req.GetTyp(), req.GetField(), req.GetTarget(), req.GetAddType())
	if err != nil {
		return nil, err
	}

	rtnList := make([]*commonPB.StatsRuleInfo, 0)
	for _, one := range rules {
		rtnList = append(rtnList, one.ToProto())
	}

	rsp.List = rtnList
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp, nil
}
