 go test -bench=.
goos: darwin
goarch: amd64
pkg: git.keepfancy.xyz/back-end/frameworks/core/pkg/mysql/benchmarks
BenchmarkMaxOpenConns1-8              	    4142	    262257 ns/op
BenchmarkMaxOpenConns2-8              	    8076	    148712 ns/op
BenchmarkMaxOpenConns5-8              	   13231	     91225 ns/op
BenchmarkMaxOpenConns10-8             	   16514	    108834 ns/op
BenchmarkMaxOpenConnsUnlimited-8      	   16228	     92739 ns/op
BenchmarkMaxIdleConnsNone-8           	    4528	   6550058 ns/op
BenchmarkMaxIdleConns1-8              	    8866	    194203 ns/op
BenchmarkMaxIdleConns2-8              	   15841	     85715 ns/op
BenchmarkMaxIdleConns5-8              	   17142	     65611 ns/op
BenchmarkMaxIdleConns10-8             	   17332	     65225 ns/op
BenchmarkConnMaxLifetimeUnlimited-8   	   16034	     88290 ns/op
BenchmarkConnMaxLifetime1000-8        	   15781	     89745 ns/op
BenchmarkConnMaxLifetime500-8         	   10000	    116800 ns/op
BenchmarkConnMaxLifetime200-8         	   14071	     74460 ns/op
BenchmarkConnMaxLifetime100-8         	   10000	    127426 ns/op
PASS
ok  	git.keepfancy.xyz/back-end/frameworks/core/pkg/mysql/benchmarks	55.782s