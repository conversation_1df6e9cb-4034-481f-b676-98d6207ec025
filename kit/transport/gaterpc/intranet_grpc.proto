syntax = "proto3";

package transport;

option go_package = "./;intranet_grpc";

message Header {
  uint32 msg_id     = 1;  // msg_id 消息ID
  string client_ip  = 2;  // 客户端 ip
  string protocols  = 3;  // 协议格式 json|pb
}

// 客户端消息
message ClientMessage {
  uint64 player_id    = 1;  // 玩家 ID
  Header header       = 2;  // 消息头
  bytes  request_data = 3;  // 消息内容
}

// ResponseMessage 回复消息
message ResponseMessage {
  Header header = 1;  // 消息头
  bytes  body   = 2;  // 消息体
}

// 处理结果
message HandleResult {
  repeated ResponseMessage responses = 1; // 回复数据， 长度为空时则不需要回复
}

// 转换操作，客户端消息通过网关转发到相应服务
service MessageHandler {
  rpc HandleClientMessage(ClientMessage) returns (HandleResult) {}
}

// 消息发送请求
message SendMessageRequest {
  repeated uint64 player_id = 1;  // 玩家 ID 列表
  Header   header           = 2;  // 消息头
  bytes    data             = 3;  // 消息内容
}

// 消息发送结果
message SendMessageResult {
  bool ok = 1; // 是否发送成功
}


// 广播消息
message BroadcastMsgRequest {
  Header header        = 1;  // 消息头
  bytes  data          = 2;  // 消息内容
  bool   not_rpc_other = 3;  // 不需要rpc调用其他网关广播
}

message BroadcastMsgResult {
  bool ok = 1; // 是否发送成功
}

// 消息发送器 服务通过网关下发数据到客户端
service MessageSender {
  rpc SendMessage(SendMessageRequest) returns (SendMessageResult) {}
  rpc BroadcastMessage(BroadcastMsgRequest) returns (BroadcastMsgResult) {}
}
