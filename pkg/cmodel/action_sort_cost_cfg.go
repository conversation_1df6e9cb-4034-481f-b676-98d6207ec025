// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ActionSortCost struct {
	Id               int64 `json:"id"`
	FakeBasicProb    int32 `json:"fakeBasicProb"`
	FakeDecreaseProb int32 `json:"fakeDecreaseProb"`
}

var lockActionSortCost sync.RWMutex
var storeActionSortCost sync.Map
var strActionSortCost string = "action_sort_cost"

func InitActionSortCostCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strActionSortCost, watchActionSortCostFunc)
	return LoadAllActionSortCostCfg()
}

func fixKeyActionSortCost(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strActionSortCost)
}
func watchActionSortCostFunc(key string, js string) {
	store, ok := storeActionSortCost.Load(key)
	if !ok {
		store = &ActionSortCost{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeActionSortCost.Store(key, store)
}

func GetActionSortCost(option ...consulconfig.Option) *ActionSortCost {
	fitKey := fixKeyActionSortCost(option...)
	store, ok := storeActionSortCost.Load(fitKey)
	if ok {
		tblActionSortCost, ok := store.(*ActionSortCost)
		if ok {
			return tblActionSortCost
		}
	}
	lockActionSortCost.Lock()
	defer lockActionSortCost.Unlock()
	store, ok = storeActionSortCost.Load(fitKey)
	if ok {
		tblActionSortCost, ok := store.(*ActionSortCost)
		if ok {
			return tblActionSortCost
		}
	}
	tblActionSortCost := &ActionSortCost{}
	action_sort_cost_str, err := consulconfig.GetInstance().GetConfig(strActionSortCost, option...)
	if action_sort_cost_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(action_sort_cost_str), &tblActionSortCost)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strActionSortCost, errUnmarshal, action_sort_cost_str)
		return nil
	}
	storeActionSortCost.Store(fitKey, tblActionSortCost)
	return tblActionSortCost
}

func LoadAllActionSortCostCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strActionSortCost, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ActionSortCost", successChannels)
	return nil
}
