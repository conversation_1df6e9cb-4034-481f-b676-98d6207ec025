// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type AppResourceInfo struct {
	Id          int64  `json:"id"`
	ConfigMd5   string `json:"configMd5"`
	RemoteMd5   string `json:"remoteMd5"`
	RemoteForce bool   `json:"remoteForce"`
	PatchForce  bool   `json:"patchForce"`
}

var lockAppResourceInfo sync.RWMutex
var storeAppResourceInfo sync.Map
var strAppResourceInfo string = "app_resource_info"

func InitAppResourceInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strAppResourceInfo, watchAppResourceInfoFunc)
	return LoadAllAppResourceInfoCfg()
}

func fixKeyAppResourceInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strAppResourceInfo)
}
func watchAppResourceInfoFunc(key string, js string) {
	store, ok := storeAppResourceInfo.Load(key)
	if !ok {
		store = &AppResourceInfo{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeAppResourceInfo.Store(key, store)
}

func GetAppResourceInfo(option ...consulconfig.Option) *AppResourceInfo {
	fitKey := fixKeyAppResourceInfo(option...)
	store, ok := storeAppResourceInfo.Load(fitKey)
	if ok {
		tblAppResourceInfo, ok := store.(*AppResourceInfo)
		if ok {
			return tblAppResourceInfo
		}
	}
	lockAppResourceInfo.Lock()
	defer lockAppResourceInfo.Unlock()
	store, ok = storeAppResourceInfo.Load(fitKey)
	if ok {
		tblAppResourceInfo, ok := store.(*AppResourceInfo)
		if ok {
			return tblAppResourceInfo
		}
	}
	tblAppResourceInfo := &AppResourceInfo{}
	app_resource_info_str, err := consulconfig.GetInstance().GetConfig(strAppResourceInfo, option...)
	if app_resource_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(app_resource_info_str), &tblAppResourceInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strAppResourceInfo, errUnmarshal, app_resource_info_str)
		return nil
	}
	storeAppResourceInfo.Store(fitKey, tblAppResourceInfo)
	return tblAppResourceInfo
}

func LoadAllAppResourceInfoCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strAppResourceInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "AppResourceInfo", successChannels)
	return nil
}
