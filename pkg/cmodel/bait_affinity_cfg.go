// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BaitAffinity struct {
	Id             int64   `json:"id"`
	BaitCoeffGroup int64   `json:"baitCoeffGroup"`
	BaitId         int64   `json:"baitId"`
	Coeff          float64 `json:"coeff"`
}

var lockBaitAffinity sync.RWMutex
var storeBaitAffinity sync.Map
var strBaitAffinity string = "bait_affinity"

func InitBaitAffinityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBaitAffinity, watchBaitAffinityFunc)
	return LoadAllBaitAffinityCfg()
}

func fixKeyBaitAffinity(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBaitAffinity)
}
func watchBaitAffinityFunc(key string, js string) {
	mapBaitAffinity := make(map[int64]*BaitAffinity)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBaitAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBaitAffinity.Store(key, mapBaitAffinity)
}

func GetAllBaitAffinity(option ...consulconfig.Option) map[int64]*BaitAffinity {
	fitKey := fixKeyBaitAffinity(option...)
	store, ok := storeBaitAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BaitAffinity)
		if ok {
			return storeMap
		}
	}
	lockBaitAffinity.Lock()
	defer lockBaitAffinity.Unlock()
	store, ok = storeBaitAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BaitAffinity)
		if ok {
			return storeMap
		}
	}
	tblBaitAffinity := make(map[int64]*BaitAffinity)
	bait_affinity_str, err := consulconfig.GetInstance().GetConfig(strBaitAffinity, option...)
	if bait_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(bait_affinity_str), &tblBaitAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "bait_affinity", errUnmarshal)
		return nil
	}
	storeBaitAffinity.Store(fitKey, tblBaitAffinity)
	return tblBaitAffinity
}

func GetBaitAffinity(id int64, option ...consulconfig.Option) *BaitAffinity {
	fitKey := fixKeyBaitAffinity(option...)
	store, ok := storeBaitAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BaitAffinity)
		if ok {
			return storeMap[id]
		}
	}
	lockBaitAffinity.Lock()
	defer lockBaitAffinity.Unlock()
	store, ok = storeBaitAffinity.Load(fitKey)
	if ok {
		tblBaitAffinity, ok := store.(*BaitAffinity)
		if ok {
			return tblBaitAffinity
		}
	}
	tblBaitAffinity := make(map[int64]*BaitAffinity)
	bait_affinity_str, err := consulconfig.GetInstance().GetConfig(strBaitAffinity, option...)
	if bait_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(bait_affinity_str), &tblBaitAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "bait_affinity", errUnmarshal)
		return nil
	}
	storeBaitAffinity.Store(fitKey, tblBaitAffinity)
	return tblBaitAffinity[id]
}

func LoadAllBaitAffinityCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBaitAffinity, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BaitAffinity", successChannels)
	return nil
}
