// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishConst struct {
	Id                 int64 `json:"id"`
	FishFreshnessDecay int32 `json:"fishFreshnessDecay"`
	FishPriceDecay     int32 `json:"fishPriceDecay"`
	FishInsuredPer     int32 `json:"fishInsuredPer"`
}

var lockBasicFishConst sync.RWMutex
var storeBasicFishConst sync.Map
var strBasicFishConst string = "basic_fish_Const"

func InitBasicFishConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishConst, watchBasicFishConstFunc)
	return LoadAllBasicFishConstCfg()
}

func fixKeyBasicFishConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishConst)
}
func watchBasicFishConstFunc(key string, js string) {
	store, ok := storeBasicFishConst.Load(key)
	if !ok {
		store = &BasicFishConst{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishConst.Store(key, store)
}

func GetBasicFishConst(option ...consulconfig.Option) *BasicFishConst {
	fitKey := fixKeyBasicFishConst(option...)
	store, ok := storeBasicFishConst.Load(fitKey)
	if ok {
		tblBasicFishConst, ok := store.(*BasicFishConst)
		if ok {
			return tblBasicFishConst
		}
	}
	lockBasicFishConst.Lock()
	defer lockBasicFishConst.Unlock()
	store, ok = storeBasicFishConst.Load(fitKey)
	if ok {
		tblBasicFishConst, ok := store.(*BasicFishConst)
		if ok {
			return tblBasicFishConst
		}
	}
	tblBasicFishConst := &BasicFishConst{}
	basic_fish_Const_str, err := consulconfig.GetInstance().GetConfig(strBasicFishConst, option...)
	if basic_fish_Const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_Const_str), &tblBasicFishConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strBasicFishConst, errUnmarshal, basic_fish_Const_str)
		return nil
	}
	storeBasicFishConst.Store(fitKey, tblBasicFishConst)
	return tblBasicFishConst
}

func LoadAllBasicFishConstCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBasicFishConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishConst", successChannels)
	return nil
}
