// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishHabitatWater struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	WaterType   int32  `json:"waterType"`
	Description string `json:"description"`
}

var lockBasicFishHabitatWater sync.RWMutex
var storeBasicFishHabitatWater sync.Map
var strBasicFishHabitatWater string = "basic_fish_habitat_water"

func InitBasicFishHabitatWaterCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishHabitatWater, watchBasicFishHabitatWaterFunc)
	return LoadAllBasicFishHabitatWaterCfg()
}

func fixKeyBasicFishHabitatWater(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishHabitatWater)
}
func watchBasicFishHabitatWaterFunc(key string, js string) {
	mapBasicFishHabitatWater := make(map[int64]*BasicFishHabitatWater)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBasicFishHabitatWater)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishHabitatWater.Store(key, mapBasicFishHabitatWater)
}

func GetAllBasicFishHabitatWater(option ...consulconfig.Option) map[int64]*BasicFishHabitatWater {
	fitKey := fixKeyBasicFishHabitatWater(option...)
	store, ok := storeBasicFishHabitatWater.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishHabitatWater)
		if ok {
			return storeMap
		}
	}
	lockBasicFishHabitatWater.Lock()
	defer lockBasicFishHabitatWater.Unlock()
	store, ok = storeBasicFishHabitatWater.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishHabitatWater)
		if ok {
			return storeMap
		}
	}
	tblBasicFishHabitatWater := make(map[int64]*BasicFishHabitatWater)
	basic_fish_habitat_water_str, err := consulconfig.GetInstance().GetConfig(strBasicFishHabitatWater, option...)
	if basic_fish_habitat_water_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_habitat_water_str), &tblBasicFishHabitatWater)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_habitat_water", errUnmarshal)
		return nil
	}
	storeBasicFishHabitatWater.Store(fitKey, tblBasicFishHabitatWater)
	return tblBasicFishHabitatWater
}

func GetBasicFishHabitatWater(id int64, option ...consulconfig.Option) *BasicFishHabitatWater {
	fitKey := fixKeyBasicFishHabitatWater(option...)
	store, ok := storeBasicFishHabitatWater.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishHabitatWater)
		if ok {
			return storeMap[id]
		}
	}
	lockBasicFishHabitatWater.Lock()
	defer lockBasicFishHabitatWater.Unlock()
	store, ok = storeBasicFishHabitatWater.Load(fitKey)
	if ok {
		tblBasicFishHabitatWater, ok := store.(*BasicFishHabitatWater)
		if ok {
			return tblBasicFishHabitatWater
		}
	}
	tblBasicFishHabitatWater := make(map[int64]*BasicFishHabitatWater)
	basic_fish_habitat_water_str, err := consulconfig.GetInstance().GetConfig(strBasicFishHabitatWater, option...)
	if basic_fish_habitat_water_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_habitat_water_str), &tblBasicFishHabitatWater)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_habitat_water", errUnmarshal)
		return nil
	}
	storeBasicFishHabitatWater.Store(fitKey, tblBasicFishHabitatWater)
	return tblBasicFishHabitatWater[id]
}

func LoadAllBasicFishHabitatWaterCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBasicFishHabitatWater, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishHabitatWater", successChannels)
	return nil
}
