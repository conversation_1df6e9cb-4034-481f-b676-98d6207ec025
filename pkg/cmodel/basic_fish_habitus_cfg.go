// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishHabitus struct {
	Id                       int64   `json:"id"`
	Name                     string  `json:"name"`
	Description              string  `json:"description"`
	Habitat                  int32   `json:"habitat"`
	Diet                     int32   `json:"diet"`
	BreedingSeason           string  `json:"breedingSeason"`
	TemperatureRange         string  `json:"temperatureRange"`
	TemperatureAffectedRatio float64 `json:"temperatureAffectedRatio"`
	PreferredWeather         string  `json:"preferredWeather"`
	WeatherAffectedRatio     float64 `json:"weatherAffectedRatio"`
	WaterDepth               int32   `json:"waterDepth"`
	WaterPlants              float64 `json:"waterPlants"`
}

var lockBasicFishHabitus sync.RWMutex
var storeBasicFishHabitus sync.Map
var strBasicFishHabitus string = "basic_fish_habitus"

func InitBasicFishHabitusCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishHabitus, watchBasicFishHabitusFunc)
	return LoadAllBasicFishHabitusCfg()
}

func fixKeyBasicFishHabitus(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishHabitus)
}
func watchBasicFishHabitusFunc(key string, js string) {
	mapBasicFishHabitus := make(map[int64]*BasicFishHabitus)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBasicFishHabitus)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishHabitus.Store(key, mapBasicFishHabitus)
}

func GetAllBasicFishHabitus(option ...consulconfig.Option) map[int64]*BasicFishHabitus {
	fitKey := fixKeyBasicFishHabitus(option...)
	store, ok := storeBasicFishHabitus.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishHabitus)
		if ok {
			return storeMap
		}
	}
	lockBasicFishHabitus.Lock()
	defer lockBasicFishHabitus.Unlock()
	store, ok = storeBasicFishHabitus.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishHabitus)
		if ok {
			return storeMap
		}
	}
	tblBasicFishHabitus := make(map[int64]*BasicFishHabitus)
	basic_fish_habitus_str, err := consulconfig.GetInstance().GetConfig(strBasicFishHabitus, option...)
	if basic_fish_habitus_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_habitus_str), &tblBasicFishHabitus)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_habitus", errUnmarshal)
		return nil
	}
	storeBasicFishHabitus.Store(fitKey, tblBasicFishHabitus)
	return tblBasicFishHabitus
}

func GetBasicFishHabitus(id int64, option ...consulconfig.Option) *BasicFishHabitus {
	fitKey := fixKeyBasicFishHabitus(option...)
	store, ok := storeBasicFishHabitus.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishHabitus)
		if ok {
			return storeMap[id]
		}
	}
	lockBasicFishHabitus.Lock()
	defer lockBasicFishHabitus.Unlock()
	store, ok = storeBasicFishHabitus.Load(fitKey)
	if ok {
		tblBasicFishHabitus, ok := store.(*BasicFishHabitus)
		if ok {
			return tblBasicFishHabitus
		}
	}
	tblBasicFishHabitus := make(map[int64]*BasicFishHabitus)
	basic_fish_habitus_str, err := consulconfig.GetInstance().GetConfig(strBasicFishHabitus, option...)
	if basic_fish_habitus_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_habitus_str), &tblBasicFishHabitus)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_habitus", errUnmarshal)
		return nil
	}
	storeBasicFishHabitus.Store(fitKey, tblBasicFishHabitus)
	return tblBasicFishHabitus[id]
}

func LoadAllBasicFishHabitusCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBasicFishHabitus, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishHabitus", successChannels)
	return nil
}
