// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishQualityFishLen struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type BasicFishQualityFishWeight struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type BasicFishQuality struct {
	Id                     int64                      `json:"id"`
	Name                   string                     `json:"name"`
	NameLanguage           int64                      `json:"nameLanguage"`
	DescLanuage            int64                      `json:"descLanuage"`
	ArtId                  string                     `json:"artId"`
	ModelId                string                     `json:"modelId"`
	Character              int32                      `json:"character"`
	Species                int32                      `json:"species"`
	SizeCategory           int32                      `json:"sizeCategory"`
	VolumeExponent         float64                    `json:"volumeExponent"`
	MassFactor             float64                    `json:"massFactor"`
	FishLen                BasicFishQualityFishLen    `json:"fishLen"`
	FishWeight             BasicFishQualityFishWeight `json:"fishWeight"`
	Exp                    int32                      `json:"exp"`
	BasicRewardItem        int64                      `json:"basicRewardItem"`
	BasicRewardNum         int32                      `json:"basicRewardNum"`
	ExtraGiftId            int64                      `json:"extraGiftId"`
	IsKeyFish              bool                       `json:"isKeyFish"`
	IsDamaged              bool                       `json:"isDamaged"`
	DamageDescription      string                     `json:"damageDescription"`
	Desc                   string                     `json:"desc"`
	ConditionalHookTimeout int32                      `json:"conditionalHookTimeout"`
	ConditionalHookSpeed   int32                      `json:"conditionalHookSpeed"`
}

var lockBasicFishQuality sync.RWMutex
var storeBasicFishQuality sync.Map
var strBasicFishQuality string = "basic_fish_quality"

func InitBasicFishQualityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishQuality, watchBasicFishQualityFunc)
	return LoadAllBasicFishQualityCfg()
}

func fixKeyBasicFishQuality(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishQuality)
}
func watchBasicFishQualityFunc(key string, js string) {
	mapBasicFishQuality := make(map[int64]*BasicFishQuality)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBasicFishQuality)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishQuality.Store(key, mapBasicFishQuality)
}

func GetAllBasicFishQuality(option ...consulconfig.Option) map[int64]*BasicFishQuality {
	fitKey := fixKeyBasicFishQuality(option...)
	store, ok := storeBasicFishQuality.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishQuality)
		if ok {
			return storeMap
		}
	}
	lockBasicFishQuality.Lock()
	defer lockBasicFishQuality.Unlock()
	store, ok = storeBasicFishQuality.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishQuality)
		if ok {
			return storeMap
		}
	}
	tblBasicFishQuality := make(map[int64]*BasicFishQuality)
	basic_fish_quality_str, err := consulconfig.GetInstance().GetConfig(strBasicFishQuality, option...)
	if basic_fish_quality_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_quality_str), &tblBasicFishQuality)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_quality", errUnmarshal)
		return nil
	}
	storeBasicFishQuality.Store(fitKey, tblBasicFishQuality)
	return tblBasicFishQuality
}

func GetBasicFishQuality(id int64, option ...consulconfig.Option) *BasicFishQuality {
	fitKey := fixKeyBasicFishQuality(option...)
	store, ok := storeBasicFishQuality.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishQuality)
		if ok {
			return storeMap[id]
		}
	}
	lockBasicFishQuality.Lock()
	defer lockBasicFishQuality.Unlock()
	store, ok = storeBasicFishQuality.Load(fitKey)
	if ok {
		tblBasicFishQuality, ok := store.(*BasicFishQuality)
		if ok {
			return tblBasicFishQuality
		}
	}
	tblBasicFishQuality := make(map[int64]*BasicFishQuality)
	basic_fish_quality_str, err := consulconfig.GetInstance().GetConfig(strBasicFishQuality, option...)
	if basic_fish_quality_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_quality_str), &tblBasicFishQuality)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_quality", errUnmarshal)
		return nil
	}
	storeBasicFishQuality.Store(fitKey, tblBasicFishQuality)
	return tblBasicFishQuality[id]
}

func LoadAllBasicFishQualityCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBasicFishQuality, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishQuality", successChannels)
	return nil
}
