// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BlockStrategy struct {
	IpBlockEnable bool    `json:"ipBlockEnable"`
	WhiteOpenType []int32 `json:"whiteOpenType"`
}

var lockBlockStrategy sync.RWMutex
var storeBlockStrategy sync.Map
var strBlockStrategy string = "block_strategy"

func InitBlockStrategyCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBlockStrategy, watchBlockStrategyFunc)
	return LoadAllBlockStrategyCfg()
}

func fixKeyBlockStrategy(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBlockStrategy)
}
func watchBlockStrategyFunc(key string, js string) {
	store, ok := storeBlockStrategy.Load(key)
	if !ok {
		store = &BlockStrategy{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBlockStrategy.Store(key, store)
}

func GetBlockStrategy(option ...consulconfig.Option) *BlockStrategy {
	fitKey := fixKeyBlockStrategy(option...)
	store, ok := storeBlockStrategy.Load(fitKey)
	if ok {
		tblBlockStrategy, ok := store.(*BlockStrategy)
		if ok {
			return tblBlockStrategy
		}
	}
	lockBlockStrategy.Lock()
	defer lockBlockStrategy.Unlock()
	store, ok = storeBlockStrategy.Load(fitKey)
	if ok {
		tblBlockStrategy, ok := store.(*BlockStrategy)
		if ok {
			return tblBlockStrategy
		}
	}
	tblBlockStrategy := &BlockStrategy{}
	block_strategy_str, err := consulconfig.GetInstance().GetConfig(strBlockStrategy, option...)
	if block_strategy_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(block_strategy_str), &tblBlockStrategy)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strBlockStrategy, errUnmarshal, block_strategy_str)
		return nil
	}
	storeBlockStrategy.Store(fitKey, tblBlockStrategy)
	return tblBlockStrategy
}

func LoadAllBlockStrategyCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBlockStrategy, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BlockStrategy", successChannels)
	return nil
}
