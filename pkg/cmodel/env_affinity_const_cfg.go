// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type EnvAffinityConst struct {
	Id                     int64   `json:"id"`
	TempToleranceWidth     float64 `json:"tempToleranceWidth"`
	WaterBottomLayerHeight float64 `json:"waterBottomLayerHeight"`
	WaterBottomLayerRatio  float64 `json:"waterBottomLayerRatio"`
	WaterTopLayerHeight    float64 `json:"waterTopLayerHeight"`
	WaterTopLayerRatio     float64 `json:"waterTopLayerRatio"`
	BoundingBoxRadiatorX   float64 `json:"boundingBoxRadiatorX"`
	BoundingBoxRadiatorY   float64 `json:"boundingBoxRadiatorY"`
	BoundingBoxRadiatorZ   float64 `json:"boundingBoxRadiatorZ"`
}

var lockEnvAffinityConst sync.RWMutex
var storeEnvAffinityConst sync.Map
var strEnvAffinityConst string = "env_affinity_const"

func InitEnvAffinityConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strEnvAffinityConst, watchEnvAffinityConstFunc)
	return LoadAllEnvAffinityConstCfg()
}

func fixKeyEnvAffinityConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strEnvAffinityConst)
}
func watchEnvAffinityConstFunc(key string, js string) {
	store, ok := storeEnvAffinityConst.Load(key)
	if !ok {
		store = &EnvAffinityConst{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeEnvAffinityConst.Store(key, store)
}

func GetEnvAffinityConst(option ...consulconfig.Option) *EnvAffinityConst {
	fitKey := fixKeyEnvAffinityConst(option...)
	store, ok := storeEnvAffinityConst.Load(fitKey)
	if ok {
		tblEnvAffinityConst, ok := store.(*EnvAffinityConst)
		if ok {
			return tblEnvAffinityConst
		}
	}
	lockEnvAffinityConst.Lock()
	defer lockEnvAffinityConst.Unlock()
	store, ok = storeEnvAffinityConst.Load(fitKey)
	if ok {
		tblEnvAffinityConst, ok := store.(*EnvAffinityConst)
		if ok {
			return tblEnvAffinityConst
		}
	}
	tblEnvAffinityConst := &EnvAffinityConst{}
	env_affinity_const_str, err := consulconfig.GetInstance().GetConfig(strEnvAffinityConst, option...)
	if env_affinity_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(env_affinity_const_str), &tblEnvAffinityConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strEnvAffinityConst, errUnmarshal, env_affinity_const_str)
		return nil
	}
	storeEnvAffinityConst.Store(fitKey, tblEnvAffinityConst)
	return tblEnvAffinityConst
}

func LoadAllEnvAffinityConstCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strEnvAffinityConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "EnvAffinityConst", successChannels)
	return nil
}
