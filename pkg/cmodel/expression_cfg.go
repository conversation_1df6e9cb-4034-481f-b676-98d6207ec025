// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Expression struct {
	Id           int32  `json:"id"`
	Name         string `json:"name"`
	ActionId     int32  `json:"actionId"`
	Ripple       int32  `json:"ripple"`
	SplashType   int32  `json:"splashType"`
	UnhookedType int32  `json:"unhookedType"`
}

var lockExpression sync.RWMutex
var storeExpression sync.Map
var strExpression string = "expression"

func InitExpressionCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strExpression, watchExpressionFunc)
	return LoadAllExpressionCfg()
}

func fixKeyExpression(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strExpression)
}
func watchExpressionFunc(key string, js string) {
	mapExpression := make(map[int64]*Expression)
	errUnmarshal := json.Unmarshal([]byte(js), &mapExpression)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeExpression.Store(key, mapExpression)
}

func GetAllExpression(option ...consulconfig.Option) map[int64]*Expression {
	fitKey := fixKeyExpression(option...)
	store, ok := storeExpression.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Expression)
		if ok {
			return storeMap
		}
	}
	lockExpression.Lock()
	defer lockExpression.Unlock()
	store, ok = storeExpression.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Expression)
		if ok {
			return storeMap
		}
	}
	tblExpression := make(map[int64]*Expression)
	expression_str, err := consulconfig.GetInstance().GetConfig(strExpression, option...)
	if expression_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(expression_str), &tblExpression)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "expression", errUnmarshal)
		return nil
	}
	storeExpression.Store(fitKey, tblExpression)
	return tblExpression
}

func GetExpression(id int64, option ...consulconfig.Option) *Expression {
	fitKey := fixKeyExpression(option...)
	store, ok := storeExpression.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Expression)
		if ok {
			return storeMap[id]
		}
	}
	lockExpression.Lock()
	defer lockExpression.Unlock()
	store, ok = storeExpression.Load(fitKey)
	if ok {
		tblExpression, ok := store.(*Expression)
		if ok {
			return tblExpression
		}
	}
	tblExpression := make(map[int64]*Expression)
	expression_str, err := consulconfig.GetInstance().GetConfig(strExpression, option...)
	if expression_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(expression_str), &tblExpression)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "expression", errUnmarshal)
		return nil
	}
	storeExpression.Store(fitKey, tblExpression)
	return tblExpression[id]
}

func LoadAllExpressionCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strExpression, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Expression", successChannels)
	return nil
}
