// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishDistributeConst struct {
	Id int64 `json:"id"`
}

var lockFishDistributeConst sync.RWMutex
var storeFishDistributeConst sync.Map
var strFishDistributeConst string = "fish_distribute_Const"

func InitFishDistributeConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishDistributeConst, watchFishDistributeConstFunc)
	return LoadAllFishDistributeConstCfg()
}

func fixKeyFishDistributeConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishDistributeConst)
}
func watchFishDistributeConstFunc(key string, js string) {
	store, ok := storeFishDistributeConst.Load(key)
	if !ok {
		store = &FishDistributeConst{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishDistributeConst.Store(key, store)
}

func GetFishDistributeConst(option ...consulconfig.Option) *FishDistributeConst {
	fitKey := fixKeyFishDistributeConst(option...)
	store, ok := storeFishDistributeConst.Load(fitKey)
	if ok {
		tblFishDistributeConst, ok := store.(*FishDistributeConst)
		if ok {
			return tblFishDistributeConst
		}
	}
	lockFishDistributeConst.Lock()
	defer lockFishDistributeConst.Unlock()
	store, ok = storeFishDistributeConst.Load(fitKey)
	if ok {
		tblFishDistributeConst, ok := store.(*FishDistributeConst)
		if ok {
			return tblFishDistributeConst
		}
	}
	tblFishDistributeConst := &FishDistributeConst{}
	fish_distribute_Const_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeConst, option...)
	if fish_distribute_Const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_Const_str), &tblFishDistributeConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strFishDistributeConst, errUnmarshal, fish_distribute_Const_str)
		return nil
	}
	storeFishDistributeConst.Store(fitKey, tblFishDistributeConst)
	return tblFishDistributeConst
}

func LoadAllFishDistributeConstCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishDistributeConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishDistributeConst", successChannels)
	return nil
}
