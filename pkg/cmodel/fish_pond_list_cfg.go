// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishPondList struct {
	Id           int64   `json:"id"`
	Name         string  `json:"name"`
	PondName     int64   `json:"pondName"`
	PondImg      string  `json:"pondImg"`
	SpotImg      string  `json:"spotImg"`
	MapId        int32   `json:"mapId"`
	FishStockId  int64   `json:"fishStockId"`
	EntryLevel   int32   `json:"entryLevel"`
	EntryItem    int64   `json:"entryItem"`
	EntryFee     int64   `json:"entryFee"`
	OpenSpot     []int32 `json:"OpenSpot"`
	HypolimnionT int32   `json:"hypolimnionT"`
	Mark         string  `json:"mark"`
}

var lockFishPondList sync.RWMutex
var storeFishPondList sync.Map
var strFishPondList string = "fish_pond_list"

func InitFishPondListCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishPondList, watchFishPondListFunc)
	return LoadAllFishPondListCfg()
}

func fixKeyFishPondList(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishPondList)
}
func watchFishPondListFunc(key string, js string) {
	mapFishPondList := make(map[int64]*FishPondList)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishPondList)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishPondList.Store(key, mapFishPondList)
}

func GetAllFishPondList(option ...consulconfig.Option) map[int64]*FishPondList {
	fitKey := fixKeyFishPondList(option...)
	store, ok := storeFishPondList.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishPondList)
		if ok {
			return storeMap
		}
	}
	lockFishPondList.Lock()
	defer lockFishPondList.Unlock()
	store, ok = storeFishPondList.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishPondList)
		if ok {
			return storeMap
		}
	}
	tblFishPondList := make(map[int64]*FishPondList)
	fish_pond_list_str, err := consulconfig.GetInstance().GetConfig(strFishPondList, option...)
	if fish_pond_list_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_pond_list_str), &tblFishPondList)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_pond_list", errUnmarshal)
		return nil
	}
	storeFishPondList.Store(fitKey, tblFishPondList)
	return tblFishPondList
}

func GetFishPondList(id int64, option ...consulconfig.Option) *FishPondList {
	fitKey := fixKeyFishPondList(option...)
	store, ok := storeFishPondList.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishPondList)
		if ok {
			return storeMap[id]
		}
	}
	lockFishPondList.Lock()
	defer lockFishPondList.Unlock()
	store, ok = storeFishPondList.Load(fitKey)
	if ok {
		tblFishPondList, ok := store.(*FishPondList)
		if ok {
			return tblFishPondList
		}
	}
	tblFishPondList := make(map[int64]*FishPondList)
	fish_pond_list_str, err := consulconfig.GetInstance().GetConfig(strFishPondList, option...)
	if fish_pond_list_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_pond_list_str), &tblFishPondList)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_pond_list", errUnmarshal)
		return nil
	}
	storeFishPondList.Store(fitKey, tblFishPondList)
	return tblFishPondList[id]
}

func LoadAllFishPondListCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishPondList, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishPondList", successChannels)
	return nil
}
