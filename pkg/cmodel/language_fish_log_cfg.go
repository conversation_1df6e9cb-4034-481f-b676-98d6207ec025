// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageFishLog struct {
	Id                int64  `json:"id"`
	Name              string `json:"name"`
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
}

var lockLanguageFishLog sync.RWMutex
var storeLanguageFishLog sync.Map
var strLanguageFishLog string = "language_fish_log"

func InitLanguageFishLogCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageFishLog, watchLanguageFishLogFunc)
	return LoadAllLanguageFishLogCfg()
}

func fixKeyLanguageFishLog(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageFishLog)
}
func watchLanguageFishLogFunc(key string, js string) {
	mapLanguageFishLog := make(map[int64]*LanguageFishLog)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageFishLog)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageFishLog.Store(key, mapLanguageFishLog)
}

func GetAllLanguageFishLog(option ...consulconfig.Option) map[int64]*LanguageFishLog {
	fitKey := fixKeyLanguageFishLog(option...)
	store, ok := storeLanguageFishLog.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageFishLog)
		if ok {
			return storeMap
		}
	}
	lockLanguageFishLog.Lock()
	defer lockLanguageFishLog.Unlock()
	store, ok = storeLanguageFishLog.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageFishLog)
		if ok {
			return storeMap
		}
	}
	tblLanguageFishLog := make(map[int64]*LanguageFishLog)
	language_fish_log_str, err := consulconfig.GetInstance().GetConfig(strLanguageFishLog, option...)
	if language_fish_log_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_fish_log_str), &tblLanguageFishLog)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_fish_log", errUnmarshal)
		return nil
	}
	storeLanguageFishLog.Store(fitKey, tblLanguageFishLog)
	return tblLanguageFishLog
}

func GetLanguageFishLog(id int64, option ...consulconfig.Option) *LanguageFishLog {
	fitKey := fixKeyLanguageFishLog(option...)
	store, ok := storeLanguageFishLog.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageFishLog)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageFishLog.Lock()
	defer lockLanguageFishLog.Unlock()
	store, ok = storeLanguageFishLog.Load(fitKey)
	if ok {
		tblLanguageFishLog, ok := store.(*LanguageFishLog)
		if ok {
			return tblLanguageFishLog
		}
	}
	tblLanguageFishLog := make(map[int64]*LanguageFishLog)
	language_fish_log_str, err := consulconfig.GetInstance().GetConfig(strLanguageFishLog, option...)
	if language_fish_log_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_fish_log_str), &tblLanguageFishLog)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_fish_log", errUnmarshal)
		return nil
	}
	storeLanguageFishLog.Store(fitKey, tblLanguageFishLog)
	return tblLanguageFishLog[id]
}

func LoadAllLanguageFishLogCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLanguageFishLog, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageFishLog", successChannels)
	return nil
}
