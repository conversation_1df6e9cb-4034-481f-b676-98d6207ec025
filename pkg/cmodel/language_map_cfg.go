// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageMapLangName struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguageMapLangDes struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguageMap struct {
	Id       int64               `json:"id"`
	Name     string              `json:"name"`
	LangName LanguageMapLangName `json:"lang_name"`
	LangDes  LanguageMapLangDes  `json:"lang_des"`
}

var lockLanguageMap sync.RWMutex
var storeLanguageMap sync.Map
var strLanguageMap string = "language_map"

func InitLanguageMapCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageMap, watchLanguageMapFunc)
	return LoadAllLanguageMapCfg()
}

func fixKeyLanguageMap(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageMap)
}
func watchLanguageMapFunc(key string, js string) {
	mapLanguageMap := make(map[int64]*LanguageMap)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageMap.Store(key, mapLanguageMap)
}

func GetAllLanguageMap(option ...consulconfig.Option) map[int64]*LanguageMap {
	fitKey := fixKeyLanguageMap(option...)
	store, ok := storeLanguageMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageMap)
		if ok {
			return storeMap
		}
	}
	lockLanguageMap.Lock()
	defer lockLanguageMap.Unlock()
	store, ok = storeLanguageMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageMap)
		if ok {
			return storeMap
		}
	}
	tblLanguageMap := make(map[int64]*LanguageMap)
	language_map_str, err := consulconfig.GetInstance().GetConfig(strLanguageMap, option...)
	if language_map_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_map_str), &tblLanguageMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_map", errUnmarshal)
		return nil
	}
	storeLanguageMap.Store(fitKey, tblLanguageMap)
	return tblLanguageMap
}

func GetLanguageMap(id int64, option ...consulconfig.Option) *LanguageMap {
	fitKey := fixKeyLanguageMap(option...)
	store, ok := storeLanguageMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageMap)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageMap.Lock()
	defer lockLanguageMap.Unlock()
	store, ok = storeLanguageMap.Load(fitKey)
	if ok {
		tblLanguageMap, ok := store.(*LanguageMap)
		if ok {
			return tblLanguageMap
		}
	}
	tblLanguageMap := make(map[int64]*LanguageMap)
	language_map_str, err := consulconfig.GetInstance().GetConfig(strLanguageMap, option...)
	if language_map_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_map_str), &tblLanguageMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_map", errUnmarshal)
		return nil
	}
	storeLanguageMap.Store(fitKey, tblLanguageMap)
	return tblLanguageMap[id]
}

func LoadAllLanguageMapCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLanguageMap, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageMap", successChannels)
	return nil
}
