// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageNoviceSceneLangName struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguageNoviceSceneLangDes struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguageNoviceScene struct {
	Id       int64                       `json:"id"`
	Name     string                      `json:"name"`
	LangName LanguageNoviceSceneLangName `json:"lang_name"`
	LangDes  LanguageNoviceSceneLangDes  `json:"lang_des"`
}

var lockLanguageNoviceScene sync.RWMutex
var storeLanguageNoviceScene sync.Map
var strLanguageNoviceScene string = "language_novice_scene"

func InitLanguageNoviceSceneCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageNoviceScene, watchLanguageNoviceSceneFunc)
	return LoadAllLanguageNoviceSceneCfg()
}

func fixKeyLanguageNoviceScene(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageNoviceScene)
}
func watchLanguageNoviceSceneFunc(key string, js string) {
	mapLanguageNoviceScene := make(map[int64]*LanguageNoviceScene)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageNoviceScene)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageNoviceScene.Store(key, mapLanguageNoviceScene)
}

func GetAllLanguageNoviceScene(option ...consulconfig.Option) map[int64]*LanguageNoviceScene {
	fitKey := fixKeyLanguageNoviceScene(option...)
	store, ok := storeLanguageNoviceScene.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageNoviceScene)
		if ok {
			return storeMap
		}
	}
	lockLanguageNoviceScene.Lock()
	defer lockLanguageNoviceScene.Unlock()
	store, ok = storeLanguageNoviceScene.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageNoviceScene)
		if ok {
			return storeMap
		}
	}
	tblLanguageNoviceScene := make(map[int64]*LanguageNoviceScene)
	language_novice_scene_str, err := consulconfig.GetInstance().GetConfig(strLanguageNoviceScene, option...)
	if language_novice_scene_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_novice_scene_str), &tblLanguageNoviceScene)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_novice_scene", errUnmarshal)
		return nil
	}
	storeLanguageNoviceScene.Store(fitKey, tblLanguageNoviceScene)
	return tblLanguageNoviceScene
}

func GetLanguageNoviceScene(id int64, option ...consulconfig.Option) *LanguageNoviceScene {
	fitKey := fixKeyLanguageNoviceScene(option...)
	store, ok := storeLanguageNoviceScene.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageNoviceScene)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageNoviceScene.Lock()
	defer lockLanguageNoviceScene.Unlock()
	store, ok = storeLanguageNoviceScene.Load(fitKey)
	if ok {
		tblLanguageNoviceScene, ok := store.(*LanguageNoviceScene)
		if ok {
			return tblLanguageNoviceScene
		}
	}
	tblLanguageNoviceScene := make(map[int64]*LanguageNoviceScene)
	language_novice_scene_str, err := consulconfig.GetInstance().GetConfig(strLanguageNoviceScene, option...)
	if language_novice_scene_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_novice_scene_str), &tblLanguageNoviceScene)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_novice_scene", errUnmarshal)
		return nil
	}
	storeLanguageNoviceScene.Store(fitKey, tblLanguageNoviceScene)
	return tblLanguageNoviceScene[id]
}

func LoadAllLanguageNoviceSceneCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLanguageNoviceScene, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageNoviceScene", successChannels)
	return nil
}
