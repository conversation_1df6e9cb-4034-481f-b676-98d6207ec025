// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Leaders struct {
	Id              int64   `json:"id"`
	Brand           int32   `json:"brand"`
	Series          int32   `json:"series"`
	SizeName        string  `json:"sizeName"`
	SeriesDes       string  `json:"seriesDes"`
	SubType         int32   `json:"subType"`
	Name            string  `json:"name"`
	Mark            string  `json:"mark"`
	ArtId           string  `json:"artId"`
	Durability      int32   `json:"durability"`
	DurabilityCoeff int32   `json:"durabilityCoeff"`
	DurabilityTag   int32   `json:"durabilityTag"`
	Diameter        int32   `json:"diameter"`
	Color           string  `json:"color"`
	Opacity         int32   `json:"opacity"`
	Length          int32   `json:"length"`
	Drag            int32   `json:"drag"`
	FrictionRatio   float32 `json:"frictionRatio"`
	Weight          int32   `json:"weight"`
	DistanceCoeff   int32   `json:"distanceCoeff"`
	DistanceTag     int32   `json:"distanceTag"`
	Crypticity      int32   `json:"crypticity"`
	CrypticityTag   int32   `json:"crypticityTag"`
}

var lockLeaders sync.RWMutex
var storeLeaders sync.Map
var strLeaders string = "leaders"

func InitLeadersCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLeaders, watchLeadersFunc)
	return LoadAllLeadersCfg()
}

func fixKeyLeaders(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLeaders)
}
func watchLeadersFunc(key string, js string) {
	mapLeaders := make(map[int64]*Leaders)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLeaders)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLeaders.Store(key, mapLeaders)
}

func GetAllLeaders(option ...consulconfig.Option) map[int64]*Leaders {
	fitKey := fixKeyLeaders(option...)
	store, ok := storeLeaders.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Leaders)
		if ok {
			return storeMap
		}
	}
	lockLeaders.Lock()
	defer lockLeaders.Unlock()
	store, ok = storeLeaders.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Leaders)
		if ok {
			return storeMap
		}
	}
	tblLeaders := make(map[int64]*Leaders)
	leaders_str, err := consulconfig.GetInstance().GetConfig(strLeaders, option...)
	if leaders_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(leaders_str), &tblLeaders)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "leaders", errUnmarshal)
		return nil
	}
	storeLeaders.Store(fitKey, tblLeaders)
	return tblLeaders
}

func GetLeaders(id int64, option ...consulconfig.Option) *Leaders {
	fitKey := fixKeyLeaders(option...)
	store, ok := storeLeaders.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Leaders)
		if ok {
			return storeMap[id]
		}
	}
	lockLeaders.Lock()
	defer lockLeaders.Unlock()
	store, ok = storeLeaders.Load(fitKey)
	if ok {
		tblLeaders, ok := store.(*Leaders)
		if ok {
			return tblLeaders
		}
	}
	tblLeaders := make(map[int64]*Leaders)
	leaders_str, err := consulconfig.GetInstance().GetConfig(strLeaders, option...)
	if leaders_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(leaders_str), &tblLeaders)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "leaders", errUnmarshal)
		return nil
	}
	storeLeaders.Store(fitKey, tblLeaders)
	return tblLeaders[id]
}

func LoadAllLeadersCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLeaders, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Leaders", successChannels)
	return nil
}
