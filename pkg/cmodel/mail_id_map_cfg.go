// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type MailIdMap struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

var lockMailIdMap sync.RWMutex
var storeMailIdMap sync.Map
var strMailIdMap string = "mail_id_map"

func InitMailIdMapCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strMailIdMap, watchMailIdMapFunc)
	return LoadAllMailIdMapCfg()
}

func fixKeyMailIdMap(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strMailIdMap)
}
func watchMailIdMapFunc(key string, js string) {
	mapMailIdMap := make(map[int64]*MailIdMap)
	errUnmarshal := json.Unmarshal([]byte(js), &mapMailIdMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeMailIdMap.Store(key, mapMailIdMap)
}

func GetAllMailIdMap(option ...consulconfig.Option) map[int64]*MailIdMap {
	fitKey := fixKeyMailIdMap(option...)
	store, ok := storeMailIdMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MailIdMap)
		if ok {
			return storeMap
		}
	}
	lockMailIdMap.Lock()
	defer lockMailIdMap.Unlock()
	store, ok = storeMailIdMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MailIdMap)
		if ok {
			return storeMap
		}
	}
	tblMailIdMap := make(map[int64]*MailIdMap)
	mail_id_map_str, err := consulconfig.GetInstance().GetConfig(strMailIdMap, option...)
	if mail_id_map_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(mail_id_map_str), &tblMailIdMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "mail_id_map", errUnmarshal)
		return nil
	}
	storeMailIdMap.Store(fitKey, tblMailIdMap)
	return tblMailIdMap
}

func GetMailIdMap(id int64, option ...consulconfig.Option) *MailIdMap {
	fitKey := fixKeyMailIdMap(option...)
	store, ok := storeMailIdMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MailIdMap)
		if ok {
			return storeMap[id]
		}
	}
	lockMailIdMap.Lock()
	defer lockMailIdMap.Unlock()
	store, ok = storeMailIdMap.Load(fitKey)
	if ok {
		tblMailIdMap, ok := store.(*MailIdMap)
		if ok {
			return tblMailIdMap
		}
	}
	tblMailIdMap := make(map[int64]*MailIdMap)
	mail_id_map_str, err := consulconfig.GetInstance().GetConfig(strMailIdMap, option...)
	if mail_id_map_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(mail_id_map_str), &tblMailIdMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "mail_id_map", errUnmarshal)
		return nil
	}
	storeMailIdMap.Store(fitKey, tblMailIdMap)
	return tblMailIdMap[id]
}

func LoadAllMailIdMapCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strMailIdMap, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "MailIdMap", successChannels)
	return nil
}
