// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type MailKeyConst struct {
	Rankid   int64 `json:"rankid"`
	Ranksort int64 `json:"ranksort"`
}

var lockMailKeyConst sync.RWMutex
var storeMailKeyConst sync.Map
var strMailKeyConst string = "mail_key_const"

func InitMailKeyConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strMailKeyConst, watchMailKeyConstFunc)
	return LoadAllMailKeyConstCfg()
}

func fixKeyMailKeyConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strMailKeyConst)
}
func watchMailKeyConstFunc(key string, js string) {
	store, ok := storeMailKeyConst.Load(key)
	if !ok {
		store = &MailKeyConst{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeMailKeyConst.Store(key, store)
}

func GetMailKeyConst(option ...consulconfig.Option) *MailKeyConst {
	fitKey := fixKeyMailKeyConst(option...)
	store, ok := storeMailKeyConst.Load(fitKey)
	if ok {
		tblMailKeyConst, ok := store.(*MailKeyConst)
		if ok {
			return tblMailKeyConst
		}
	}
	lockMailKeyConst.Lock()
	defer lockMailKeyConst.Unlock()
	store, ok = storeMailKeyConst.Load(fitKey)
	if ok {
		tblMailKeyConst, ok := store.(*MailKeyConst)
		if ok {
			return tblMailKeyConst
		}
	}
	tblMailKeyConst := &MailKeyConst{}
	mail_key_const_str, err := consulconfig.GetInstance().GetConfig(strMailKeyConst, option...)
	if mail_key_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(mail_key_const_str), &tblMailKeyConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strMailKeyConst, errUnmarshal, mail_key_const_str)
		return nil
	}
	storeMailKeyConst.Store(fitKey, tblMailKeyConst)
	return tblMailKeyConst
}

func LoadAllMailKeyConstCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strMailKeyConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "MailKeyConst", successChannels)
	return nil
}
