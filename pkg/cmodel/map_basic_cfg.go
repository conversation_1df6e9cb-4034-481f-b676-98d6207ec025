// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type MapBasic struct {
	Id         int64   `json:"id"`
	Name       string  `json:"name"`
	Desc       int64   `json:"desc"`
	SizeX      int64   `json:"sizeX"`
	SizeY      int64   `json:"sizeY"`
	SpotInfoId int64   `json:"spotInfoId"`
	WorldX     int32   `json:"worldX"`
	WorldY     int32   `json:"worldY"`
	WaterMaxZ  float64 `json:"waterMaxZ"`
	WaterMinZ  float64 `json:"waterMinZ"`
	Mark       string  `json:"mark"`
}

var lockMapBasic sync.RWMutex
var storeMapBasic sync.Map
var strMapBasic string = "map_basic"

func InitMapBasicCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strMapBasic, watchMapBasicFunc)
	return LoadAllMapBasicCfg()
}

func fixKeyMapBasic(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strMapBasic)
}
func watchMapBasicFunc(key string, js string) {
	mapMapBasic := make(map[int64]*MapBasic)
	errUnmarshal := json.Unmarshal([]byte(js), &mapMapBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeMapBasic.Store(key, mapMapBasic)
}

func GetAllMapBasic(option ...consulconfig.Option) map[int64]*MapBasic {
	fitKey := fixKeyMapBasic(option...)
	store, ok := storeMapBasic.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapBasic)
		if ok {
			return storeMap
		}
	}
	lockMapBasic.Lock()
	defer lockMapBasic.Unlock()
	store, ok = storeMapBasic.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapBasic)
		if ok {
			return storeMap
		}
	}
	tblMapBasic := make(map[int64]*MapBasic)
	map_basic_str, err := consulconfig.GetInstance().GetConfig(strMapBasic, option...)
	if map_basic_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(map_basic_str), &tblMapBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "map_basic", errUnmarshal)
		return nil
	}
	storeMapBasic.Store(fitKey, tblMapBasic)
	return tblMapBasic
}

func GetMapBasic(id int64, option ...consulconfig.Option) *MapBasic {
	fitKey := fixKeyMapBasic(option...)
	store, ok := storeMapBasic.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapBasic)
		if ok {
			return storeMap[id]
		}
	}
	lockMapBasic.Lock()
	defer lockMapBasic.Unlock()
	store, ok = storeMapBasic.Load(fitKey)
	if ok {
		tblMapBasic, ok := store.(*MapBasic)
		if ok {
			return tblMapBasic
		}
	}
	tblMapBasic := make(map[int64]*MapBasic)
	map_basic_str, err := consulconfig.GetInstance().GetConfig(strMapBasic, option...)
	if map_basic_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(map_basic_str), &tblMapBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "map_basic", errUnmarshal)
		return nil
	}
	storeMapBasic.Store(fitKey, tblMapBasic)
	return tblMapBasic[id]
}

func LoadAllMapBasicCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strMapBasic, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "MapBasic", successChannels)
	return nil
}
