// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type MapSpotInfoSpotList struct {
	SpotId   int32  `json:"spotId"`
	SpotName int64  `json:"spotName"`
	AssetId  string `json:"assetId"`
	X        int32  `json:"x"`
	Y        int32  `json:"y"`
}

type MapSpotInfo struct {
	Id       int64                 `json:"id"`
	Name     string                `json:"name"`
	SpotList []MapSpotInfoSpotList `json:"SpotList"`
}

var lockMapSpotInfo sync.RWMutex
var storeMapSpotInfo sync.Map
var strMapSpotInfo string = "map_spot_info"

func InitMapSpotInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strMapSpotInfo, watchMapSpotInfoFunc)
	return LoadAllMapSpotInfoCfg()
}

func fixKeyMapSpotInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strMapSpotInfo)
}
func watchMapSpotInfoFunc(key string, js string) {
	mapMapSpotInfo := make(map[int64]*MapSpotInfo)
	errUnmarshal := json.Unmarshal([]byte(js), &mapMapSpotInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeMapSpotInfo.Store(key, mapMapSpotInfo)
}

func GetAllMapSpotInfo(option ...consulconfig.Option) map[int64]*MapSpotInfo {
	fitKey := fixKeyMapSpotInfo(option...)
	store, ok := storeMapSpotInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapSpotInfo)
		if ok {
			return storeMap
		}
	}
	lockMapSpotInfo.Lock()
	defer lockMapSpotInfo.Unlock()
	store, ok = storeMapSpotInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapSpotInfo)
		if ok {
			return storeMap
		}
	}
	tblMapSpotInfo := make(map[int64]*MapSpotInfo)
	map_spot_info_str, err := consulconfig.GetInstance().GetConfig(strMapSpotInfo, option...)
	if map_spot_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(map_spot_info_str), &tblMapSpotInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "map_spot_info", errUnmarshal)
		return nil
	}
	storeMapSpotInfo.Store(fitKey, tblMapSpotInfo)
	return tblMapSpotInfo
}

func GetMapSpotInfo(id int64, option ...consulconfig.Option) *MapSpotInfo {
	fitKey := fixKeyMapSpotInfo(option...)
	store, ok := storeMapSpotInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapSpotInfo)
		if ok {
			return storeMap[id]
		}
	}
	lockMapSpotInfo.Lock()
	defer lockMapSpotInfo.Unlock()
	store, ok = storeMapSpotInfo.Load(fitKey)
	if ok {
		tblMapSpotInfo, ok := store.(*MapSpotInfo)
		if ok {
			return tblMapSpotInfo
		}
	}
	tblMapSpotInfo := make(map[int64]*MapSpotInfo)
	map_spot_info_str, err := consulconfig.GetInstance().GetConfig(strMapSpotInfo, option...)
	if map_spot_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(map_spot_info_str), &tblMapSpotInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "map_spot_info", errUnmarshal)
		return nil
	}
	storeMapSpotInfo.Store(fitKey, tblMapSpotInfo)
	return tblMapSpotInfo[id]
}

func LoadAllMapSpotInfoCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strMapSpotInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "MapSpotInfo", successChannels)
	return nil
}
