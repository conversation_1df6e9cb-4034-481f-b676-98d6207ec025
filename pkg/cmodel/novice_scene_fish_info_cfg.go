// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type NoviceSceneFishInfo struct {
	Id           int64   `json:"id"`
	Mark         string  `json:"mark"`
	FishId       int64   `json:"fishId"`
	G            float64 `json:"g"`
	Len          int32   `json:"len"`
	Exp          int32   `json:"exp"`
	HookDelay    float64 `json:"hookDelay"`
	EscapeyDelay float64 `json:"escapeyDelay"`
}

var lockNoviceSceneFishInfo sync.RWMutex
var storeNoviceSceneFishInfo sync.Map
var strNoviceSceneFishInfo string = "novice_scene_fish_info"

func InitNoviceSceneFishInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strNoviceSceneFishInfo, watchNoviceSceneFishInfoFunc)
	return LoadAllNoviceSceneFishInfoCfg()
}

func fixKeyNoviceSceneFishInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strNoviceSceneFishInfo)
}
func watchNoviceSceneFishInfoFunc(key string, js string) {
	mapNoviceSceneFishInfo := make(map[int64]*NoviceSceneFishInfo)
	errUnmarshal := json.Unmarshal([]byte(js), &mapNoviceSceneFishInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeNoviceSceneFishInfo.Store(key, mapNoviceSceneFishInfo)
}

func GetAllNoviceSceneFishInfo(option ...consulconfig.Option) map[int64]*NoviceSceneFishInfo {
	fitKey := fixKeyNoviceSceneFishInfo(option...)
	store, ok := storeNoviceSceneFishInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneFishInfo)
		if ok {
			return storeMap
		}
	}
	lockNoviceSceneFishInfo.Lock()
	defer lockNoviceSceneFishInfo.Unlock()
	store, ok = storeNoviceSceneFishInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneFishInfo)
		if ok {
			return storeMap
		}
	}
	tblNoviceSceneFishInfo := make(map[int64]*NoviceSceneFishInfo)
	novice_scene_fish_info_str, err := consulconfig.GetInstance().GetConfig(strNoviceSceneFishInfo, option...)
	if novice_scene_fish_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(novice_scene_fish_info_str), &tblNoviceSceneFishInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "novice_scene_fish_info", errUnmarshal)
		return nil
	}
	storeNoviceSceneFishInfo.Store(fitKey, tblNoviceSceneFishInfo)
	return tblNoviceSceneFishInfo
}

func GetNoviceSceneFishInfo(id int64, option ...consulconfig.Option) *NoviceSceneFishInfo {
	fitKey := fixKeyNoviceSceneFishInfo(option...)
	store, ok := storeNoviceSceneFishInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneFishInfo)
		if ok {
			return storeMap[id]
		}
	}
	lockNoviceSceneFishInfo.Lock()
	defer lockNoviceSceneFishInfo.Unlock()
	store, ok = storeNoviceSceneFishInfo.Load(fitKey)
	if ok {
		tblNoviceSceneFishInfo, ok := store.(*NoviceSceneFishInfo)
		if ok {
			return tblNoviceSceneFishInfo
		}
	}
	tblNoviceSceneFishInfo := make(map[int64]*NoviceSceneFishInfo)
	novice_scene_fish_info_str, err := consulconfig.GetInstance().GetConfig(strNoviceSceneFishInfo, option...)
	if novice_scene_fish_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(novice_scene_fish_info_str), &tblNoviceSceneFishInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "novice_scene_fish_info", errUnmarshal)
		return nil
	}
	storeNoviceSceneFishInfo.Store(fitKey, tblNoviceSceneFishInfo)
	return tblNoviceSceneFishInfo[id]
}

func LoadAllNoviceSceneFishInfoCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strNoviceSceneFishInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "NoviceSceneFishInfo", successChannels)
	return nil
}
