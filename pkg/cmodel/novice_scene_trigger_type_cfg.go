// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type NoviceSceneTriggerType struct {
	Id   int64  `json:"id"`
	Mark string `json:"mark"`
	Name string `json:"name"`
}

var lockNoviceSceneTriggerType sync.RWMutex
var storeNoviceSceneTriggerType sync.Map
var strNoviceSceneTriggerType string = "novice_scene_trigger_type"

func InitNoviceSceneTriggerTypeCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strNoviceSceneTriggerType, watchNoviceSceneTriggerTypeFunc)
	return LoadAllNoviceSceneTriggerTypeCfg()
}

func fixKeyNoviceSceneTriggerType(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strNoviceSceneTriggerType)
}
func watchNoviceSceneTriggerTypeFunc(key string, js string) {
	mapNoviceSceneTriggerType := make(map[int64]*NoviceSceneTriggerType)
	errUnmarshal := json.Unmarshal([]byte(js), &mapNoviceSceneTriggerType)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeNoviceSceneTriggerType.Store(key, mapNoviceSceneTriggerType)
}

func GetAllNoviceSceneTriggerType(option ...consulconfig.Option) map[int64]*NoviceSceneTriggerType {
	fitKey := fixKeyNoviceSceneTriggerType(option...)
	store, ok := storeNoviceSceneTriggerType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneTriggerType)
		if ok {
			return storeMap
		}
	}
	lockNoviceSceneTriggerType.Lock()
	defer lockNoviceSceneTriggerType.Unlock()
	store, ok = storeNoviceSceneTriggerType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneTriggerType)
		if ok {
			return storeMap
		}
	}
	tblNoviceSceneTriggerType := make(map[int64]*NoviceSceneTriggerType)
	novice_scene_trigger_type_str, err := consulconfig.GetInstance().GetConfig(strNoviceSceneTriggerType, option...)
	if novice_scene_trigger_type_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(novice_scene_trigger_type_str), &tblNoviceSceneTriggerType)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "novice_scene_trigger_type", errUnmarshal)
		return nil
	}
	storeNoviceSceneTriggerType.Store(fitKey, tblNoviceSceneTriggerType)
	return tblNoviceSceneTriggerType
}

func GetNoviceSceneTriggerType(id int64, option ...consulconfig.Option) *NoviceSceneTriggerType {
	fitKey := fixKeyNoviceSceneTriggerType(option...)
	store, ok := storeNoviceSceneTriggerType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneTriggerType)
		if ok {
			return storeMap[id]
		}
	}
	lockNoviceSceneTriggerType.Lock()
	defer lockNoviceSceneTriggerType.Unlock()
	store, ok = storeNoviceSceneTriggerType.Load(fitKey)
	if ok {
		tblNoviceSceneTriggerType, ok := store.(*NoviceSceneTriggerType)
		if ok {
			return tblNoviceSceneTriggerType
		}
	}
	tblNoviceSceneTriggerType := make(map[int64]*NoviceSceneTriggerType)
	novice_scene_trigger_type_str, err := consulconfig.GetInstance().GetConfig(strNoviceSceneTriggerType, option...)
	if novice_scene_trigger_type_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(novice_scene_trigger_type_str), &tblNoviceSceneTriggerType)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "novice_scene_trigger_type", errUnmarshal)
		return nil
	}
	storeNoviceSceneTriggerType.Store(fitKey, tblNoviceSceneTriggerType)
	return tblNoviceSceneTriggerType[id]
}

func LoadAllNoviceSceneTriggerTypeCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strNoviceSceneTriggerType, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "NoviceSceneTriggerType", successChannels)
	return nil
}
