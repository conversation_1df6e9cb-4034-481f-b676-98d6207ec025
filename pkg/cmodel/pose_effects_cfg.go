// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type PoseEffects struct {
	Id     int32     `json:"id"`
	Baits  []int32   `json:"baits"`
	Light  []float32 `json:"light"`
	Noise  []float32 `json:"noise"`
	Splash []float32 `json:"splash"`
}

var lockPoseEffects sync.RWMutex
var storePoseEffects sync.Map
var strPoseEffects string = "pose_effects"

func InitPoseEffectsCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strPoseEffects, watchPoseEffectsFunc)
	return LoadAllPoseEffectsCfg()
}

func fixKeyPoseEffects(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strPoseEffects)
}
func watchPoseEffectsFunc(key string, js string) {
	mapPoseEffects := make(map[int64]*PoseEffects)
	errUnmarshal := json.Unmarshal([]byte(js), &mapPoseEffects)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storePoseEffects.Store(key, mapPoseEffects)
}

func GetAllPoseEffects(option ...consulconfig.Option) map[int64]*PoseEffects {
	fitKey := fixKeyPoseEffects(option...)
	store, ok := storePoseEffects.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseEffects)
		if ok {
			return storeMap
		}
	}
	lockPoseEffects.Lock()
	defer lockPoseEffects.Unlock()
	store, ok = storePoseEffects.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseEffects)
		if ok {
			return storeMap
		}
	}
	tblPoseEffects := make(map[int64]*PoseEffects)
	pose_effects_str, err := consulconfig.GetInstance().GetConfig(strPoseEffects, option...)
	if pose_effects_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pose_effects_str), &tblPoseEffects)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pose_effects", errUnmarshal)
		return nil
	}
	storePoseEffects.Store(fitKey, tblPoseEffects)
	return tblPoseEffects
}

func GetPoseEffects(id int64, option ...consulconfig.Option) *PoseEffects {
	fitKey := fixKeyPoseEffects(option...)
	store, ok := storePoseEffects.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseEffects)
		if ok {
			return storeMap[id]
		}
	}
	lockPoseEffects.Lock()
	defer lockPoseEffects.Unlock()
	store, ok = storePoseEffects.Load(fitKey)
	if ok {
		tblPoseEffects, ok := store.(*PoseEffects)
		if ok {
			return tblPoseEffects
		}
	}
	tblPoseEffects := make(map[int64]*PoseEffects)
	pose_effects_str, err := consulconfig.GetInstance().GetConfig(strPoseEffects, option...)
	if pose_effects_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pose_effects_str), &tblPoseEffects)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pose_effects", errUnmarshal)
		return nil
	}
	storePoseEffects.Store(fitKey, tblPoseEffects)
	return tblPoseEffects[id]
}

func LoadAllPoseEffectsCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strPoseEffects, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "PoseEffects", successChannels)
	return nil
}
