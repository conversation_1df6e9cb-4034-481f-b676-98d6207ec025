// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RealBite struct {
	Id            int32 `json:"id"`
	BehaviourId   int32 `json:"behaviourId"`
	ActionId      int32 `json:"actionId"`
	ProbWeight    int32 `json:"probWeight"`
	ProportionMin int32 `json:"proportionMin"`
	ProportionMax int32 `json:"proportionMax"`
	TimeMin       int32 `json:"timeMin"`
	TimeMax       int32 `json:"timeMax"`
}

var lockRealBite sync.RWMutex
var storeRealBite sync.Map
var strRealBite string = "real_bite"

func InitRealBiteCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRealBite, watchRealBiteFunc)
	return LoadAllRealBiteCfg()
}

func fixKeyRealBite(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRealBite)
}
func watchRealBiteFunc(key string, js string) {
	mapRealBite := make(map[int64]*RealBite)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRealBite)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRealBite.Store(key, mapRealBite)
}

func GetAllRealBite(option ...consulconfig.Option) map[int64]*RealBite {
	fitKey := fixKeyRealBite(option...)
	store, ok := storeRealBite.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RealBite)
		if ok {
			return storeMap
		}
	}
	lockRealBite.Lock()
	defer lockRealBite.Unlock()
	store, ok = storeRealBite.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RealBite)
		if ok {
			return storeMap
		}
	}
	tblRealBite := make(map[int64]*RealBite)
	real_bite_str, err := consulconfig.GetInstance().GetConfig(strRealBite, option...)
	if real_bite_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(real_bite_str), &tblRealBite)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "real_bite", errUnmarshal)
		return nil
	}
	storeRealBite.Store(fitKey, tblRealBite)
	return tblRealBite
}

func GetRealBite(id int64, option ...consulconfig.Option) *RealBite {
	fitKey := fixKeyRealBite(option...)
	store, ok := storeRealBite.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RealBite)
		if ok {
			return storeMap[id]
		}
	}
	lockRealBite.Lock()
	defer lockRealBite.Unlock()
	store, ok = storeRealBite.Load(fitKey)
	if ok {
		tblRealBite, ok := store.(*RealBite)
		if ok {
			return tblRealBite
		}
	}
	tblRealBite := make(map[int64]*RealBite)
	real_bite_str, err := consulconfig.GetInstance().GetConfig(strRealBite, option...)
	if real_bite_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(real_bite_str), &tblRealBite)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "real_bite", errUnmarshal)
		return nil
	}
	storeRealBite.Store(fitKey, tblRealBite)
	return tblRealBite[id]
}

func LoadAllRealBiteCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRealBite, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RealBite", successChannels)
	return nil
}
