// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RecommMapping struct {
	Id                int64 `json:"id"`
	WeatherFactorName int64 `json:"weatherFactorName"`
	RecommType        int64 `json:"recommType"`
}

var lockRecommMapping sync.RWMutex
var storeRecommMapping sync.Map
var strRecommMapping string = "recomm_mapping"

func InitRecommMappingCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRecommMapping, watchRecommMappingFunc)
	return LoadAllRecommMappingCfg()
}

func fixKeyRecommMapping(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRecommMapping)
}
func watchRecommMappingFunc(key string, js string) {
	mapRecommMapping := make(map[int64]*RecommMapping)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRecommMapping)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRecommMapping.Store(key, mapRecommMapping)
}

func GetAllRecommMapping(option ...consulconfig.Option) map[int64]*RecommMapping {
	fitKey := fixKeyRecommMapping(option...)
	store, ok := storeRecommMapping.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RecommMapping)
		if ok {
			return storeMap
		}
	}
	lockRecommMapping.Lock()
	defer lockRecommMapping.Unlock()
	store, ok = storeRecommMapping.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RecommMapping)
		if ok {
			return storeMap
		}
	}
	tblRecommMapping := make(map[int64]*RecommMapping)
	recomm_mapping_str, err := consulconfig.GetInstance().GetConfig(strRecommMapping, option...)
	if recomm_mapping_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(recomm_mapping_str), &tblRecommMapping)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "recomm_mapping", errUnmarshal)
		return nil
	}
	storeRecommMapping.Store(fitKey, tblRecommMapping)
	return tblRecommMapping
}

func GetRecommMapping(id int64, option ...consulconfig.Option) *RecommMapping {
	fitKey := fixKeyRecommMapping(option...)
	store, ok := storeRecommMapping.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RecommMapping)
		if ok {
			return storeMap[id]
		}
	}
	lockRecommMapping.Lock()
	defer lockRecommMapping.Unlock()
	store, ok = storeRecommMapping.Load(fitKey)
	if ok {
		tblRecommMapping, ok := store.(*RecommMapping)
		if ok {
			return tblRecommMapping
		}
	}
	tblRecommMapping := make(map[int64]*RecommMapping)
	recomm_mapping_str, err := consulconfig.GetInstance().GetConfig(strRecommMapping, option...)
	if recomm_mapping_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(recomm_mapping_str), &tblRecommMapping)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "recomm_mapping", errUnmarshal)
		return nil
	}
	storeRecommMapping.Store(fitKey, tblRecommMapping)
	return tblRecommMapping[id]
}

func LoadAllRecommMappingCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRecommMapping, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RecommMapping", successChannels)
	return nil
}
