// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RoleConst struct {
	Id                    int64   `json:"id"`
	ThrowRodCostHp        int32   `json:"throwRodCostHp"`
	CatchFishCostHp       int32   `json:"catchFishCostHp"`
	HooksetBaseTime       int32   `json:"hooksetBaseTime"`
	HooksetMediumPercent  float32 `json:"hooksetMediumPercent"`
	HooksetPerfectPercent float32 `json:"hooksetPerfectPercent"`
}

var lockRoleConst sync.RWMutex
var storeRoleConst sync.Map
var strRoleConst string = "role_const"

func InitRoleConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRoleConst, watchRoleConstFunc)
	return LoadAllRoleConstCfg()
}

func fixKeyRoleConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRoleConst)
}
func watchRoleConstFunc(key string, js string) {
	store, ok := storeRoleConst.Load(key)
	if !ok {
		store = &RoleConst{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRoleConst.Store(key, store)
}

func GetRoleConst(option ...consulconfig.Option) *RoleConst {
	fitKey := fixKeyRoleConst(option...)
	store, ok := storeRoleConst.Load(fitKey)
	if ok {
		tblRoleConst, ok := store.(*RoleConst)
		if ok {
			return tblRoleConst
		}
	}
	lockRoleConst.Lock()
	defer lockRoleConst.Unlock()
	store, ok = storeRoleConst.Load(fitKey)
	if ok {
		tblRoleConst, ok := store.(*RoleConst)
		if ok {
			return tblRoleConst
		}
	}
	tblRoleConst := &RoleConst{}
	role_const_str, err := consulconfig.GetInstance().GetConfig(strRoleConst, option...)
	if role_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(role_const_str), &tblRoleConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strRoleConst, errUnmarshal, role_const_str)
		return nil
	}
	storeRoleConst.Store(fitKey, tblRoleConst)
	return tblRoleConst
}

func LoadAllRoleConstCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRoleConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RoleConst", successChannels)
	return nil
}
