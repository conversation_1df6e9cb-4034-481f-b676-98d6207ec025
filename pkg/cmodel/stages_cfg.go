// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Stages struct {
	Id           int64  `json:"id"`
	Name         string `json:"name"`
	ActivityId   int64  `json:"activityId"`
	StageId      int64  `json:"stageId"`
	IsLarge      bool   `json:"isLarge"`
	StageRewards int64  `json:"stageRewards"`
	Value        int64  `json:"value"`
}

var lockStages sync.RWMutex
var storeStages sync.Map
var strStages string = "stages"

func InitStagesCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStages, watchStagesFunc)
	return LoadAllStagesCfg()
}

func fixKeyStages(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStages)
}
func watchStagesFunc(key string, js string) {
	mapStages := make(map[int64]*Stages)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStages)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStages.Store(key, mapStages)
}

func GetAllStages(option ...consulconfig.Option) map[int64]*Stages {
	fitKey := fixKeyStages(option...)
	store, ok := storeStages.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Stages)
		if ok {
			return storeMap
		}
	}
	lockStages.Lock()
	defer lockStages.Unlock()
	store, ok = storeStages.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Stages)
		if ok {
			return storeMap
		}
	}
	tblStages := make(map[int64]*Stages)
	stages_str, err := consulconfig.GetInstance().GetConfig(strStages, option...)
	if stages_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stages_str), &tblStages)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stages", errUnmarshal)
		return nil
	}
	storeStages.Store(fitKey, tblStages)
	return tblStages
}

func GetStages(id int64, option ...consulconfig.Option) *Stages {
	fitKey := fixKeyStages(option...)
	store, ok := storeStages.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Stages)
		if ok {
			return storeMap[id]
		}
	}
	lockStages.Lock()
	defer lockStages.Unlock()
	store, ok = storeStages.Load(fitKey)
	if ok {
		tblStages, ok := store.(*Stages)
		if ok {
			return tblStages
		}
	}
	tblStages := make(map[int64]*Stages)
	stages_str, err := consulconfig.GetInstance().GetConfig(strStages, option...)
	if stages_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stages_str), &tblStages)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stages", errUnmarshal)
		return nil
	}
	storeStages.Store(fitKey, tblStages)
	return tblStages[id]
}

func LoadAllStagesCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStages, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Stages", successChannels)
	return nil
}
