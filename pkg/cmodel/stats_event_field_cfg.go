// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StatsEventField struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Mark string `json:"mark"`
}

var lockStatsEventField sync.RWMutex
var storeStatsEventField sync.Map
var strStatsEventField string = "stats_event_field"

func InitStatsEventFieldCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStatsEventField, watchStatsEventFieldFunc)
	return LoadAllStatsEventFieldCfg()
}

func fixKeyStatsEventField(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStatsEventField)
}
func watchStatsEventFieldFunc(key string, js string) {
	mapStatsEventField := make(map[int64]*StatsEventField)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStatsEventField)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStatsEventField.Store(key, mapStatsEventField)
}

func GetAllStatsEventField(option ...consulconfig.Option) map[int64]*StatsEventField {
	fitKey := fixKeyStatsEventField(option...)
	store, ok := storeStatsEventField.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsEventField)
		if ok {
			return storeMap
		}
	}
	lockStatsEventField.Lock()
	defer lockStatsEventField.Unlock()
	store, ok = storeStatsEventField.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsEventField)
		if ok {
			return storeMap
		}
	}
	tblStatsEventField := make(map[int64]*StatsEventField)
	stats_event_field_str, err := consulconfig.GetInstance().GetConfig(strStatsEventField, option...)
	if stats_event_field_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_event_field_str), &tblStatsEventField)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats_event_field", errUnmarshal)
		return nil
	}
	storeStatsEventField.Store(fitKey, tblStatsEventField)
	return tblStatsEventField
}

func GetStatsEventField(id int64, option ...consulconfig.Option) *StatsEventField {
	fitKey := fixKeyStatsEventField(option...)
	store, ok := storeStatsEventField.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsEventField)
		if ok {
			return storeMap[id]
		}
	}
	lockStatsEventField.Lock()
	defer lockStatsEventField.Unlock()
	store, ok = storeStatsEventField.Load(fitKey)
	if ok {
		tblStatsEventField, ok := store.(*StatsEventField)
		if ok {
			return tblStatsEventField
		}
	}
	tblStatsEventField := make(map[int64]*StatsEventField)
	stats_event_field_str, err := consulconfig.GetInstance().GetConfig(strStatsEventField, option...)
	if stats_event_field_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_event_field_str), &tblStatsEventField)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats_event_field", errUnmarshal)
		return nil
	}
	storeStatsEventField.Store(fitKey, tblStatsEventField)
	return tblStatsEventField[id]
}

func LoadAllStatsEventFieldCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStatsEventField, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StatsEventField", successChannels)
	return nil
}
