// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StoreRecommendGoodsList struct {
	StoreBuyId     int64  `json:"storeBuyId"`
	RecommendImage string `json:"recommendImage"`
	IconMini       string `json:"iconMini"`
}

type StoreRecommend struct {
	Id            int64                     `json:"id"`
	Name          string                    `json:"name"`
	NameLanguage  int64                     `json:"nameLanguage"`
	RecommendType int32                     `json:"recommendType"`
	SortId        int32                     `json:"sortId"`
	GoodsList     []StoreRecommendGoodsList `json:"GoodsList"`
}

var lockStoreRecommend sync.RWMutex
var storeStoreRecommend sync.Map
var strStoreRecommend string = "store_recommend"

func InitStoreRecommendCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStoreRecommend, watchStoreRecommendFunc)
	return LoadAllStoreRecommendCfg()
}

func fixKeyStoreRecommend(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStoreRecommend)
}
func watchStoreRecommendFunc(key string, js string) {
	mapStoreRecommend := make(map[int64]*StoreRecommend)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStoreRecommend)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStoreRecommend.Store(key, mapStoreRecommend)
}

func GetAllStoreRecommend(option ...consulconfig.Option) map[int64]*StoreRecommend {
	fitKey := fixKeyStoreRecommend(option...)
	store, ok := storeStoreRecommend.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StoreRecommend)
		if ok {
			return storeMap
		}
	}
	lockStoreRecommend.Lock()
	defer lockStoreRecommend.Unlock()
	store, ok = storeStoreRecommend.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StoreRecommend)
		if ok {
			return storeMap
		}
	}
	tblStoreRecommend := make(map[int64]*StoreRecommend)
	store_recommend_str, err := consulconfig.GetInstance().GetConfig(strStoreRecommend, option...)
	if store_recommend_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(store_recommend_str), &tblStoreRecommend)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "store_recommend", errUnmarshal)
		return nil
	}
	storeStoreRecommend.Store(fitKey, tblStoreRecommend)
	return tblStoreRecommend
}

func GetStoreRecommend(id int64, option ...consulconfig.Option) *StoreRecommend {
	fitKey := fixKeyStoreRecommend(option...)
	store, ok := storeStoreRecommend.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StoreRecommend)
		if ok {
			return storeMap[id]
		}
	}
	lockStoreRecommend.Lock()
	defer lockStoreRecommend.Unlock()
	store, ok = storeStoreRecommend.Load(fitKey)
	if ok {
		tblStoreRecommend, ok := store.(*StoreRecommend)
		if ok {
			return tblStoreRecommend
		}
	}
	tblStoreRecommend := make(map[int64]*StoreRecommend)
	store_recommend_str, err := consulconfig.GetInstance().GetConfig(strStoreRecommend, option...)
	if store_recommend_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(store_recommend_str), &tblStoreRecommend)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "store_recommend", errUnmarshal)
		return nil
	}
	storeStoreRecommend.Store(fitKey, tblStoreRecommend)
	return tblStoreRecommend[id]
}

func LoadAllStoreRecommendCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStoreRecommend, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StoreRecommend", successChannels)
	return nil
}
