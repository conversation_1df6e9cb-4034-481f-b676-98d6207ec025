// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StoreRoom struct {
	Id        int64  `json:"id"`
	Name      string `json:"name"`
	Mark      string `json:"mark"`
	ShowStyle int32  `json:"showStyle"`
	ItemType  int32  `json:"itemType"`
	GoodsId   int64  `json:"goodsId"`
	CostItem  int64  `json:"costItem"`
	CostCount int64  `json:"costCount"`
	PondId    int64  `json:"pondId"`
}

var lockStoreRoom sync.RWMutex
var storeStoreRoom sync.Map
var strStoreRoom string = "store_room"

func InitStoreRoomCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStoreRoom, watchStoreRoomFunc)
	return LoadAllStoreRoomCfg()
}

func fixKeyStoreRoom(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStoreRoom)
}
func watchStoreRoomFunc(key string, js string) {
	mapStoreRoom := make(map[int64]*StoreRoom)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStoreRoom)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStoreRoom.Store(key, mapStoreRoom)
}

func GetAllStoreRoom(option ...consulconfig.Option) map[int64]*StoreRoom {
	fitKey := fixKeyStoreRoom(option...)
	store, ok := storeStoreRoom.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StoreRoom)
		if ok {
			return storeMap
		}
	}
	lockStoreRoom.Lock()
	defer lockStoreRoom.Unlock()
	store, ok = storeStoreRoom.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StoreRoom)
		if ok {
			return storeMap
		}
	}
	tblStoreRoom := make(map[int64]*StoreRoom)
	store_room_str, err := consulconfig.GetInstance().GetConfig(strStoreRoom, option...)
	if store_room_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(store_room_str), &tblStoreRoom)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "store_room", errUnmarshal)
		return nil
	}
	storeStoreRoom.Store(fitKey, tblStoreRoom)
	return tblStoreRoom
}

func GetStoreRoom(id int64, option ...consulconfig.Option) *StoreRoom {
	fitKey := fixKeyStoreRoom(option...)
	store, ok := storeStoreRoom.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StoreRoom)
		if ok {
			return storeMap[id]
		}
	}
	lockStoreRoom.Lock()
	defer lockStoreRoom.Unlock()
	store, ok = storeStoreRoom.Load(fitKey)
	if ok {
		tblStoreRoom, ok := store.(*StoreRoom)
		if ok {
			return tblStoreRoom
		}
	}
	tblStoreRoom := make(map[int64]*StoreRoom)
	store_room_str, err := consulconfig.GetInstance().GetConfig(strStoreRoom, option...)
	if store_room_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(store_room_str), &tblStoreRoom)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "store_room", errUnmarshal)
		return nil
	}
	storeStoreRoom.Store(fitKey, tblStoreRoom)
	return tblStoreRoom[id]
}

func LoadAllStoreRoomCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStoreRoom, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StoreRoom", successChannels)
	return nil
}
