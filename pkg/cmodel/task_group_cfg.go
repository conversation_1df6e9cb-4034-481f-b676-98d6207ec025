// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TaskGroup struct {
	Id           int64  `json:"id"`
	Name         string `json:"name"`
	Mark         string `json:"mark"`
	NameLanguage int64  `json:"nameLanguage"`
	Icon         string `json:"icon"`
}

var lockTaskGroup sync.RWMutex
var storeTaskGroup sync.Map
var strTaskGroup string = "task_group"

func InitTaskGroupCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTaskGroup, watchTaskGroupFunc)
	return LoadAllTaskGroupCfg()
}

func fixKeyTaskGroup(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTaskGroup)
}
func watchTaskGroupFunc(key string, js string) {
	mapTaskGroup := make(map[int64]*TaskGroup)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTaskGroup)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTaskGroup.Store(key, mapTaskGroup)
}

func GetAllTaskGroup(option ...consulconfig.Option) map[int64]*TaskGroup {
	fitKey := fixKeyTaskGroup(option...)
	store, ok := storeTaskGroup.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskGroup)
		if ok {
			return storeMap
		}
	}
	lockTaskGroup.Lock()
	defer lockTaskGroup.Unlock()
	store, ok = storeTaskGroup.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskGroup)
		if ok {
			return storeMap
		}
	}
	tblTaskGroup := make(map[int64]*TaskGroup)
	task_group_str, err := consulconfig.GetInstance().GetConfig(strTaskGroup, option...)
	if task_group_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_group_str), &tblTaskGroup)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_group", errUnmarshal)
		return nil
	}
	storeTaskGroup.Store(fitKey, tblTaskGroup)
	return tblTaskGroup
}

func GetTaskGroup(id int64, option ...consulconfig.Option) *TaskGroup {
	fitKey := fixKeyTaskGroup(option...)
	store, ok := storeTaskGroup.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskGroup)
		if ok {
			return storeMap[id]
		}
	}
	lockTaskGroup.Lock()
	defer lockTaskGroup.Unlock()
	store, ok = storeTaskGroup.Load(fitKey)
	if ok {
		tblTaskGroup, ok := store.(*TaskGroup)
		if ok {
			return tblTaskGroup
		}
	}
	tblTaskGroup := make(map[int64]*TaskGroup)
	task_group_str, err := consulconfig.GetInstance().GetConfig(strTaskGroup, option...)
	if task_group_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_group_str), &tblTaskGroup)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_group", errUnmarshal)
		return nil
	}
	storeTaskGroup.Store(fitKey, tblTaskGroup)
	return tblTaskGroup[id]
}

func LoadAllTaskGroupCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTaskGroup, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TaskGroup", successChannels)
	return nil
}
