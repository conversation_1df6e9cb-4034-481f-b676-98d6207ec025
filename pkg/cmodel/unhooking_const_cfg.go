// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type UnhookingConst struct {
	Id                             int64   `json:"id"`
	UnhookingValueInObstacle       int32   `json:"unhookingValueInObstacle"`
	UnhookingValueMaxInObstacle    int32   `json:"unhookingValueMaxInObstacle"`
	UnhookingValueRange            []int32 `json:"unhookingValueRange"`
	UnhookingProbabilityRange      []int32 `json:"unhookingProbabilityRange"`
	DamageFishWhenHooksetLow       int32   `json:"damageFishWhenHooksetLow"`
	DamegeFishWhenFightDrastically int32   `json:"damegeFishWhenFightDrastically"`
	DamegeFishWhenFishExhausted    int32   `json:"damegeFishWhenFishExhausted"`
	OnPlayerFightUnavailingUpAdd   int32   `json:"onPlayerFightUnavailingUpAdd"`
}

var lockUnhookingConst sync.RWMutex
var storeUnhookingConst sync.Map
var strUnhookingConst string = "unhooking_const"

func InitUnhookingConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strUnhookingConst, watchUnhookingConstFunc)
	return LoadAllUnhookingConstCfg()
}

func fixKeyUnhookingConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strUnhookingConst)
}
func watchUnhookingConstFunc(key string, js string) {
	store, ok := storeUnhookingConst.Load(key)
	if !ok {
		store = &UnhookingConst{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeUnhookingConst.Store(key, store)
}

func GetUnhookingConst(option ...consulconfig.Option) *UnhookingConst {
	fitKey := fixKeyUnhookingConst(option...)
	store, ok := storeUnhookingConst.Load(fitKey)
	if ok {
		tblUnhookingConst, ok := store.(*UnhookingConst)
		if ok {
			return tblUnhookingConst
		}
	}
	lockUnhookingConst.Lock()
	defer lockUnhookingConst.Unlock()
	store, ok = storeUnhookingConst.Load(fitKey)
	if ok {
		tblUnhookingConst, ok := store.(*UnhookingConst)
		if ok {
			return tblUnhookingConst
		}
	}
	tblUnhookingConst := &UnhookingConst{}
	unhooking_const_str, err := consulconfig.GetInstance().GetConfig(strUnhookingConst, option...)
	if unhooking_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(unhooking_const_str), &tblUnhookingConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strUnhookingConst, errUnmarshal, unhooking_const_str)
		return nil
	}
	storeUnhookingConst.Store(fitKey, tblUnhookingConst)
	return tblUnhookingConst
}

func LoadAllUnhookingConstCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strUnhookingConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "UnhookingConst", successChannels)
	return nil
}
