// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type WeatherConditions struct {
	Id             int64  `json:"id"`
	Name           string `json:"name"`
	WeatherName    string `json:"weatherName"`
	VisualEffectId int64  `json:"visualEffectId"`
	AudioEffectId  int64  `json:"audioEffectId"`
	Mark           string `json:"mark"`
}

var lockWeatherConditions sync.RWMutex
var storeWeatherConditions sync.Map
var strWeatherConditions string = "weather_conditions"

func InitWeatherConditionsCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strWeatherConditions, watchWeatherConditionsFunc)
	return LoadAllWeatherConditionsCfg()
}

func fixKeyWeatherConditions(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strWeatherConditions)
}
func watchWeatherConditionsFunc(key string, js string) {
	mapWeatherConditions := make(map[int64]*WeatherConditions)
	errUnmarshal := json.Unmarshal([]byte(js), &mapWeatherConditions)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeWeatherConditions.Store(key, mapWeatherConditions)
}

func GetAllWeatherConditions(option ...consulconfig.Option) map[int64]*WeatherConditions {
	fitKey := fixKeyWeatherConditions(option...)
	store, ok := storeWeatherConditions.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherConditions)
		if ok {
			return storeMap
		}
	}
	lockWeatherConditions.Lock()
	defer lockWeatherConditions.Unlock()
	store, ok = storeWeatherConditions.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherConditions)
		if ok {
			return storeMap
		}
	}
	tblWeatherConditions := make(map[int64]*WeatherConditions)
	weather_conditions_str, err := consulconfig.GetInstance().GetConfig(strWeatherConditions, option...)
	if weather_conditions_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(weather_conditions_str), &tblWeatherConditions)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "weather_conditions", errUnmarshal)
		return nil
	}
	storeWeatherConditions.Store(fitKey, tblWeatherConditions)
	return tblWeatherConditions
}

func GetWeatherConditions(id int64, option ...consulconfig.Option) *WeatherConditions {
	fitKey := fixKeyWeatherConditions(option...)
	store, ok := storeWeatherConditions.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherConditions)
		if ok {
			return storeMap[id]
		}
	}
	lockWeatherConditions.Lock()
	defer lockWeatherConditions.Unlock()
	store, ok = storeWeatherConditions.Load(fitKey)
	if ok {
		tblWeatherConditions, ok := store.(*WeatherConditions)
		if ok {
			return tblWeatherConditions
		}
	}
	tblWeatherConditions := make(map[int64]*WeatherConditions)
	weather_conditions_str, err := consulconfig.GetInstance().GetConfig(strWeatherConditions, option...)
	if weather_conditions_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(weather_conditions_str), &tblWeatherConditions)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "weather_conditions", errUnmarshal)
		return nil
	}
	storeWeatherConditions.Store(fitKey, tblWeatherConditions)
	return tblWeatherConditions[id]
}

func LoadAllWeatherConditionsCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strWeatherConditions, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "WeatherConditions", successChannels)
	return nil
}
