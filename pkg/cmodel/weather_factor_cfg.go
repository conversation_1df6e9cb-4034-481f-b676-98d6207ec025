// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type WeatherFactor struct {
	Id                int64   `json:"id"`
	Name              string  `json:"name"`
	WeatherId         int64   `json:"weatherId"`
	OutTemp           []int64 `json:"OutTemp"`
	WindSpeed         []int64 `json:"WindSpeed"`
	WaterTemp         []int64 `json:"WaterTemp"`
	Humidity          int64   `json:"humidity"`
	WindDirection     int64   `json:"windDirection"`
	LuxInExposure     int64   `json:"luxInExposure"`
	LuxInShadow       int64   `json:"luxInShadow"`
	PressureInfluence int64   `json:"pressureInfluence"`
}

var lockWeatherFactor sync.RWMutex
var storeWeatherFactor sync.Map
var strWeatherFactor string = "weather_factor"

func InitWeatherFactorCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strWeatherFactor, watchWeatherFactorFunc)
	return LoadAllWeatherFactorCfg()
}

func fixKeyWeatherFactor(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strWeatherFactor)
}
func watchWeatherFactorFunc(key string, js string) {
	mapWeatherFactor := make(map[int64]*WeatherFactor)
	errUnmarshal := json.Unmarshal([]byte(js), &mapWeatherFactor)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeWeatherFactor.Store(key, mapWeatherFactor)
}

func GetAllWeatherFactor(option ...consulconfig.Option) map[int64]*WeatherFactor {
	fitKey := fixKeyWeatherFactor(option...)
	store, ok := storeWeatherFactor.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherFactor)
		if ok {
			return storeMap
		}
	}
	lockWeatherFactor.Lock()
	defer lockWeatherFactor.Unlock()
	store, ok = storeWeatherFactor.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherFactor)
		if ok {
			return storeMap
		}
	}
	tblWeatherFactor := make(map[int64]*WeatherFactor)
	weather_factor_str, err := consulconfig.GetInstance().GetConfig(strWeatherFactor, option...)
	if weather_factor_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(weather_factor_str), &tblWeatherFactor)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "weather_factor", errUnmarshal)
		return nil
	}
	storeWeatherFactor.Store(fitKey, tblWeatherFactor)
	return tblWeatherFactor
}

func GetWeatherFactor(id int64, option ...consulconfig.Option) *WeatherFactor {
	fitKey := fixKeyWeatherFactor(option...)
	store, ok := storeWeatherFactor.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherFactor)
		if ok {
			return storeMap[id]
		}
	}
	lockWeatherFactor.Lock()
	defer lockWeatherFactor.Unlock()
	store, ok = storeWeatherFactor.Load(fitKey)
	if ok {
		tblWeatherFactor, ok := store.(*WeatherFactor)
		if ok {
			return tblWeatherFactor
		}
	}
	tblWeatherFactor := make(map[int64]*WeatherFactor)
	weather_factor_str, err := consulconfig.GetInstance().GetConfig(strWeatherFactor, option...)
	if weather_factor_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(weather_factor_str), &tblWeatherFactor)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "weather_factor", errUnmarshal)
		return nil
	}
	storeWeatherFactor.Store(fitKey, tblWeatherFactor)
	return tblWeatherFactor[id]
}

func LoadAllWeatherFactorCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strWeatherFactor, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "WeatherFactor", successChannels)
	return nil
}
