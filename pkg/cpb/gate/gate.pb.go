// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: gate.proto

package gatePB

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GateHeartBeatReq 心跳检测请求
type GateHeartBeatReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeStamp *uint64 `protobuf:"varint,1,opt,name=time_stamp,json=timeStamp,proto3,oneof" json:"time_stamp,omitempty"` // 时间
}

func (x *GateHeartBeatReq) Reset() {
	*x = GateHeartBeatReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gate_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GateHeartBeatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GateHeartBeatReq) ProtoMessage() {}

func (x *GateHeartBeatReq) ProtoReflect() protoreflect.Message {
	mi := &file_gate_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GateHeartBeatReq.ProtoReflect.Descriptor instead.
func (*GateHeartBeatReq) Descriptor() ([]byte, []int) {
	return file_gate_proto_rawDescGZIP(), []int{0}
}

func (x *GateHeartBeatReq) GetTimeStamp() uint64 {
	if x != nil && x.TimeStamp != nil {
		return *x.TimeStamp
	}
	return 0
}

// GateHeartBeatRsp 心跳检测应答
type GateHeartBeatRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeStamp *uint64 `protobuf:"varint,1,opt,name=time_stamp,json=timeStamp,proto3,oneof" json:"time_stamp,omitempty"` // 时间
}

func (x *GateHeartBeatRsp) Reset() {
	*x = GateHeartBeatRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gate_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GateHeartBeatRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GateHeartBeatRsp) ProtoMessage() {}

func (x *GateHeartBeatRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gate_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GateHeartBeatRsp.ProtoReflect.Descriptor instead.
func (*GateHeartBeatRsp) Descriptor() ([]byte, []int) {
	return file_gate_proto_rawDescGZIP(), []int{1}
}

func (x *GateHeartBeatRsp) GetTimeStamp() uint64 {
	if x != nil && x.TimeStamp != nil {
		return *x.TimeStamp
	}
	return 0
}

// GateAnotherLoginNtf 顶号通知
type GateAnotherLoginNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reserve   *uint32 `protobuf:"varint,1,opt,name=reserve,proto3,oneof" json:"reserve,omitempty"`                      // 保留
	Device    *string `protobuf:"bytes,2,opt,name=device,proto3,oneof" json:"device,omitempty"`                         // 顶号设备
	TimeStamp *int64  `protobuf:"varint,3,opt,name=time_stamp,json=timeStamp,proto3,oneof" json:"time_stamp,omitempty"` // 顶号时间
}

func (x *GateAnotherLoginNtf) Reset() {
	*x = GateAnotherLoginNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gate_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GateAnotherLoginNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GateAnotherLoginNtf) ProtoMessage() {}

func (x *GateAnotherLoginNtf) ProtoReflect() protoreflect.Message {
	mi := &file_gate_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GateAnotherLoginNtf.ProtoReflect.Descriptor instead.
func (*GateAnotherLoginNtf) Descriptor() ([]byte, []int) {
	return file_gate_proto_rawDescGZIP(), []int{2}
}

func (x *GateAnotherLoginNtf) GetReserve() uint32 {
	if x != nil && x.Reserve != nil {
		return *x.Reserve
	}
	return 0
}

func (x *GateAnotherLoginNtf) GetDevice() string {
	if x != nil && x.Device != nil {
		return *x.Device
	}
	return ""
}

func (x *GateAnotherLoginNtf) GetTimeStamp() int64 {
	if x != nil && x.TimeStamp != nil {
		return *x.TimeStamp
	}
	return 0
}

var File_gate_proto protoreflect.FileDescriptor

var file_gate_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x67, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x61,
	0x74, 0x65, 0x22, 0x45, 0x0a, 0x10, 0x47, 0x61, 0x74, 0x65, 0x48, 0x65, 0x61, 0x72, 0x74, 0x42,
	0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x45, 0x0a, 0x10, 0x47, 0x61, 0x74,
	0x65, 0x48, 0x65, 0x61, 0x72, 0x74, 0x42, 0x65, 0x61, 0x74, 0x52, 0x73, 0x70, 0x12, 0x22, 0x0a,
	0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x48, 0x00, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x88, 0x01,
	0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x22, 0x9b, 0x01, 0x0a, 0x13, 0x47, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4e, 0x74, 0x66, 0x12, 0x1d, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x07, 0x72, 0x65, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x6d, 0x70, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x72, 0x65, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x10,
	0x5a, 0x0e, 0x2e, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x3b, 0x67, 0x61, 0x74, 0x65, 0x50, 0x42, 0x3b,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_gate_proto_rawDescOnce sync.Once
	file_gate_proto_rawDescData = file_gate_proto_rawDesc
)

func file_gate_proto_rawDescGZIP() []byte {
	file_gate_proto_rawDescOnce.Do(func() {
		file_gate_proto_rawDescData = protoimpl.X.CompressGZIP(file_gate_proto_rawDescData)
	})
	return file_gate_proto_rawDescData
}

var file_gate_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_gate_proto_goTypes = []interface{}{
	(*GateHeartBeatReq)(nil),    // 0: gate.GateHeartBeatReq
	(*GateHeartBeatRsp)(nil),    // 1: gate.GateHeartBeatRsp
	(*GateAnotherLoginNtf)(nil), // 2: gate.GateAnotherLoginNtf
}
var file_gate_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_gate_proto_init() }
func file_gate_proto_init() {
	if File_gate_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_gate_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GateHeartBeatReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gate_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GateHeartBeatRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gate_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GateAnotherLoginNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_gate_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_gate_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_gate_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gate_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gate_proto_goTypes,
		DependencyIndexes: file_gate_proto_depIdxs,
		MessageInfos:      file_gate_proto_msgTypes,
	}.Build()
	File_gate_proto = out.File
	file_gate_proto_rawDesc = nil
	file_gate_proto_goTypes = nil
	file_gate_proto_depIdxs = nil
}
