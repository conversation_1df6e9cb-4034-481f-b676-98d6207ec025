// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: mail.proto

package mailPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取邮件列表请求
type GetMailListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageIndex int32            `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`                    // 页码
	MailType  common.MAIL_TYPE `protobuf:"varint,2,opt,name=mail_type,json=mailType,proto3,enum=common.MAIL_TYPE" json:"mail_type,omitempty"` // 邮件类型
}

func (x *GetMailListReq) Reset() {
	*x = GetMailListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMailListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMailListReq) ProtoMessage() {}

func (x *GetMailListReq) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMailListReq.ProtoReflect.Descriptor instead.
func (*GetMailListReq) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{0}
}

func (x *GetMailListReq) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetMailListReq) GetMailType() common.MAIL_TYPE {
	if x != nil {
		return x.MailType
	}
	return common.MAIL_TYPE(0)
}

type GetMailListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret            *common.Result           `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	MailType       common.MAIL_TYPE         `protobuf:"varint,2,opt,name=mail_type,json=mailType,proto3,enum=common.MAIL_TYPE" json:"mail_type,omitempty"` // 邮件类型
	MailList       []*common.MailDetailInfo `protobuf:"bytes,3,rep,name=mail_list,json=mailList,proto3" json:"mail_list,omitempty"`                        // 邮件列表
	UnclaimedCount int32                    `protobuf:"varint,4,opt,name=unclaimed_count,json=unclaimedCount,proto3" json:"unclaimed_count,omitempty"`     // 未领取邮件数量(总数)
	TotalSize      int32                    `protobuf:"varint,5,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`                    // 邮件总数量
	UnreadCount    int32                    `protobuf:"varint,6,opt,name=unread_count,json=unreadCount,proto3" json:"unread_count,omitempty"`              // 未读邮件数量(总数)
}

func (x *GetMailListRsp) Reset() {
	*x = GetMailListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMailListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMailListRsp) ProtoMessage() {}

func (x *GetMailListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMailListRsp.ProtoReflect.Descriptor instead.
func (*GetMailListRsp) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{1}
}

func (x *GetMailListRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetMailListRsp) GetMailType() common.MAIL_TYPE {
	if x != nil {
		return x.MailType
	}
	return common.MAIL_TYPE(0)
}

func (x *GetMailListRsp) GetMailList() []*common.MailDetailInfo {
	if x != nil {
		return x.MailList
	}
	return nil
}

func (x *GetMailListRsp) GetUnclaimedCount() int32 {
	if x != nil {
		return x.UnclaimedCount
	}
	return 0
}

func (x *GetMailListRsp) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

func (x *GetMailListRsp) GetUnreadCount() int32 {
	if x != nil {
		return x.UnreadCount
	}
	return 0
}

// 读邮件请求
type ReadMailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MailId   uint64           `protobuf:"varint,1,opt,name=mail_id,json=mailId,proto3" json:"mail_id,omitempty"`                             // 邮件ID
	MailType common.MAIL_TYPE `protobuf:"varint,2,opt,name=mail_type,json=mailType,proto3,enum=common.MAIL_TYPE" json:"mail_type,omitempty"` // 邮件类型
}

func (x *ReadMailReq) Reset() {
	*x = ReadMailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadMailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadMailReq) ProtoMessage() {}

func (x *ReadMailReq) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadMailReq.ProtoReflect.Descriptor instead.
func (*ReadMailReq) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{2}
}

func (x *ReadMailReq) GetMailId() uint64 {
	if x != nil {
		return x.MailId
	}
	return 0
}

func (x *ReadMailReq) GetMailType() common.MAIL_TYPE {
	if x != nil {
		return x.MailType
	}
	return common.MAIL_TYPE(0)
}

type ReadMailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	MailType common.MAIL_TYPE `protobuf:"varint,2,opt,name=mail_type,json=mailType,proto3,enum=common.MAIL_TYPE" json:"mail_type,omitempty"` // 邮件类型
	MailId   uint64           `protobuf:"varint,3,opt,name=mail_id,json=mailId,proto3" json:"mail_id,omitempty"`                             // 邮件ID
}

func (x *ReadMailRsp) Reset() {
	*x = ReadMailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadMailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadMailRsp) ProtoMessage() {}

func (x *ReadMailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadMailRsp.ProtoReflect.Descriptor instead.
func (*ReadMailRsp) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{3}
}

func (x *ReadMailRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ReadMailRsp) GetMailType() common.MAIL_TYPE {
	if x != nil {
		return x.MailType
	}
	return common.MAIL_TYPE(0)
}

func (x *ReadMailRsp) GetMailId() uint64 {
	if x != nil {
		return x.MailId
	}
	return 0
}

// 领取附件奖励请求
type ClaimRewardAttachReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClaimAll bool             `protobuf:"varint,1,opt,name=claim_all,json=claimAll,proto3" json:"claim_all,omitempty"`                       // 是否领取全部
	MailId   uint64           `protobuf:"varint,2,opt,name=mail_id,json=mailId,proto3" json:"mail_id,omitempty"`                             // 邮件ID
	MailType common.MAIL_TYPE `protobuf:"varint,3,opt,name=mail_type,json=mailType,proto3,enum=common.MAIL_TYPE" json:"mail_type,omitempty"` // 邮件类型
}

func (x *ClaimRewardAttachReq) Reset() {
	*x = ClaimRewardAttachReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimRewardAttachReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimRewardAttachReq) ProtoMessage() {}

func (x *ClaimRewardAttachReq) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimRewardAttachReq.ProtoReflect.Descriptor instead.
func (*ClaimRewardAttachReq) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{4}
}

func (x *ClaimRewardAttachReq) GetClaimAll() bool {
	if x != nil {
		return x.ClaimAll
	}
	return false
}

func (x *ClaimRewardAttachReq) GetMailId() uint64 {
	if x != nil {
		return x.MailId
	}
	return 0
}

func (x *ClaimRewardAttachReq) GetMailType() common.MAIL_TYPE {
	if x != nil {
		return x.MailType
	}
	return common.MAIL_TYPE(0)
}

// 领取附件奖励响应 (具体奖励信息以奖励数据通知为准)
type ClaimRewardAttachRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	ClaimAll bool                 `protobuf:"varint,2,opt,name=claim_all,json=claimAll,proto3" json:"claim_all,omitempty"`                       // 是否领取全部
	MailId   uint64               `protobuf:"varint,3,opt,name=mail_id,json=mailId,proto3" json:"mail_id,omitempty"`                             // 邮件ID
	MailType common.MAIL_TYPE     `protobuf:"varint,4,opt,name=mail_type,json=mailType,proto3,enum=common.MAIL_TYPE" json:"mail_type,omitempty"` // 邮件类型
	ItemList *common.ItemBaseList `protobuf:"bytes,5,opt,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`                        // 奖励列表
}

func (x *ClaimRewardAttachRsp) Reset() {
	*x = ClaimRewardAttachRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimRewardAttachRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimRewardAttachRsp) ProtoMessage() {}

func (x *ClaimRewardAttachRsp) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimRewardAttachRsp.ProtoReflect.Descriptor instead.
func (*ClaimRewardAttachRsp) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{5}
}

func (x *ClaimRewardAttachRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ClaimRewardAttachRsp) GetClaimAll() bool {
	if x != nil {
		return x.ClaimAll
	}
	return false
}

func (x *ClaimRewardAttachRsp) GetMailId() uint64 {
	if x != nil {
		return x.MailId
	}
	return 0
}

func (x *ClaimRewardAttachRsp) GetMailType() common.MAIL_TYPE {
	if x != nil {
		return x.MailType
	}
	return common.MAIL_TYPE(0)
}

func (x *ClaimRewardAttachRsp) GetItemList() *common.ItemBaseList {
	if x != nil {
		return x.ItemList
	}
	return nil
}

// 新邮件通知
type NewMailNotify struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MailInfo *common.MailDetailInfo `protobuf:"bytes,1,opt,name=mail_info,json=mailInfo,proto3" json:"mail_info,omitempty"` // 邮件信息
}

func (x *NewMailNotify) Reset() {
	*x = NewMailNotify{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewMailNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewMailNotify) ProtoMessage() {}

func (x *NewMailNotify) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewMailNotify.ProtoReflect.Descriptor instead.
func (*NewMailNotify) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{6}
}

func (x *NewMailNotify) GetMailInfo() *common.MailDetailInfo {
	if x != nil {
		return x.MailInfo
	}
	return nil
}

// 新系统邮件通知
// todo 暂时用空结构体客户端收到后拉取系统邮件
type NewSystemMailNotify struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NewSystemMailNotify) Reset() {
	*x = NewSystemMailNotify{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewSystemMailNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewSystemMailNotify) ProtoMessage() {}

func (x *NewSystemMailNotify) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewSystemMailNotify.ProtoReflect.Descriptor instead.
func (*NewSystemMailNotify) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{7}
}

// 广播
type MsgBroadcastNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgBroadcastInfo *common.MsgBroadcastDetailInfo `protobuf:"bytes,1,opt,name=msg_broadcast_info,json=msgBroadcastInfo,proto3" json:"msg_broadcast_info,omitempty"` // 广播信息
}

func (x *MsgBroadcastNtf) Reset() {
	*x = MsgBroadcastNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgBroadcastNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgBroadcastNtf) ProtoMessage() {}

func (x *MsgBroadcastNtf) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgBroadcastNtf.ProtoReflect.Descriptor instead.
func (*MsgBroadcastNtf) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{8}
}

func (x *MsgBroadcastNtf) GetMsgBroadcastInfo() *common.MsgBroadcastDetailInfo {
	if x != nil {
		return x.MsgBroadcastInfo
	}
	return nil
}

// 读邮件请求
type AnnPopupInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChannelId common.CHANNEL_TYPE `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3,enum=common.CHANNEL_TYPE" json:"channel_id,omitempty"` // 渠道id
	Id        int32               `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                                                         // 拍脸图唯一id
	Enable    int32               `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`                                                 // 是否开启
}

func (x *AnnPopupInfoReq) Reset() {
	*x = AnnPopupInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnPopupInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnPopupInfoReq) ProtoMessage() {}

func (x *AnnPopupInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnPopupInfoReq.ProtoReflect.Descriptor instead.
func (*AnnPopupInfoReq) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{9}
}

func (x *AnnPopupInfoReq) GetChannelId() common.CHANNEL_TYPE {
	if x != nil {
		return x.ChannelId
	}
	return common.CHANNEL_TYPE(0)
}

func (x *AnnPopupInfoReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AnnPopupInfoReq) GetEnable() int32 {
	if x != nil {
		return x.Enable
	}
	return 0
}

type AnnPopupInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret       *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	PopupInfo []*AnnPopup    `protobuf:"bytes,2,rep,name=popup_info,json=popupInfo,proto3" json:"popup_info,omitempty"` // 拍脸图
}

func (x *AnnPopupInfoRes) Reset() {
	*x = AnnPopupInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnPopupInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnPopupInfoRes) ProtoMessage() {}

func (x *AnnPopupInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnPopupInfoRes.ProtoReflect.Descriptor instead.
func (*AnnPopupInfoRes) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{10}
}

func (x *AnnPopupInfoRes) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *AnnPopupInfoRes) GetPopupInfo() []*AnnPopup {
	if x != nil {
		return x.PopupInfo
	}
	return nil
}

// 弹窗公告配置
type AnnPopup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                // 唯一标识（自增主键）
	Priority  int32 `protobuf:"varint,2,opt,name=priority,proto3" json:"priority,omitempty"`                    // 优先级（数值越小越优先）
	ChannelId int32 `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"` // 渠道ID
	PopStyle  int32 `protobuf:"varint,4,opt,name=pop_style,json=popStyle,proto3" json:"pop_style,omitempty"`    // 弹窗样式（1=有关闭按钮）
	// 文本内容配置（JSON格式）
	AnnContent string `protobuf:"bytes,5,opt,name=ann_content,json=annContent,proto3" json:"ann_content,omitempty"`
	// 用户行为配置（JSON格式）
	AnnAction string `protobuf:"bytes,6,opt,name=ann_action,json=annAction,proto3" json:"ann_action,omitempty"`
	// 展示条件配置（JSON格式）
	AnnConditions string `protobuf:"bytes,7,opt,name=ann_conditions,json=annConditions,proto3" json:"ann_conditions,omitempty"`
	Enable        bool   `protobuf:"varint,8,opt,name=enable,proto3" json:"enable,omitempty"`                        // 是否启用（默认true）
	StartTime     int64  `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 生效开始时间
	EndTime       int64  `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`      // 生效结束时间
}

func (x *AnnPopup) Reset() {
	*x = AnnPopup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mail_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnPopup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnPopup) ProtoMessage() {}

func (x *AnnPopup) ProtoReflect() protoreflect.Message {
	mi := &file_mail_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnPopup.ProtoReflect.Descriptor instead.
func (*AnnPopup) Descriptor() ([]byte, []int) {
	return file_mail_proto_rawDescGZIP(), []int{11}
}

func (x *AnnPopup) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AnnPopup) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *AnnPopup) GetChannelId() int32 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *AnnPopup) GetPopStyle() int32 {
	if x != nil {
		return x.PopStyle
	}
	return 0
}

func (x *AnnPopup) GetAnnContent() string {
	if x != nil {
		return x.AnnContent
	}
	return ""
}

func (x *AnnPopup) GetAnnAction() string {
	if x != nil {
		return x.AnnAction
	}
	return ""
}

func (x *AnnPopup) GetAnnConditions() string {
	if x != nil {
		return x.AnnConditions
	}
	return ""
}

func (x *AnnPopup) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *AnnPopup) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *AnnPopup) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

var File_mail_proto protoreflect.FileDescriptor

var file_mail_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x6d, 0x61, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x6d, 0x61,
	0x69, 0x6c, 0x50, 0x42, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2e, 0x0a,
	0x09, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x82, 0x02,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d,
	0x61, 0x69, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x6d,
	0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x75, 0x6e, 0x63, 0x6c, 0x61,
	0x69, 0x6d, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x75, 0x6e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x56, 0x0a, 0x0b, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x61,
	0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x52, 0x08, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x78, 0x0a, 0x0b, 0x52, 0x65,
	0x61, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6d, 0x61,
	0x69, 0x6c, 0x49, 0x64, 0x22, 0x7c, 0x0a, 0x14, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x41, 0x6c, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6d, 0x61, 0x69, 0x6c,
	0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xd1, 0x01, 0x0a, 0x14, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x41, 0x6c, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61,
	0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6d, 0x61, 0x69,
	0x6c, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x69, 0x74,
	0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x44, 0x0a, 0x0d, 0x4e, 0x65, 0x77, 0x4d, 0x61, 0x69,
	0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x33, 0x0a, 0x09, 0x6d, 0x61, 0x69, 0x6c, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x15, 0x0a, 0x13,
	0x4e, 0x65, 0x77, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6c, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x22, 0x5f, 0x0a, 0x0f, 0x4d, 0x73, 0x67, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x4e, 0x74, 0x66, 0x12, 0x4c, 0x0a, 0x12, 0x6d, 0x73, 0x67, 0x5f, 0x62, 0x72,
	0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x73, 0x67, 0x42,
	0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x10, 0x6d, 0x73, 0x67, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6e, 0x0a, 0x0f, 0x41, 0x6e, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x22, 0x64, 0x0a, 0x0f, 0x41, 0x6e, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2f, 0x0a, 0x0a, 0x70, 0x6f, 0x70,
	0x75, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x6d, 0x61, 0x69, 0x6c, 0x50, 0x42, 0x2e, 0x41, 0x6e, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x52,
	0x09, 0x70, 0x6f, 0x70, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xab, 0x02, 0x0a, 0x08, 0x41,
	0x6e, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f, 0x70, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x70, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x6e, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x6e, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x6e, 0x6e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x3d, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x2e,
	0x6b, 0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61,
	0x63, 0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x68, 0x61, 0x6c, 0x6c,
	0x3b, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_mail_proto_rawDescOnce sync.Once
	file_mail_proto_rawDescData = file_mail_proto_rawDesc
)

func file_mail_proto_rawDescGZIP() []byte {
	file_mail_proto_rawDescOnce.Do(func() {
		file_mail_proto_rawDescData = protoimpl.X.CompressGZIP(file_mail_proto_rawDescData)
	})
	return file_mail_proto_rawDescData
}

var file_mail_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_mail_proto_goTypes = []interface{}{
	(*GetMailListReq)(nil),                // 0: mailPB.GetMailListReq
	(*GetMailListRsp)(nil),                // 1: mailPB.GetMailListRsp
	(*ReadMailReq)(nil),                   // 2: mailPB.ReadMailReq
	(*ReadMailRsp)(nil),                   // 3: mailPB.ReadMailRsp
	(*ClaimRewardAttachReq)(nil),          // 4: mailPB.ClaimRewardAttachReq
	(*ClaimRewardAttachRsp)(nil),          // 5: mailPB.ClaimRewardAttachRsp
	(*NewMailNotify)(nil),                 // 6: mailPB.NewMailNotify
	(*NewSystemMailNotify)(nil),           // 7: mailPB.NewSystemMailNotify
	(*MsgBroadcastNtf)(nil),               // 8: mailPB.MsgBroadcastNtf
	(*AnnPopupInfoReq)(nil),               // 9: mailPB.AnnPopupInfoReq
	(*AnnPopupInfoRes)(nil),               // 10: mailPB.AnnPopupInfoRes
	(*AnnPopup)(nil),                      // 11: mailPB.AnnPopup
	(common.MAIL_TYPE)(0),                 // 12: common.MAIL_TYPE
	(*common.Result)(nil),                 // 13: common.Result
	(*common.MailDetailInfo)(nil),         // 14: common.MailDetailInfo
	(*common.ItemBaseList)(nil),           // 15: common.ItemBaseList
	(*common.MsgBroadcastDetailInfo)(nil), // 16: common.MsgBroadcastDetailInfo
	(common.CHANNEL_TYPE)(0),              // 17: common.CHANNEL_TYPE
}
var file_mail_proto_depIdxs = []int32{
	12, // 0: mailPB.GetMailListReq.mail_type:type_name -> common.MAIL_TYPE
	13, // 1: mailPB.GetMailListRsp.ret:type_name -> common.Result
	12, // 2: mailPB.GetMailListRsp.mail_type:type_name -> common.MAIL_TYPE
	14, // 3: mailPB.GetMailListRsp.mail_list:type_name -> common.MailDetailInfo
	12, // 4: mailPB.ReadMailReq.mail_type:type_name -> common.MAIL_TYPE
	13, // 5: mailPB.ReadMailRsp.ret:type_name -> common.Result
	12, // 6: mailPB.ReadMailRsp.mail_type:type_name -> common.MAIL_TYPE
	12, // 7: mailPB.ClaimRewardAttachReq.mail_type:type_name -> common.MAIL_TYPE
	13, // 8: mailPB.ClaimRewardAttachRsp.ret:type_name -> common.Result
	12, // 9: mailPB.ClaimRewardAttachRsp.mail_type:type_name -> common.MAIL_TYPE
	15, // 10: mailPB.ClaimRewardAttachRsp.item_list:type_name -> common.ItemBaseList
	14, // 11: mailPB.NewMailNotify.mail_info:type_name -> common.MailDetailInfo
	16, // 12: mailPB.MsgBroadcastNtf.msg_broadcast_info:type_name -> common.MsgBroadcastDetailInfo
	17, // 13: mailPB.AnnPopupInfoReq.channel_id:type_name -> common.CHANNEL_TYPE
	13, // 14: mailPB.AnnPopupInfoRes.ret:type_name -> common.Result
	11, // 15: mailPB.AnnPopupInfoRes.popup_info:type_name -> mailPB.AnnPopup
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_mail_proto_init() }
func file_mail_proto_init() {
	if File_mail_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_mail_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMailListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMailListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadMailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadMailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimRewardAttachReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimRewardAttachRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewMailNotify); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewSystemMailNotify); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgBroadcastNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnPopupInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnPopupInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mail_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnPopup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_mail_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_mail_proto_goTypes,
		DependencyIndexes: file_mail_proto_depIdxs,
		MessageInfos:      file_mail_proto_msgTypes,
	}.Build()
	File_mail_proto = out.File
	file_mail_proto_rawDesc = nil
	file_mail_proto_goTypes = nil
	file_mail_proto_depIdxs = nil
}
