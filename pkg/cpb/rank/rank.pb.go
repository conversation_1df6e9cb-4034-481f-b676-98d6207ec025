// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: rank.proto

package rankPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 查询排行榜
type GetRankListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`       // 排行榜ID
	Start int32 `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"` // 开始位置
	End   int32 `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`     // 结束位置
}

func (x *GetRankListReq) Reset() {
	*x = GetRankListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rank_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRankListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRankListReq) ProtoMessage() {}

func (x *GetRankListReq) ProtoReflect() protoreflect.Message {
	mi := &file_rank_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRankListReq.ProtoReflect.Descriptor instead.
func (*GetRankListReq) Descriptor() ([]byte, []int) {
	return file_rank_proto_rawDescGZIP(), []int{0}
}

func (x *GetRankListReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetRankListReq) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *GetRankListReq) GetEnd() int32 {
	if x != nil {
		return x.End
	}
	return 0
}

// 查询排行榜
type GetRankListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret   *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`      // 结果
	Id    int64                `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`       // 排行榜ID
	List  []*common.RankPlayer `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`    // 排行榜列表
	Self  *common.RankPlayer   `protobuf:"bytes,4,opt,name=self,proto3" json:"self,omitempty"`    // 自己
	Round int32                `protobuf:"varint,5,opt,name=round,proto3" json:"round,omitempty"` // 排名周期
}

func (x *GetRankListRsp) Reset() {
	*x = GetRankListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rank_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRankListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRankListRsp) ProtoMessage() {}

func (x *GetRankListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_rank_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRankListRsp.ProtoReflect.Descriptor instead.
func (*GetRankListRsp) Descriptor() ([]byte, []int) {
	return file_rank_proto_rawDescGZIP(), []int{1}
}

func (x *GetRankListRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetRankListRsp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetRankListRsp) GetList() []*common.RankPlayer {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetRankListRsp) GetSelf() *common.RankPlayer {
	if x != nil {
		return x.Self
	}
	return nil
}

func (x *GetRankListRsp) GetRound() int32 {
	if x != nil {
		return x.Round
	}
	return 0
}

var File_rank_proto protoreflect.FileDescriptor

var file_rank_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x72, 0x61,
	0x6e, 0x6b, 0x50, 0x42, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x48, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a,
	0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x26, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x61, 0x6e, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x12,
	0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x3d, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65,
	0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d,
	0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x3b, 0x72, 0x61,
	0x6e, 0x6b, 0x50, 0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_rank_proto_rawDescOnce sync.Once
	file_rank_proto_rawDescData = file_rank_proto_rawDesc
)

func file_rank_proto_rawDescGZIP() []byte {
	file_rank_proto_rawDescOnce.Do(func() {
		file_rank_proto_rawDescData = protoimpl.X.CompressGZIP(file_rank_proto_rawDescData)
	})
	return file_rank_proto_rawDescData
}

var file_rank_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rank_proto_goTypes = []interface{}{
	(*GetRankListReq)(nil),    // 0: rankPB.GetRankListReq
	(*GetRankListRsp)(nil),    // 1: rankPB.GetRankListRsp
	(*common.Result)(nil),     // 2: common.Result
	(*common.RankPlayer)(nil), // 3: common.RankPlayer
}
var file_rank_proto_depIdxs = []int32{
	2, // 0: rankPB.GetRankListRsp.ret:type_name -> common.Result
	3, // 1: rankPB.GetRankListRsp.list:type_name -> common.RankPlayer
	3, // 2: rankPB.GetRankListRsp.self:type_name -> common.RankPlayer
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_rank_proto_init() }
func file_rank_proto_init() {
	if File_rank_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_rank_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRankListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rank_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRankListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rank_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rank_proto_goTypes,
		DependencyIndexes: file_rank_proto_depIdxs,
		MessageInfos:      file_rank_proto_msgTypes,
	}.Build()
	File_rank_proto = out.File
	file_rank_proto_rawDesc = nil
	file_rank_proto_goTypes = nil
	file_rank_proto_depIdxs = nil
}
