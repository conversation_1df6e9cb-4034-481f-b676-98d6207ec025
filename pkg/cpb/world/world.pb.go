// 天气系统模块

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: world.proto

package worldPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetWeatherReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId int64 `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"` // 渔场id
	Count  int32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                 // 天数长度 limit<30
}

func (x *GetWeatherReq) Reset() {
	*x = GetWeatherReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_world_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWeatherReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWeatherReq) ProtoMessage() {}

func (x *GetWeatherReq) ProtoReflect() protoreflect.Message {
	mi := &file_world_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWeatherReq.ProtoReflect.Descriptor instead.
func (*GetWeatherReq) Descriptor() ([]byte, []int) {
	return file_world_proto_rawDescGZIP(), []int{0}
}

func (x *GetWeatherReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *GetWeatherReq) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetWeatherRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Data   []*common.WeatherDay `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`                    // 查询列表
	Start  int32                `protobuf:"varint,3,opt,name=start,proto3" json:"start,omitempty"`                 // 开始索引
	End    int32                `protobuf:"varint,4,opt,name=end,proto3" json:"end,omitempty"`                     // 下次索引
	PondId int64                `protobuf:"varint,5,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"` // 渔场id
}

func (x *GetWeatherRsp) Reset() {
	*x = GetWeatherRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_world_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWeatherRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWeatherRsp) ProtoMessage() {}

func (x *GetWeatherRsp) ProtoReflect() protoreflect.Message {
	mi := &file_world_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWeatherRsp.ProtoReflect.Descriptor instead.
func (*GetWeatherRsp) Descriptor() ([]byte, []int) {
	return file_world_proto_rawDescGZIP(), []int{1}
}

func (x *GetWeatherRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetWeatherRsp) GetData() []*common.WeatherDay {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetWeatherRsp) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *GetWeatherRsp) GetEnd() int32 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *GetWeatherRsp) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

type GetGameTimeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetGameTimeReq) Reset() {
	*x = GetGameTimeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_world_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameTimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameTimeReq) ProtoMessage() {}

func (x *GetGameTimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_world_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameTimeReq.ProtoReflect.Descriptor instead.
func (*GetGameTimeReq) Descriptor() ([]byte, []int) {
	return file_world_proto_rawDescGZIP(), []int{2}
}

type GetGameTimeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Data *common.GameTime `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetGameTimeRsp) Reset() {
	*x = GetGameTimeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_world_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameTimeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameTimeRsp) ProtoMessage() {}

func (x *GetGameTimeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_world_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameTimeRsp.ProtoReflect.Descriptor instead.
func (*GetGameTimeRsp) Descriptor() ([]byte, []int) {
	return file_world_proto_rawDescGZIP(), []int{3}
}

func (x *GetGameTimeRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetGameTimeRsp) GetData() *common.GameTime {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetWorldTimeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientTs int64 `protobuf:"varint,1,opt,name=client_ts,json=clientTs,proto3" json:"client_ts,omitempty"` // 客户端时间戳
}

func (x *GetWorldTimeReq) Reset() {
	*x = GetWorldTimeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_world_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorldTimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorldTimeReq) ProtoMessage() {}

func (x *GetWorldTimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_world_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorldTimeReq.ProtoReflect.Descriptor instead.
func (*GetWorldTimeReq) Descriptor() ([]byte, []int) {
	return file_world_proto_rawDescGZIP(), []int{4}
}

func (x *GetWorldTimeReq) GetClientTs() int64 {
	if x != nil {
		return x.ClientTs
	}
	return 0
}

type GetWorldTimeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	ServerTs int64          `protobuf:"varint,2,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"` // 服务器时间戳
	ClientTs int64          `protobuf:"varint,3,opt,name=client_ts,json=clientTs,proto3" json:"client_ts,omitempty"` // 客户端时间戳
}

func (x *GetWorldTimeRsp) Reset() {
	*x = GetWorldTimeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_world_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorldTimeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorldTimeRsp) ProtoMessage() {}

func (x *GetWorldTimeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_world_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorldTimeRsp.ProtoReflect.Descriptor instead.
func (*GetWorldTimeRsp) Descriptor() ([]byte, []int) {
	return file_world_proto_rawDescGZIP(), []int{5}
}

func (x *GetWorldTimeRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetWorldTimeRsp) GetServerTs() int64 {
	if x != nil {
		return x.ServerTs
	}
	return 0
}

func (x *GetWorldTimeRsp) GetClientTs() int64 {
	if x != nil {
		return x.ClientTs
	}
	return 0
}

// 同步更新服务器时间
type UpdateServerTimeNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerTs int64 `protobuf:"varint,1,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"` // 服务器时间戳
}

func (x *UpdateServerTimeNtf) Reset() {
	*x = UpdateServerTimeNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_world_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServerTimeNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServerTimeNtf) ProtoMessage() {}

func (x *UpdateServerTimeNtf) ProtoReflect() protoreflect.Message {
	mi := &file_world_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServerTimeNtf.ProtoReflect.Descriptor instead.
func (*UpdateServerTimeNtf) Descriptor() ([]byte, []int) {
	return file_world_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateServerTimeNtf) GetServerTs() int64 {
	if x != nil {
		return x.ServerTs
	}
	return 0
}

var File_world_proto protoreflect.FileDescriptor

var file_world_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x77,
	0x6f, 0x72, 0x6c, 0x64, 0x50, 0x42, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x3e, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x9a, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65,
	0x72, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x57, 0x65,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x44, 0x61, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x22,
	0x10, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x22, 0x58, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2e, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x73, 0x22, 0x6d, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20,
	0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x73, 0x22, 0x32, 0x0a, 0x13, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x4e, 0x74,
	0x66, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x73, 0x42, 0x3f,
	0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e,
	0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e,
	0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70,
	0x62, 0x2f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x3b, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x50, 0x42, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_world_proto_rawDescOnce sync.Once
	file_world_proto_rawDescData = file_world_proto_rawDesc
)

func file_world_proto_rawDescGZIP() []byte {
	file_world_proto_rawDescOnce.Do(func() {
		file_world_proto_rawDescData = protoimpl.X.CompressGZIP(file_world_proto_rawDescData)
	})
	return file_world_proto_rawDescData
}

var file_world_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_world_proto_goTypes = []interface{}{
	(*GetWeatherReq)(nil),       // 0: worldPB.GetWeatherReq
	(*GetWeatherRsp)(nil),       // 1: worldPB.GetWeatherRsp
	(*GetGameTimeReq)(nil),      // 2: worldPB.GetGameTimeReq
	(*GetGameTimeRsp)(nil),      // 3: worldPB.GetGameTimeRsp
	(*GetWorldTimeReq)(nil),     // 4: worldPB.GetWorldTimeReq
	(*GetWorldTimeRsp)(nil),     // 5: worldPB.GetWorldTimeRsp
	(*UpdateServerTimeNtf)(nil), // 6: worldPB.UpdateServerTimeNtf
	(*common.Result)(nil),       // 7: common.Result
	(*common.WeatherDay)(nil),   // 8: common.WeatherDay
	(*common.GameTime)(nil),     // 9: common.GameTime
}
var file_world_proto_depIdxs = []int32{
	7, // 0: worldPB.GetWeatherRsp.ret:type_name -> common.Result
	8, // 1: worldPB.GetWeatherRsp.data:type_name -> common.WeatherDay
	7, // 2: worldPB.GetGameTimeRsp.ret:type_name -> common.Result
	9, // 3: worldPB.GetGameTimeRsp.data:type_name -> common.GameTime
	7, // 4: worldPB.GetWorldTimeRsp.ret:type_name -> common.Result
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_world_proto_init() }
func file_world_proto_init() {
	if File_world_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_world_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWeatherReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_world_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWeatherRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_world_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameTimeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_world_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameTimeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_world_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorldTimeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_world_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorldTimeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_world_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServerTimeNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_world_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_world_proto_goTypes,
		DependencyIndexes: file_world_proto_depIdxs,
		MessageInfos:      file_world_proto_msgTypes,
	}.Build()
	File_world_proto = out.File
	file_world_proto_rawDesc = nil
	file_world_proto_goTypes = nil
	file_world_proto_depIdxs = nil
}
