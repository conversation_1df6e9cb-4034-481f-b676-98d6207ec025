// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: assetrpc/assetrpc.proto

package assetRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Item Asset操作请求
type OperateItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId     int32                   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`                                        // 产品 ID
	PlayerId      uint64                  `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                                           // 玩家id
	ItemOperation common.ITEM_OPERATION   `protobuf:"varint,3,opt,name=item_operation,json=itemOperation,proto3,enum=common.ITEM_OPERATION" json:"item_operation,omitempty"` // 物品操作类型
	Loots         []*common.OriginLoot    `protobuf:"bytes,4,rep,name=loots,proto3" json:"loots,omitempty"`                                                                  // 物品元数据
	Source        common.ITEM_SOURCE_TYPE `protobuf:"varint,5,opt,name=source,proto3,enum=common.ITEM_SOURCE_TYPE" json:"source,omitempty"`                                  // 来源
	ClaimID       string                  `protobuf:"bytes,6,opt,name=claimID,proto3" json:"claimID,omitempty"`                                                              // 领奖唯一ID 后续做幂等
	Storage       common.STORAGE_TYPE     `protobuf:"varint,7,opt,name=storage,proto3,enum=common.STORAGE_TYPE" json:"storage,omitempty"`
}

func (x *OperateItemReq) Reset() {
	*x = OperateItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_assetrpc_assetrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateItemReq) ProtoMessage() {}

func (x *OperateItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_assetrpc_assetrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateItemReq.ProtoReflect.Descriptor instead.
func (*OperateItemReq) Descriptor() ([]byte, []int) {
	return file_assetrpc_assetrpc_proto_rawDescGZIP(), []int{0}
}

func (x *OperateItemReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *OperateItemReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *OperateItemReq) GetItemOperation() common.ITEM_OPERATION {
	if x != nil {
		return x.ItemOperation
	}
	return common.ITEM_OPERATION(0)
}

func (x *OperateItemReq) GetLoots() []*common.OriginLoot {
	if x != nil {
		return x.Loots
	}
	return nil
}

func (x *OperateItemReq) GetSource() common.ITEM_SOURCE_TYPE {
	if x != nil {
		return x.Source
	}
	return common.ITEM_SOURCE_TYPE(0)
}

func (x *OperateItemReq) GetClaimID() string {
	if x != nil {
		return x.ClaimID
	}
	return ""
}

func (x *OperateItemReq) GetStorage() common.STORAGE_TYPE {
	if x != nil {
		return x.Storage
	}
	return common.STORAGE_TYPE(0)
}

type OperateItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret         *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Transaction string             `protobuf:"bytes,2,opt,name=transaction,proto3" json:"transaction,omitempty"`           // 流水凭证
	Timestamp   int64              `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`              // 时间戳
	ItemList    []*common.ItemInfo `protobuf:"bytes,4,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"` // 物品元数据
}

func (x *OperateItemRsp) Reset() {
	*x = OperateItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_assetrpc_assetrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateItemRsp) ProtoMessage() {}

func (x *OperateItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_assetrpc_assetrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateItemRsp.ProtoReflect.Descriptor instead.
func (*OperateItemRsp) Descriptor() ([]byte, []int) {
	return file_assetrpc_assetrpc_proto_rawDescGZIP(), []int{1}
}

func (x *OperateItemRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *OperateItemRsp) GetTransaction() string {
	if x != nil {
		return x.Transaction
	}
	return ""
}

func (x *OperateItemRsp) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *OperateItemRsp) GetItemList() []*common.ItemInfo {
	if x != nil {
		return x.ItemList
	}
	return nil
}

// 获取玩家Item by itemId
type QueryItemNumReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32               `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` // 产品 ID
	PlayerId  uint64              `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    // 玩家id
	Items     []*common.Item      `protobuf:"bytes,4,rep,name=items,proto3" json:"items,omitempty"`                           // 查询物品
	Storage   common.STORAGE_TYPE `protobuf:"varint,5,opt,name=storage,proto3,enum=common.STORAGE_TYPE" json:"storage,omitempty"`
}

func (x *QueryItemNumReq) Reset() {
	*x = QueryItemNumReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_assetrpc_assetrpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryItemNumReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryItemNumReq) ProtoMessage() {}

func (x *QueryItemNumReq) ProtoReflect() protoreflect.Message {
	mi := &file_assetrpc_assetrpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryItemNumReq.ProtoReflect.Descriptor instead.
func (*QueryItemNumReq) Descriptor() ([]byte, []int) {
	return file_assetrpc_assetrpc_proto_rawDescGZIP(), []int{2}
}

func (x *QueryItemNumReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *QueryItemNumReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *QueryItemNumReq) GetItems() []*common.Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *QueryItemNumReq) GetStorage() common.STORAGE_TYPE {
	if x != nil {
		return x.Storage
	}
	return common.STORAGE_TYPE(0)
}

type QueryItemNumRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	PlayerId uint64             `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
	ItemInfo []*common.ItemInfo `protobuf:"bytes,3,rep,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`  // 物品情况
}

func (x *QueryItemNumRsp) Reset() {
	*x = QueryItemNumRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_assetrpc_assetrpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryItemNumRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryItemNumRsp) ProtoMessage() {}

func (x *QueryItemNumRsp) ProtoReflect() protoreflect.Message {
	mi := &file_assetrpc_assetrpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryItemNumRsp.ProtoReflect.Descriptor instead.
func (*QueryItemNumRsp) Descriptor() ([]byte, []int) {
	return file_assetrpc_assetrpc_proto_rawDescGZIP(), []int{3}
}

func (x *QueryItemNumRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *QueryItemNumRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *QueryItemNumRsp) GetItemInfo() []*common.ItemInfo {
	if x != nil {
		return x.ItemInfo
	}
	return nil
}

// 查询分类
type QueryCategoryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32                  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`               // 产品 ID
	PlayerId  uint64                 `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                  // 玩家id
	Category  []common.ITEM_CATEGORY `protobuf:"varint,3,rep,packed,name=category,proto3,enum=common.ITEM_CATEGORY" json:"category,omitempty"` // 大分类
	Storage   common.STORAGE_TYPE    `protobuf:"varint,4,opt,name=storage,proto3,enum=common.STORAGE_TYPE" json:"storage,omitempty"`
}

func (x *QueryCategoryReq) Reset() {
	*x = QueryCategoryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_assetrpc_assetrpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCategoryReq) ProtoMessage() {}

func (x *QueryCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_assetrpc_assetrpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCategoryReq.ProtoReflect.Descriptor instead.
func (*QueryCategoryReq) Descriptor() ([]byte, []int) {
	return file_assetrpc_assetrpc_proto_rawDescGZIP(), []int{4}
}

func (x *QueryCategoryReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *QueryCategoryReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *QueryCategoryReq) GetCategory() []common.ITEM_CATEGORY {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *QueryCategoryReq) GetStorage() common.STORAGE_TYPE {
	if x != nil {
		return x.Storage
	}
	return common.STORAGE_TYPE(0)
}

type QueryCategoryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	PlayerId uint64             `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
	ItemList []*common.ItemInfo `protobuf:"bytes,3,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`  // 道具
}

func (x *QueryCategoryRsp) Reset() {
	*x = QueryCategoryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_assetrpc_assetrpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCategoryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCategoryRsp) ProtoMessage() {}

func (x *QueryCategoryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_assetrpc_assetrpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCategoryRsp.ProtoReflect.Descriptor instead.
func (*QueryCategoryRsp) Descriptor() ([]byte, []int) {
	return file_assetrpc_assetrpc_proto_rawDescGZIP(), []int{5}
}

func (x *QueryCategoryRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *QueryCategoryRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *QueryCategoryRsp) GetItemList() []*common.ItemInfo {
	if x != nil {
		return x.ItemList
	}
	return nil
}

// 道具转移
type MoveItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32                `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` // 产品 ID
	PlayerId  uint64               `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    // 玩家id
	From      common.STORAGE_TYPE  `protobuf:"varint,3,opt,name=from,proto3,enum=common.STORAGE_TYPE" json:"from,omitempty"`   // 从哪个仓库
	To        common.STORAGE_TYPE  `protobuf:"varint,4,opt,name=to,proto3,enum=common.STORAGE_TYPE" json:"to,omitempty"`       // 移动到哪个仓库
	ItemList  []*common.OriginLoot `protobuf:"bytes,5,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
}

func (x *MoveItemReq) Reset() {
	*x = MoveItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_assetrpc_assetrpc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoveItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveItemReq) ProtoMessage() {}

func (x *MoveItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_assetrpc_assetrpc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveItemReq.ProtoReflect.Descriptor instead.
func (*MoveItemReq) Descriptor() ([]byte, []int) {
	return file_assetrpc_assetrpc_proto_rawDescGZIP(), []int{6}
}

func (x *MoveItemReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *MoveItemReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *MoveItemReq) GetFrom() common.STORAGE_TYPE {
	if x != nil {
		return x.From
	}
	return common.STORAGE_TYPE(0)
}

func (x *MoveItemReq) GetTo() common.STORAGE_TYPE {
	if x != nil {
		return x.To
	}
	return common.STORAGE_TYPE(0)
}

func (x *MoveItemReq) GetItemList() []*common.OriginLoot {
	if x != nil {
		return x.ItemList
	}
	return nil
}

type MoveItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result      `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	From       common.STORAGE_TYPE `protobuf:"varint,3,opt,name=from,proto3,enum=common.STORAGE_TYPE" json:"from,omitempty"` // 从哪个仓库
	To         common.STORAGE_TYPE `protobuf:"varint,4,opt,name=to,proto3,enum=common.STORAGE_TYPE" json:"to,omitempty"`     // 移动到哪个仓库
	FromChange []*common.ItemInfo  `protobuf:"bytes,5,rep,name=from_change,json=fromChange,proto3" json:"from_change,omitempty"`
	ToChange   []*common.ItemInfo  `protobuf:"bytes,6,rep,name=to_change,json=toChange,proto3" json:"to_change,omitempty"`
}

func (x *MoveItemRsp) Reset() {
	*x = MoveItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_assetrpc_assetrpc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoveItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveItemRsp) ProtoMessage() {}

func (x *MoveItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_assetrpc_assetrpc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveItemRsp.ProtoReflect.Descriptor instead.
func (*MoveItemRsp) Descriptor() ([]byte, []int) {
	return file_assetrpc_assetrpc_proto_rawDescGZIP(), []int{7}
}

func (x *MoveItemRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *MoveItemRsp) GetFrom() common.STORAGE_TYPE {
	if x != nil {
		return x.From
	}
	return common.STORAGE_TYPE(0)
}

func (x *MoveItemRsp) GetTo() common.STORAGE_TYPE {
	if x != nil {
		return x.To
	}
	return common.STORAGE_TYPE(0)
}

func (x *MoveItemRsp) GetFromChange() []*common.ItemInfo {
	if x != nil {
		return x.FromChange
	}
	return nil
}

func (x *MoveItemRsp) GetToChange() []*common.ItemInfo {
	if x != nil {
		return x.ToChange
	}
	return nil
}

var File_assetrpc_assetrpc_proto protoreflect.FileDescriptor

var file_assetrpc_assetrpc_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x73, 0x73, 0x65, 0x74, 0x72, 0x70, 0x63, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x70, 0x63, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x02, 0x0a, 0x0e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0e, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d,
	0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x52, 0x0d, 0x69, 0x74, 0x65, 0x6d,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x05, 0x6c, 0x6f, 0x6f,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4c, 0x6f, 0x6f, 0x74, 0x52, 0x05, 0x6c, 0x6f,
	0x6f, 0x74, 0x73, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45,
	0x4d, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x49, 0x44,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x49, 0x44, 0x12,
	0x2e, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x22,
	0xa1, 0x01, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x03, 0x72, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x2d, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0xa1, 0x01, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x49, 0x74, 0x65,
	0x6d, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x22, 0x7f, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x49, 0x74, 0x65, 0x6d, 0x4e, 0x75, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x69, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xb1, 0x01, 0x0a, 0x10, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x2e, 0x0a, 0x07,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x22, 0x80, 0x01, 0x0a,
	0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03,
	0x72, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0xca, 0x01, 0x0a, 0x0b, 0x4d, 0x6f, 0x76, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x04, 0x66,
	0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x24, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52, 0x41,
	0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x2f, 0x0a, 0x09, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4c, 0x6f,
	0x6f, 0x74, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xe1, 0x01, 0x0a,
	0x0b, 0x4d, 0x6f, 0x76, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03,
	0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x28,
	0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x24, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54,
	0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x31,
	0x0a, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74,
	0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x32, 0xa2, 0x02, 0x0a, 0x0c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x43, 0x0a, 0x0b, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x18, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x4e, 0x75, 0x6d, 0x12, 0x19, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x70,
	0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x4e, 0x75, 0x6d, 0x52, 0x65,
	0x71, 0x1a, 0x19, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x4e, 0x75, 0x6d, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x49,
	0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x1a, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3a, 0x0a, 0x08, 0x4d, 0x6f, 0x76,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x15, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x70, 0x63,
	0x2e, 0x4d, 0x6f, 0x76, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x4d, 0x6f, 0x76, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x3b, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_assetrpc_assetrpc_proto_rawDescOnce sync.Once
	file_assetrpc_assetrpc_proto_rawDescData = file_assetrpc_assetrpc_proto_rawDesc
)

func file_assetrpc_assetrpc_proto_rawDescGZIP() []byte {
	file_assetrpc_assetrpc_proto_rawDescOnce.Do(func() {
		file_assetrpc_assetrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_assetrpc_assetrpc_proto_rawDescData)
	})
	return file_assetrpc_assetrpc_proto_rawDescData
}

var file_assetrpc_assetrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_assetrpc_assetrpc_proto_goTypes = []interface{}{
	(*OperateItemReq)(nil),       // 0: assetRpc.OperateItemReq
	(*OperateItemRsp)(nil),       // 1: assetRpc.OperateItemRsp
	(*QueryItemNumReq)(nil),      // 2: assetRpc.QueryItemNumReq
	(*QueryItemNumRsp)(nil),      // 3: assetRpc.QueryItemNumRsp
	(*QueryCategoryReq)(nil),     // 4: assetRpc.QueryCategoryReq
	(*QueryCategoryRsp)(nil),     // 5: assetRpc.QueryCategoryRsp
	(*MoveItemReq)(nil),          // 6: assetRpc.MoveItemReq
	(*MoveItemRsp)(nil),          // 7: assetRpc.MoveItemRsp
	(common.ITEM_OPERATION)(0),   // 8: common.ITEM_OPERATION
	(*common.OriginLoot)(nil),    // 9: common.OriginLoot
	(common.ITEM_SOURCE_TYPE)(0), // 10: common.ITEM_SOURCE_TYPE
	(common.STORAGE_TYPE)(0),     // 11: common.STORAGE_TYPE
	(*common.Result)(nil),        // 12: common.Result
	(*common.ItemInfo)(nil),      // 13: common.ItemInfo
	(*common.Item)(nil),          // 14: common.Item
	(common.ITEM_CATEGORY)(0),    // 15: common.ITEM_CATEGORY
}
var file_assetrpc_assetrpc_proto_depIdxs = []int32{
	8,  // 0: assetRpc.OperateItemReq.item_operation:type_name -> common.ITEM_OPERATION
	9,  // 1: assetRpc.OperateItemReq.loots:type_name -> common.OriginLoot
	10, // 2: assetRpc.OperateItemReq.source:type_name -> common.ITEM_SOURCE_TYPE
	11, // 3: assetRpc.OperateItemReq.storage:type_name -> common.STORAGE_TYPE
	12, // 4: assetRpc.OperateItemRsp.ret:type_name -> common.Result
	13, // 5: assetRpc.OperateItemRsp.item_list:type_name -> common.ItemInfo
	14, // 6: assetRpc.QueryItemNumReq.items:type_name -> common.Item
	11, // 7: assetRpc.QueryItemNumReq.storage:type_name -> common.STORAGE_TYPE
	12, // 8: assetRpc.QueryItemNumRsp.ret:type_name -> common.Result
	13, // 9: assetRpc.QueryItemNumRsp.item_info:type_name -> common.ItemInfo
	15, // 10: assetRpc.QueryCategoryReq.category:type_name -> common.ITEM_CATEGORY
	11, // 11: assetRpc.QueryCategoryReq.storage:type_name -> common.STORAGE_TYPE
	12, // 12: assetRpc.QueryCategoryRsp.ret:type_name -> common.Result
	13, // 13: assetRpc.QueryCategoryRsp.item_list:type_name -> common.ItemInfo
	11, // 14: assetRpc.MoveItemReq.from:type_name -> common.STORAGE_TYPE
	11, // 15: assetRpc.MoveItemReq.to:type_name -> common.STORAGE_TYPE
	9,  // 16: assetRpc.MoveItemReq.item_list:type_name -> common.OriginLoot
	12, // 17: assetRpc.MoveItemRsp.ret:type_name -> common.Result
	11, // 18: assetRpc.MoveItemRsp.from:type_name -> common.STORAGE_TYPE
	11, // 19: assetRpc.MoveItemRsp.to:type_name -> common.STORAGE_TYPE
	13, // 20: assetRpc.MoveItemRsp.from_change:type_name -> common.ItemInfo
	13, // 21: assetRpc.MoveItemRsp.to_change:type_name -> common.ItemInfo
	0,  // 22: assetRpc.AssetService.OperateItem:input_type -> assetRpc.OperateItemReq
	2,  // 23: assetRpc.AssetService.QueryItemNum:input_type -> assetRpc.QueryItemNumReq
	4,  // 24: assetRpc.AssetService.QueryCategory:input_type -> assetRpc.QueryCategoryReq
	6,  // 25: assetRpc.AssetService.MoveItem:input_type -> assetRpc.MoveItemReq
	1,  // 26: assetRpc.AssetService.OperateItem:output_type -> assetRpc.OperateItemRsp
	3,  // 27: assetRpc.AssetService.QueryItemNum:output_type -> assetRpc.QueryItemNumRsp
	5,  // 28: assetRpc.AssetService.QueryCategory:output_type -> assetRpc.QueryCategoryRsp
	7,  // 29: assetRpc.AssetService.MoveItem:output_type -> assetRpc.MoveItemRsp
	26, // [26:30] is the sub-list for method output_type
	22, // [22:26] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_assetrpc_assetrpc_proto_init() }
func file_assetrpc_assetrpc_proto_init() {
	if File_assetrpc_assetrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_assetrpc_assetrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_assetrpc_assetrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_assetrpc_assetrpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryItemNumReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_assetrpc_assetrpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryItemNumRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_assetrpc_assetrpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCategoryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_assetrpc_assetrpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCategoryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_assetrpc_assetrpc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoveItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_assetrpc_assetrpc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoveItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_assetrpc_assetrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_assetrpc_assetrpc_proto_goTypes,
		DependencyIndexes: file_assetrpc_assetrpc_proto_depIdxs,
		MessageInfos:      file_assetrpc_assetrpc_proto_msgTypes,
	}.Build()
	File_assetrpc_assetrpc_proto = out.File
	file_assetrpc_assetrpc_proto_rawDesc = nil
	file_assetrpc_assetrpc_proto_goTypes = nil
	file_assetrpc_assetrpc_proto_depIdxs = nil
}
