syntax = "proto3";

package assetRpc;
option go_package = ".;assetRpc";

import "common.proto";
import "enum.proto";
import "errors.proto";

// Item Asset操作请求
message OperateItemReq {
    int32                   product_id               = 1;  // 产品 ID
    uint64                  player_id                = 2;  // 玩家id
    common.ITEM_OPERATION   item_operation           = 3;  // 物品操作类型
    repeated                common.OriginLoot  loots = 4;  // 物品元数据
    common.ITEM_SOURCE_TYPE source                   = 5;  // 来源
    string                  claimID                  = 6;  // 领奖唯一ID 后续做幂等
    common.STORAGE_TYPE   storage = 7;
}

message OperateItemRsp {
    common.Result ret                        = 1;
    string        transaction                = 2;  // 流水凭证
    int64         timestamp                  = 3;  // 时间戳
    repeated      common.ItemInfo  item_list = 4;  // 物品元数据
}

// 获取玩家Item by itemId
message QueryItemNumReq {
    int32                       product_id        = 1; // 产品 ID
    uint64                      player_id         = 2; // 玩家id
    repeated common.Item        items             = 4; // 查询物品
    common.STORAGE_TYPE   storage = 5;
}

message QueryItemNumRsp {
    common.Result               ret               = 1;
    uint64                      player_id         = 2; // 玩家id
    repeated common.ItemInfo    item_info         = 3; // 物品情况
}

// 查询分类
message QueryCategoryReq {
    int32                       product_id        = 1; // 产品 ID
    uint64                      player_id         = 2; // 玩家id
    repeated common.ITEM_CATEGORY category          = 3; // 大分类
    common.STORAGE_TYPE   storage = 4;
}

message QueryCategoryRsp {
    common.Result ret                       = 1;
    uint64        player_id                 = 2;  // 玩家id
    repeated      common.ItemInfo     item_list = 3;  // 道具
}

// 道具转移
message MoveItemReq {
    int32               product_id                    = 1;  // 产品 ID
    uint64              player_id                     = 2;  // 玩家id
    common.STORAGE_TYPE from                          = 3;  // 从哪个仓库
    common.STORAGE_TYPE to                            = 4;  // 移动到哪个仓库
    repeated            common.OriginLoot     item_list = 5;
}

message MoveItemRsp {
    common.Result ret                       = 1;
    common.STORAGE_TYPE from                          = 3;  // 从哪个仓库
    common.STORAGE_TYPE to                            = 4;  // 移动到哪个仓库
    repeated            common.ItemInfo     from_change = 5;
    repeated            common.ItemInfo     to_change = 6;
}

// RPC服务
service AssetService {
    // 操作玩家Item数量
    rpc OperateItem(OperateItemReq) returns (OperateItemRsp) {}
    // 获取当前Item数量
    rpc QueryItemNum(QueryItemNumReq) returns (QueryItemNumRsp) {}
    // 查询当前category 类所有道具
    rpc QueryCategory(QueryCategoryReq) returns (QueryCategoryRsp) {}
    // 从不同仓库转移
    rpc MoveItem(MoveItemReq) returns (MoveItemRsp) {}
}