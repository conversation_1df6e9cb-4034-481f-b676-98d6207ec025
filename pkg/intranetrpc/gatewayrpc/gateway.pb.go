// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: gatewayrpc/gateway.proto

package gatewayrpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AnotherLoginRequest 顶号请求
type AnotherLoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家 ID
	Device   string `protobuf:"bytes,2,opt,name=device,proto3" json:"device,omitempty"`                      // 登录设备名
	Imei     string `protobuf:"bytes,3,opt,name=imei,proto3" json:"imei,omitempty"`                          // 设备 imei
	OriImei  string `protobuf:"bytes,4,opt,name=ori_imei,json=oriImei,proto3" json:"ori_imei,omitempty"`     // 原先设备 imei
}

func (x *AnotherLoginRequest) Reset() {
	*x = AnotherLoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gatewayrpc_gateway_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnotherLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnotherLoginRequest) ProtoMessage() {}

func (x *AnotherLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gatewayrpc_gateway_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnotherLoginRequest.ProtoReflect.Descriptor instead.
func (*AnotherLoginRequest) Descriptor() ([]byte, []int) {
	return file_gatewayrpc_gateway_proto_rawDescGZIP(), []int{0}
}

func (x *AnotherLoginRequest) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *AnotherLoginRequest) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

func (x *AnotherLoginRequest) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *AnotherLoginRequest) GetOriImei() string {
	if x != nil {
		return x.OriImei
	}
	return ""
}

// AnotherLoginResponse 顶号应答
type AnotherLoginResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AnotherLoginResponse) Reset() {
	*x = AnotherLoginResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gatewayrpc_gateway_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnotherLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnotherLoginResponse) ProtoMessage() {}

func (x *AnotherLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gatewayrpc_gateway_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnotherLoginResponse.ProtoReflect.Descriptor instead.
func (*AnotherLoginResponse) Descriptor() ([]byte, []int) {
	return file_gatewayrpc_gateway_proto_rawDescGZIP(), []int{1}
}

// GetPlayerGatewayAddrRequest 获取玩家所在网关地址请求
type GetPlayerGatewayAddrRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerIds []uint64 `protobuf:"varint,1,rep,packed,name=player_ids,json=playerIds,proto3" json:"player_ids,omitempty"`
}

func (x *GetPlayerGatewayAddrRequest) Reset() {
	*x = GetPlayerGatewayAddrRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gatewayrpc_gateway_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerGatewayAddrRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerGatewayAddrRequest) ProtoMessage() {}

func (x *GetPlayerGatewayAddrRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gatewayrpc_gateway_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerGatewayAddrRequest.ProtoReflect.Descriptor instead.
func (*GetPlayerGatewayAddrRequest) Descriptor() ([]byte, []int) {
	return file_gatewayrpc_gateway_proto_rawDescGZIP(), []int{2}
}

func (x *GetPlayerGatewayAddrRequest) GetPlayerIds() []uint64 {
	if x != nil {
		return x.PlayerIds
	}
	return nil
}

// GetPlayerGatewayAddrResponse 获取玩家所在网关地址应答
type GetPlayerGatewayAddrResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addrs map[uint64]string `protobuf:"bytes,1,rep,name=addrs,proto3" json:"addrs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetPlayerGatewayAddrResponse) Reset() {
	*x = GetPlayerGatewayAddrResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gatewayrpc_gateway_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerGatewayAddrResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerGatewayAddrResponse) ProtoMessage() {}

func (x *GetPlayerGatewayAddrResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gatewayrpc_gateway_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerGatewayAddrResponse.ProtoReflect.Descriptor instead.
func (*GetPlayerGatewayAddrResponse) Descriptor() ([]byte, []int) {
	return file_gatewayrpc_gateway_proto_rawDescGZIP(), []int{3}
}

func (x *GetPlayerGatewayAddrResponse) GetAddrs() map[uint64]string {
	if x != nil {
		return x.Addrs
	}
	return nil
}

// GetPlayerIPRequest 获取玩家ip地址请求
type GetPlayerIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerIds []uint64 `protobuf:"varint,1,rep,packed,name=player_ids,json=playerIds,proto3" json:"player_ids,omitempty"`
}

func (x *GetPlayerIPRequest) Reset() {
	*x = GetPlayerIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gatewayrpc_gateway_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerIPRequest) ProtoMessage() {}

func (x *GetPlayerIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gatewayrpc_gateway_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerIPRequest.ProtoReflect.Descriptor instead.
func (*GetPlayerIPRequest) Descriptor() ([]byte, []int) {
	return file_gatewayrpc_gateway_proto_rawDescGZIP(), []int{4}
}

func (x *GetPlayerIPRequest) GetPlayerIds() []uint64 {
	if x != nil {
		return x.PlayerIds
	}
	return nil
}

// GetPlayerIPResponse 获取玩家ip地址响应
type GetPlayerIPResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addrs map[uint64]string `protobuf:"bytes,1,rep,name=addrs,proto3" json:"addrs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetPlayerIPResponse) Reset() {
	*x = GetPlayerIPResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gatewayrpc_gateway_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerIPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerIPResponse) ProtoMessage() {}

func (x *GetPlayerIPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gatewayrpc_gateway_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerIPResponse.ProtoReflect.Descriptor instead.
func (*GetPlayerIPResponse) Descriptor() ([]byte, []int) {
	return file_gatewayrpc_gateway_proto_rawDescGZIP(), []int{5}
}

func (x *GetPlayerIPResponse) GetAddrs() map[uint64]string {
	if x != nil {
		return x.Addrs
	}
	return nil
}

var File_gatewayrpc_gateway_proto protoreflect.FileDescriptor

var file_gatewayrpc_gateway_proto_rawDesc = []byte{
	0x0a, 0x18, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x72, 0x70, 0x63, 0x2f, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x22, 0x79, 0x0a, 0x13, 0x41, 0x6e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69,
	0x6d, 0x65, 0x69, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x69, 0x5f, 0x69, 0x6d, 0x65, 0x69, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x69, 0x49, 0x6d, 0x65, 0x69, 0x22, 0x16,
	0x0a, 0x14, 0x41, 0x6e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3c, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x41, 0x64, 0x64, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x41, 0x64, 0x64, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x05, 0x61, 0x64, 0x64, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x72,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x61, 0x64, 0x64, 0x72, 0x73, 0x1a, 0x38, 0x0a,
	0x0a, 0x41, 0x64, 0x64, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x33, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x8e, 0x01, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x50, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x61, 0x64, 0x64, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x61, 0x64,
	0x64, 0x72, 0x73, 0x1a, 0x38, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x8f, 0x02,
	0x0a, 0x0b, 0x47, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4d, 0x0a,
	0x0c, 0x41, 0x6e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x1c, 0x2e,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x41, 0x6e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x41, 0x6e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x41, 0x64, 0x64, 0x72, 0x12, 0x24, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x41, 0x64, 0x64, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x50, 0x12, 0x1b, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1c, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42,
	0x0f, 0x5a, 0x0d, 0x2e, 0x2f, 0x3b, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x72, 0x70, 0x63,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_gatewayrpc_gateway_proto_rawDescOnce sync.Once
	file_gatewayrpc_gateway_proto_rawDescData = file_gatewayrpc_gateway_proto_rawDesc
)

func file_gatewayrpc_gateway_proto_rawDescGZIP() []byte {
	file_gatewayrpc_gateway_proto_rawDescOnce.Do(func() {
		file_gatewayrpc_gateway_proto_rawDescData = protoimpl.X.CompressGZIP(file_gatewayrpc_gateway_proto_rawDescData)
	})
	return file_gatewayrpc_gateway_proto_rawDescData
}

var file_gatewayrpc_gateway_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_gatewayrpc_gateway_proto_goTypes = []interface{}{
	(*AnotherLoginRequest)(nil),          // 0: gateway.AnotherLoginRequest
	(*AnotherLoginResponse)(nil),         // 1: gateway.AnotherLoginResponse
	(*GetPlayerGatewayAddrRequest)(nil),  // 2: gateway.GetPlayerGatewayAddrRequest
	(*GetPlayerGatewayAddrResponse)(nil), // 3: gateway.GetPlayerGatewayAddrResponse
	(*GetPlayerIPRequest)(nil),           // 4: gateway.GetPlayerIPRequest
	(*GetPlayerIPResponse)(nil),          // 5: gateway.GetPlayerIPResponse
	nil,                                  // 6: gateway.GetPlayerGatewayAddrResponse.AddrsEntry
	nil,                                  // 7: gateway.GetPlayerIPResponse.AddrsEntry
}
var file_gatewayrpc_gateway_proto_depIdxs = []int32{
	6, // 0: gateway.GetPlayerGatewayAddrResponse.addrs:type_name -> gateway.GetPlayerGatewayAddrResponse.AddrsEntry
	7, // 1: gateway.GetPlayerIPResponse.addrs:type_name -> gateway.GetPlayerIPResponse.AddrsEntry
	0, // 2: gateway.GateService.AnotherLogin:input_type -> gateway.AnotherLoginRequest
	2, // 3: gateway.GateService.GetPlayerGatewayAddr:input_type -> gateway.GetPlayerGatewayAddrRequest
	4, // 4: gateway.GateService.GetPlayerIP:input_type -> gateway.GetPlayerIPRequest
	1, // 5: gateway.GateService.AnotherLogin:output_type -> gateway.AnotherLoginResponse
	3, // 6: gateway.GateService.GetPlayerGatewayAddr:output_type -> gateway.GetPlayerGatewayAddrResponse
	5, // 7: gateway.GateService.GetPlayerIP:output_type -> gateway.GetPlayerIPResponse
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_gatewayrpc_gateway_proto_init() }
func file_gatewayrpc_gateway_proto_init() {
	if File_gatewayrpc_gateway_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_gatewayrpc_gateway_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnotherLoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gatewayrpc_gateway_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnotherLoginResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gatewayrpc_gateway_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerGatewayAddrRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gatewayrpc_gateway_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerGatewayAddrResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gatewayrpc_gateway_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gatewayrpc_gateway_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerIPResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gatewayrpc_gateway_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_gatewayrpc_gateway_proto_goTypes,
		DependencyIndexes: file_gatewayrpc_gateway_proto_depIdxs,
		MessageInfos:      file_gatewayrpc_gateway_proto_msgTypes,
	}.Build()
	File_gatewayrpc_gateway_proto = out.File
	file_gatewayrpc_gateway_proto_rawDesc = nil
	file_gatewayrpc_gateway_proto_goTypes = nil
	file_gatewayrpc_gateway_proto_depIdxs = nil
}
