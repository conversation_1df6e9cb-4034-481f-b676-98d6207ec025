syntax = "proto3";

package gateway;
option go_package = "./;gatewayrpc";

// AnotherLoginRequest 顶号请求
message AnotherLoginRequest {
    uint64 player_id = 1; // 玩家 ID
    string device    = 2;    // 登录设备名
    string imei      = 3;      // 设备 imei
    string ori_imei  = 4;    // 原先设备 imei
}

// AnotherLoginResponse 顶号应答
message AnotherLoginResponse {}

// GetPlayerGatewayAddrRequest 获取玩家所在网关地址请求
message GetPlayerGatewayAddrRequest {
    repeated uint64 player_ids = 1;
}

// GetPlayerGatewayAddrResponse 获取玩家所在网关地址应答
message GetPlayerGatewayAddrResponse {
    map<uint64, string> addrs = 1;
}

// GetPlayerIPRequest 获取玩家ip地址请求
message GetPlayerIPRequest {
    repeated uint64 player_ids = 1;
}

// GetPlayerIPResponse 获取玩家ip地址响应
message GetPlayerIPResponse {
    map<uint64, string> addrs = 1;
}

// RPC服务
service GateService {
    rpc AnotherLogin(AnotherLoginRequest) returns (AnotherLoginResponse) {}
    rpc GetPlayerGatewayAddr(GetPlayerGatewayAddrRequest) returns (GetPlayerGatewayAddrResponse) {}
    rpc GetPlayerIP(GetPlayerIPRequest) returns (GetPlayerIPResponse) {}
}