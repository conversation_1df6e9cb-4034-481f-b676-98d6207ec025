// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: gatewayrpc/gateway.proto

package gatewayrpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	GateService_AnotherLogin_FullMethodName         = "/gateway.GateService/AnotherLogin"
	GateService_GetPlayerGatewayAddr_FullMethodName = "/gateway.GateService/GetPlayerGatewayAddr"
	GateService_GetPlayerIP_FullMethodName          = "/gateway.GateService/GetPlayerIP"
)

// GateServiceClient is the client API for GateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GateServiceClient interface {
	AnotherLogin(ctx context.Context, in *AnotherLoginRequest, opts ...grpc.CallOption) (*AnotherLoginResponse, error)
	GetPlayerGatewayAddr(ctx context.Context, in *GetPlayerGatewayAddrRequest, opts ...grpc.CallOption) (*GetPlayerGatewayAddrResponse, error)
	GetPlayerIP(ctx context.Context, in *GetPlayerIPRequest, opts ...grpc.CallOption) (*GetPlayerIPResponse, error)
}

type gateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGateServiceClient(cc grpc.ClientConnInterface) GateServiceClient {
	return &gateServiceClient{cc}
}

func (c *gateServiceClient) AnotherLogin(ctx context.Context, in *AnotherLoginRequest, opts ...grpc.CallOption) (*AnotherLoginResponse, error) {
	out := new(AnotherLoginResponse)
	err := c.cc.Invoke(ctx, GateService_AnotherLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gateServiceClient) GetPlayerGatewayAddr(ctx context.Context, in *GetPlayerGatewayAddrRequest, opts ...grpc.CallOption) (*GetPlayerGatewayAddrResponse, error) {
	out := new(GetPlayerGatewayAddrResponse)
	err := c.cc.Invoke(ctx, GateService_GetPlayerGatewayAddr_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gateServiceClient) GetPlayerIP(ctx context.Context, in *GetPlayerIPRequest, opts ...grpc.CallOption) (*GetPlayerIPResponse, error) {
	out := new(GetPlayerIPResponse)
	err := c.cc.Invoke(ctx, GateService_GetPlayerIP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GateServiceServer is the server API for GateService service.
// All implementations should embed UnimplementedGateServiceServer
// for forward compatibility
type GateServiceServer interface {
	AnotherLogin(context.Context, *AnotherLoginRequest) (*AnotherLoginResponse, error)
	GetPlayerGatewayAddr(context.Context, *GetPlayerGatewayAddrRequest) (*GetPlayerGatewayAddrResponse, error)
	GetPlayerIP(context.Context, *GetPlayerIPRequest) (*GetPlayerIPResponse, error)
}

// UnimplementedGateServiceServer should be embedded to have forward compatible implementations.
type UnimplementedGateServiceServer struct {
}

func (UnimplementedGateServiceServer) AnotherLogin(context.Context, *AnotherLoginRequest) (*AnotherLoginResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AnotherLogin not implemented")
}
func (UnimplementedGateServiceServer) GetPlayerGatewayAddr(context.Context, *GetPlayerGatewayAddrRequest) (*GetPlayerGatewayAddrResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlayerGatewayAddr not implemented")
}
func (UnimplementedGateServiceServer) GetPlayerIP(context.Context, *GetPlayerIPRequest) (*GetPlayerIPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlayerIP not implemented")
}

// UnsafeGateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GateServiceServer will
// result in compilation errors.
type UnsafeGateServiceServer interface {
	mustEmbedUnimplementedGateServiceServer()
}

func RegisterGateServiceServer(s grpc.ServiceRegistrar, srv GateServiceServer) {
	s.RegisterService(&GateService_ServiceDesc, srv)
}

func _GateService_AnotherLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnotherLoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GateServiceServer).AnotherLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GateService_AnotherLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GateServiceServer).AnotherLogin(ctx, req.(*AnotherLoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GateService_GetPlayerGatewayAddr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerGatewayAddrRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GateServiceServer).GetPlayerGatewayAddr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GateService_GetPlayerGatewayAddr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GateServiceServer).GetPlayerGatewayAddr(ctx, req.(*GetPlayerGatewayAddrRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GateService_GetPlayerIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GateServiceServer).GetPlayerIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GateService_GetPlayerIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GateServiceServer).GetPlayerIP(ctx, req.(*GetPlayerIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GateService_ServiceDesc is the grpc.ServiceDesc for GateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "gateway.GateService",
	HandlerType: (*GateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AnotherLogin",
			Handler:    _GateService_AnotherLogin_Handler,
		},
		{
			MethodName: "GetPlayerGatewayAddr",
			Handler:    _GateService_GetPlayerGatewayAddr_Handler,
		},
		{
			MethodName: "GetPlayerIP",
			Handler:    _GateService_GetPlayerIP_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gatewayrpc/gateway.proto",
}
