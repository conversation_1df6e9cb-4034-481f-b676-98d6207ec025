// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: gmrpc/gmrpc.proto

package gmRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Gm内部请求
type GmCmdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid  common.PRODUCT_ID `protobuf:"varint,1,opt,name=pid,proto3,enum=common.PRODUCT_ID" json:"pid,omitempty"`
	Oid  string            `protobuf:"bytes,2,opt,name=oid,proto3" json:"oid,omitempty"`
	Data string            `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                   // 请求数据 json打包
	Cmd  common.GM_CMD     `protobuf:"varint,4,opt,name=cmd,proto3,enum=common.GM_CMD" json:"cmd,omitempty"` // 操作命令字
}

func (x *GmCmdReq) Reset() {
	*x = GmCmdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gmrpc_gmrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdReq) ProtoMessage() {}

func (x *GmCmdReq) ProtoReflect() protoreflect.Message {
	mi := &file_gmrpc_gmrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdReq.ProtoReflect.Descriptor instead.
func (*GmCmdReq) Descriptor() ([]byte, []int) {
	return file_gmrpc_gmrpc_proto_rawDescGZIP(), []int{0}
}

func (x *GmCmdReq) GetPid() common.PRODUCT_ID {
	if x != nil {
		return x.Pid
	}
	return common.PRODUCT_ID(0)
}

func (x *GmCmdReq) GetOid() string {
	if x != nil {
		return x.Oid
	}
	return ""
}

func (x *GmCmdReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *GmCmdReq) GetCmd() common.GM_CMD {
	if x != nil {
		return x.Cmd
	}
	return common.GM_CMD(0)
}

type GmCmdRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Data string         `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"` // 回复数据打包 JSON
}

func (x *GmCmdRsp) Reset() {
	*x = GmCmdRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gmrpc_gmrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdRsp) ProtoMessage() {}

func (x *GmCmdRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gmrpc_gmrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdRsp.ProtoReflect.Descriptor instead.
func (*GmCmdRsp) Descriptor() ([]byte, []int) {
	return file_gmrpc_gmrpc_proto_rawDescGZIP(), []int{1}
}

func (x *GmCmdRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GmCmdRsp) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

var File_gmrpc_gmrpc_proto protoreflect.FileDescriptor

var file_gmrpc_gmrpc_proto_rawDesc = []byte{
	0x0a, 0x11, 0x67, 0x6d, 0x72, 0x70, 0x63, 0x2f, 0x67, 0x6d, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x67, 0x6d, 0x52, 0x70, 0x63, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x78, 0x0a, 0x08, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x24, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x49,
	0x44, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x03,
	0x63, 0x6d, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x47, 0x4d, 0x5f, 0x43, 0x4d, 0x44, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x22, 0x40,
	0x0a, 0x08, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x32, 0x65, 0x0a, 0x09, 0x47, 0x6d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x27, 0x0a,
	0x03, 0x43, 0x6d, 0x64, 0x12, 0x0f, 0x2e, 0x67, 0x6d, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x6d, 0x43,
	0x6d, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x0f, 0x2e, 0x67, 0x6d, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x6d,
	0x43, 0x6d, 0x64, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x0b, 0x53, 0x65, 0x74, 0x54, 0x65, 0x73,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x0f, 0x2e, 0x67, 0x6d, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x6d,
	0x43, 0x6d, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x0f, 0x2e, 0x67, 0x6d, 0x52, 0x70, 0x63, 0x2e, 0x47,
	0x6d, 0x43, 0x6d, 0x64, 0x52, 0x73, 0x70, 0x42, 0x09, 0x5a, 0x07, 0x2e, 0x3b, 0x67, 0x6d, 0x52,
	0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_gmrpc_gmrpc_proto_rawDescOnce sync.Once
	file_gmrpc_gmrpc_proto_rawDescData = file_gmrpc_gmrpc_proto_rawDesc
)

func file_gmrpc_gmrpc_proto_rawDescGZIP() []byte {
	file_gmrpc_gmrpc_proto_rawDescOnce.Do(func() {
		file_gmrpc_gmrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_gmrpc_gmrpc_proto_rawDescData)
	})
	return file_gmrpc_gmrpc_proto_rawDescData
}

var file_gmrpc_gmrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_gmrpc_gmrpc_proto_goTypes = []interface{}{
	(*GmCmdReq)(nil),       // 0: gmRpc.GmCmdReq
	(*GmCmdRsp)(nil),       // 1: gmRpc.GmCmdRsp
	(common.PRODUCT_ID)(0), // 2: common.PRODUCT_ID
	(common.GM_CMD)(0),     // 3: common.GM_CMD
	(*common.Result)(nil),  // 4: common.Result
}
var file_gmrpc_gmrpc_proto_depIdxs = []int32{
	2, // 0: gmRpc.GmCmdReq.pid:type_name -> common.PRODUCT_ID
	3, // 1: gmRpc.GmCmdReq.cmd:type_name -> common.GM_CMD
	4, // 2: gmRpc.GmCmdRsp.ret:type_name -> common.Result
	0, // 3: gmRpc.GmService.Cmd:input_type -> gmRpc.GmCmdReq
	0, // 4: gmRpc.GmService.SetTestTime:input_type -> gmRpc.GmCmdReq
	1, // 5: gmRpc.GmService.Cmd:output_type -> gmRpc.GmCmdRsp
	1, // 6: gmRpc.GmService.SetTestTime:output_type -> gmRpc.GmCmdRsp
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_gmrpc_gmrpc_proto_init() }
func file_gmrpc_gmrpc_proto_init() {
	if File_gmrpc_gmrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_gmrpc_gmrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gmrpc_gmrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gmrpc_gmrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_gmrpc_gmrpc_proto_goTypes,
		DependencyIndexes: file_gmrpc_gmrpc_proto_depIdxs,
		MessageInfos:      file_gmrpc_gmrpc_proto_msgTypes,
	}.Build()
	File_gmrpc_gmrpc_proto = out.File
	file_gmrpc_gmrpc_proto_rawDesc = nil
	file_gmrpc_gmrpc_proto_goTypes = nil
	file_gmrpc_gmrpc_proto_depIdxs = nil
}
