syntax = "proto3";

package gmRpc;
option go_package = ".;gmRpc";

import "errors.proto";
import "enum.proto";

// Rpc服务
service GmService {
    // Gm 请求处理
    rpc Cmd (GmCmdReq) returns (GmCmdRsp);
    rpc SetTestTime (GmCmdReq) returns (GmCmdRsp);
}

// Gm内部请求
message GmCmdReq {
    common.PRODUCT_ID pid  = 1;
    string            oid  = 2;
    string            data = 3;  // 请求数据 json打包
    common.GM_CMD     cmd  = 4;  // 操作命令字
}

message GmCmdRsp {
    common.Result ret  = 1;
    string        data = 2;  // 回复数据打包 JSON 
}

