// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: hallrpc/hallrpc.proto

package hallRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OptPlayerItemReq 操作道具请求(+-)
type OptPlayerItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId      uint64                  `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                                           // 玩家id
	ItemList      []*common.ItemBase      `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`                                            // 道具列表
	ItemOperation common.ITEM_OPERATION   `protobuf:"varint,3,opt,name=item_operation,json=itemOperation,proto3,enum=common.ITEM_OPERATION" json:"item_operation,omitempty"` // 物品操作类型
	ItemSource    common.ITEM_SOURCE_TYPE `protobuf:"varint,4,opt,name=item_source,json=itemSource,proto3,enum=common.ITEM_SOURCE_TYPE" json:"item_source,omitempty"`        // 物品来源
	IsUnpack      bool                    `protobuf:"varint,5,opt,name=is_unpack,json=isUnpack,proto3" json:"is_unpack,omitempty"`                                           // 是否拆包(针对礼包)
}

func (x *OptPlayerItemReq) Reset() {
	*x = OptPlayerItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptPlayerItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptPlayerItemReq) ProtoMessage() {}

func (x *OptPlayerItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptPlayerItemReq.ProtoReflect.Descriptor instead.
func (*OptPlayerItemReq) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{0}
}

func (x *OptPlayerItemReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *OptPlayerItemReq) GetItemList() []*common.ItemBase {
	if x != nil {
		return x.ItemList
	}
	return nil
}

func (x *OptPlayerItemReq) GetItemOperation() common.ITEM_OPERATION {
	if x != nil {
		return x.ItemOperation
	}
	return common.ITEM_OPERATION(0)
}

func (x *OptPlayerItemReq) GetItemSource() common.ITEM_SOURCE_TYPE {
	if x != nil {
		return x.ItemSource
	}
	return common.ITEM_SOURCE_TYPE(0)
}

func (x *OptPlayerItemReq) GetIsUnpack() bool {
	if x != nil {
		return x.IsUnpack
	}
	return false
}

// OptPlayerItemReq 操作道具响应(+-)
type OptPlayerItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                 // 返回结果
	RewardInfo *common.Reward `protobuf:"bytes,2,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info,omitempty"` // 奖励信息
}

func (x *OptPlayerItemRsp) Reset() {
	*x = OptPlayerItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptPlayerItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptPlayerItemRsp) ProtoMessage() {}

func (x *OptPlayerItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptPlayerItemRsp.ProtoReflect.Descriptor instead.
func (*OptPlayerItemRsp) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{1}
}

func (x *OptPlayerItemRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *OptPlayerItemRsp) GetRewardInfo() *common.Reward {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

// 玩家使用道具 (透传到房间)
type UsePlayerItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId      uint64                `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                                           // 玩家id
	ItemList      []*common.ItemBase    `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`                                            // 道具列表
	ItemOperation common.ITEM_OPERATION `protobuf:"varint,3,opt,name=item_operation,json=itemOperation,proto3,enum=common.ITEM_OPERATION" json:"item_operation,omitempty"` // 物品操作类型
}

func (x *UsePlayerItemReq) Reset() {
	*x = UsePlayerItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsePlayerItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsePlayerItemReq) ProtoMessage() {}

func (x *UsePlayerItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsePlayerItemReq.ProtoReflect.Descriptor instead.
func (*UsePlayerItemReq) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{2}
}

func (x *UsePlayerItemReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *UsePlayerItemReq) GetItemList() []*common.ItemBase {
	if x != nil {
		return x.ItemList
	}
	return nil
}

func (x *UsePlayerItemReq) GetItemOperation() common.ITEM_OPERATION {
	if x != nil {
		return x.ItemOperation
	}
	return common.ITEM_OPERATION(0)
}

// 玩家使用道具返回
type UsePlayerItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *UsePlayerItemRsp) Reset() {
	*x = UsePlayerItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsePlayerItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsePlayerItemRsp) ProtoMessage() {}

func (x *UsePlayerItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsePlayerItemRsp.ProtoReflect.Descriptor instead.
func (*UsePlayerItemRsp) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{3}
}

func (x *UsePlayerItemRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 查询玩家基础信息
type QueryPlayerBaseInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
}

func (x *QueryPlayerBaseInfoReq) Reset() {
	*x = QueryPlayerBaseInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPlayerBaseInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPlayerBaseInfoReq) ProtoMessage() {}

func (x *QueryPlayerBaseInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPlayerBaseInfoReq.ProtoReflect.Descriptor instead.
func (*QueryPlayerBaseInfoReq) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{4}
}

func (x *QueryPlayerBaseInfoReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 查询玩家基础信息返回
type QueryPlayerBaseInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerInfo *common.PlayerBaseInfo `protobuf:"bytes,1,opt,name=player_info,json=playerInfo,proto3" json:"player_info,omitempty"` // 玩家基础信息
}

func (x *QueryPlayerBaseInfoRsp) Reset() {
	*x = QueryPlayerBaseInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPlayerBaseInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPlayerBaseInfoRsp) ProtoMessage() {}

func (x *QueryPlayerBaseInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPlayerBaseInfoRsp.ProtoReflect.Descriptor instead.
func (*QueryPlayerBaseInfoRsp) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{5}
}

func (x *QueryPlayerBaseInfoRsp) GetPlayerInfo() *common.PlayerBaseInfo {
	if x != nil {
		return x.PlayerInfo
	}
	return nil
}

// gm加载干架规则请求
type GmLoadRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32 `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` // 产品id
}

func (x *GmLoadRuleReq) Reset() {
	*x = GmLoadRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmLoadRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmLoadRuleReq) ProtoMessage() {}

func (x *GmLoadRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmLoadRuleReq.ProtoReflect.Descriptor instead.
func (*GmLoadRuleReq) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{6}
}

func (x *GmLoadRuleReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

// gm加载干架规则返回
type GmLoadRuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *GmLoadRuleRsp) Reset() {
	*x = GmLoadRuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmLoadRuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmLoadRuleRsp) ProtoMessage() {}

func (x *GmLoadRuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmLoadRuleRsp.ProtoReflect.Descriptor instead.
func (*GmLoadRuleRsp) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{7}
}

func (x *GmLoadRuleRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 查询玩家钓组信息
type QueryPlayerRodInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
	Id       int32  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                             // 钓组id
}

func (x *QueryPlayerRodInfoReq) Reset() {
	*x = QueryPlayerRodInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPlayerRodInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPlayerRodInfoReq) ProtoMessage() {}

func (x *QueryPlayerRodInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPlayerRodInfoReq.ProtoReflect.Descriptor instead.
func (*QueryPlayerRodInfoReq) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{8}
}

func (x *QueryPlayerRodInfoReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *QueryPlayerRodInfoReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 查询玩家钓组信息
type QueryPlayerRodInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                        // 返回结果
	RodInfo *common.RodBagInfo `protobuf:"bytes,2,opt,name=rod_info,json=rodInfo,proto3" json:"rod_info,omitempty"` // 钓组信息
}

func (x *QueryPlayerRodInfoRsp) Reset() {
	*x = QueryPlayerRodInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPlayerRodInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPlayerRodInfoRsp) ProtoMessage() {}

func (x *QueryPlayerRodInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPlayerRodInfoRsp.ProtoReflect.Descriptor instead.
func (*QueryPlayerRodInfoRsp) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{9}
}

func (x *QueryPlayerRodInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *QueryPlayerRodInfoRsp) GetRodInfo() *common.RodBagInfo {
	if x != nil {
		return x.RodInfo
	}
	return nil
}

// 查询当前节假日类型
type QueryHolidayTypeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QueryHolidayTypeReq) Reset() {
	*x = QueryHolidayTypeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryHolidayTypeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryHolidayTypeReq) ProtoMessage() {}

func (x *QueryHolidayTypeReq) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryHolidayTypeReq.ProtoReflect.Descriptor instead.
func (*QueryHolidayTypeReq) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{10}
}

// 查询当前节假日类型返回
type QueryHolidayTypeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result      `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                             // 返回结果
	Type common.HOLIDAY_TYPE `protobuf:"varint,2,opt,name=type,proto3,enum=common.HOLIDAY_TYPE" json:"type,omitempty"` // 节假日类型
}

func (x *QueryHolidayTypeRsp) Reset() {
	*x = QueryHolidayTypeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryHolidayTypeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryHolidayTypeRsp) ProtoMessage() {}

func (x *QueryHolidayTypeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryHolidayTypeRsp.ProtoReflect.Descriptor instead.
func (*QueryHolidayTypeRsp) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{11}
}

func (x *QueryHolidayTypeRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *QueryHolidayTypeRsp) GetType() common.HOLIDAY_TYPE {
	if x != nil {
		return x.Type
	}
	return common.HOLIDAY_TYPE(0)
}

// 磨损钓竿耐久
type LossRodDurabilityReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64          `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                                                                      // 玩家id
	RodId    int32           `protobuf:"varint,2,opt,name=rod_id,json=rodId,proto3" json:"rod_id,omitempty"`                                                                               // 钓竿id
	Change   map[int32]int64 `protobuf:"bytes,3,rep,name=change,proto3" json:"change,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // key = sitId，value=扣减值
}

func (x *LossRodDurabilityReq) Reset() {
	*x = LossRodDurabilityReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LossRodDurabilityReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LossRodDurabilityReq) ProtoMessage() {}

func (x *LossRodDurabilityReq) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LossRodDurabilityReq.ProtoReflect.Descriptor instead.
func (*LossRodDurabilityReq) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{12}
}

func (x *LossRodDurabilityReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *LossRodDurabilityReq) GetRodId() int32 {
	if x != nil {
		return x.RodId
	}
	return 0
}

func (x *LossRodDurabilityReq) GetChange() map[int32]int64 {
	if x != nil {
		return x.Change
	}
	return nil
}

type LossRodDurabilityRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *LossRodDurabilityRsp) Reset() {
	*x = LossRodDurabilityRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LossRodDurabilityRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LossRodDurabilityRsp) ProtoMessage() {}

func (x *LossRodDurabilityRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LossRodDurabilityRsp.ProtoReflect.Descriptor instead.
func (*LossRodDurabilityRsp) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{13}
}

func (x *LossRodDurabilityRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 扣除鱼饵耐久
type LossItemHeapReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId   uint64                  `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`
	ItemId     int64                   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`                                          // 道具id
	Durability int32                   `protobuf:"varint,3,opt,name=durability,proto3" json:"durability,omitempty"`                                                // 耐久度
	SourceType common.ITEM_SOURCE_TYPE `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3,enum=common.ITEM_SOURCE_TYPE" json:"source_type,omitempty"` // 来源类型
}

func (x *LossItemHeapReq) Reset() {
	*x = LossItemHeapReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LossItemHeapReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LossItemHeapReq) ProtoMessage() {}

func (x *LossItemHeapReq) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LossItemHeapReq.ProtoReflect.Descriptor instead.
func (*LossItemHeapReq) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{14}
}

func (x *LossItemHeapReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *LossItemHeapReq) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *LossItemHeapReq) GetDurability() int32 {
	if x != nil {
		return x.Durability
	}
	return 0
}

func (x *LossItemHeapReq) GetSourceType() common.ITEM_SOURCE_TYPE {
	if x != nil {
		return x.SourceType
	}
	return common.ITEM_SOURCE_TYPE(0)
}

type LossItemHeapRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *LossItemHeapRsp) Reset() {
	*x = LossItemHeapRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LossItemHeapRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LossItemHeapRsp) ProtoMessage() {}

func (x *LossItemHeapRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LossItemHeapRsp.ProtoReflect.Descriptor instead.
func (*LossItemHeapRsp) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{15}
}

func (x *LossItemHeapRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 设置玩家红点状态
type SetPlayerRedDotReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId      uint64                  `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                                    // 玩家id
	ModuleType    common.USER_MODULE_TYPE `protobuf:"varint,2,opt,name=module_type,json=moduleType,proto3,enum=common.USER_MODULE_TYPE" json:"module_type,omitempty"` // 主模块类型
	SubModuleType []int32                 `protobuf:"varint,3,rep,packed,name=sub_module_type,json=subModuleType,proto3" json:"sub_module_type,omitempty"`            // 子模块类型，每个模块有不同的常量定义
	HasRedDot     bool                    `protobuf:"varint,4,opt,name=has_red_dot,json=hasRedDot,proto3" json:"has_red_dot,omitempty"`                               // 是否有红点
}

func (x *SetPlayerRedDotReq) Reset() {
	*x = SetPlayerRedDotReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPlayerRedDotReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPlayerRedDotReq) ProtoMessage() {}

func (x *SetPlayerRedDotReq) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPlayerRedDotReq.ProtoReflect.Descriptor instead.
func (*SetPlayerRedDotReq) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{16}
}

func (x *SetPlayerRedDotReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *SetPlayerRedDotReq) GetModuleType() common.USER_MODULE_TYPE {
	if x != nil {
		return x.ModuleType
	}
	return common.USER_MODULE_TYPE(0)
}

func (x *SetPlayerRedDotReq) GetSubModuleType() []int32 {
	if x != nil {
		return x.SubModuleType
	}
	return nil
}

func (x *SetPlayerRedDotReq) GetHasRedDot() bool {
	if x != nil {
		return x.HasRedDot
	}
	return false
}

// 设置玩家红点状态
type SetPlayerRedDotRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *SetPlayerRedDotRes) Reset() {
	*x = SetPlayerRedDotRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hallrpc_hallrpc_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPlayerRedDotRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPlayerRedDotRes) ProtoMessage() {}

func (x *SetPlayerRedDotRes) ProtoReflect() protoreflect.Message {
	mi := &file_hallrpc_hallrpc_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPlayerRedDotRes.ProtoReflect.Descriptor instead.
func (*SetPlayerRedDotRes) Descriptor() ([]byte, []int) {
	return file_hallrpc_hallrpc_proto_rawDescGZIP(), []int{17}
}

func (x *SetPlayerRedDotRes) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

var File_hallrpc_hallrpc_proto protoreflect.FileDescriptor

var file_hallrpc_hallrpc_proto_rawDesc = []byte{
	0x0a, 0x15, 0x68, 0x61, 0x6c, 0x6c, 0x72, 0x70, 0x63, 0x2f, 0x68, 0x61, 0x6c, 0x6c, 0x72, 0x70,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63,
	0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf5, 0x01, 0x0a, 0x10, 0x4f, 0x70, 0x74,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x08, 0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x0e, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f,
	0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x52, 0x0d, 0x69, 0x74, 0x65, 0x6d, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0b, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0a, 0x69, 0x74, 0x65, 0x6d, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b,
	0x22, 0x65, 0x0a, 0x10, 0x4f, 0x70, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x9d, 0x01, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x52, 0x08,
	0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x0e, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x52, 0x0d, 0x69, 0x74, 0x65, 0x6d, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x34, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x22, 0x35, 0x0a,
	0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x51, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x37,
	0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2e, 0x0a, 0x0d, 0x47, 0x6d, 0x4c, 0x6f, 0x61,
	0x64, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x0d, 0x47, 0x6d, 0x4c, 0x6f, 0x61,
	0x64, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x22, 0x44, 0x0a, 0x15, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x68, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52,
	0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x72,
	0x6f, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42, 0x61, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x72, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x15, 0x0a, 0x13, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x22, 0x61, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x48, 0x4f, 0x4c, 0x49, 0x44, 0x41, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0xc8, 0x01, 0x0a, 0x14, 0x4c, 0x6f, 0x73, 0x73, 0x52, 0x6f, 0x64,
	0x44, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x6f, 0x64, 0x49,
	0x64, 0x12, 0x41, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x4c, 0x6f, 0x73, 0x73,
	0x52, 0x6f, 0x64, 0x44, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x38, 0x0a, 0x14, 0x4c, 0x6f, 0x73, 0x73, 0x52, 0x6f, 0x64, 0x44, 0x75, 0x72, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x22, 0xa2, 0x01, 0x0a, 0x0f, 0x4c, 0x6f,
	0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65,
	0x6d, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x12, 0x39, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x33,
	0x0a, 0x0f, 0x4c, 0x6f, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03,
	0x72, 0x65, 0x74, 0x22, 0xb4, 0x01, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x75, 0x62,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x68, 0x61,
	0x73, 0x5f, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x68, 0x61, 0x73, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x22, 0x36, 0x0a, 0x12, 0x53, 0x65,
	0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x52, 0x65, 0x73,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x32, 0xf5, 0x04, 0x0a, 0x0b, 0x48, 0x61, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x45, 0x0a, 0x0d, 0x4f, 0x70, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x19, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x4f, 0x70,
	0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x19,
	0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x4f, 0x70, 0x74, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x57, 0x0a, 0x13, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1f, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x1a, 0x1f, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0a, 0x47, 0x6d, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x16, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x6d, 0x4c, 0x6f, 0x61,
	0x64, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52,
	0x70, 0x63, 0x2e, 0x47, 0x6d, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x54, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52,
	0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x6f, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x6f, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48,
	0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x2e, 0x68, 0x61, 0x6c,
	0x6c, 0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52,
	0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x11, 0x4c, 0x6f, 0x73, 0x73, 0x52, 0x6f,
	0x64, 0x44, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x2e, 0x68, 0x61,
	0x6c, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x4c, 0x6f, 0x73, 0x73, 0x52, 0x6f, 0x64, 0x44, 0x75, 0x72,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x68, 0x61, 0x6c,
	0x6c, 0x52, 0x70, 0x63, 0x2e, 0x4c, 0x6f, 0x73, 0x73, 0x52, 0x6f, 0x64, 0x44, 0x75, 0x72, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x73, 0x70, 0x12, 0x42, 0x0a, 0x0c, 0x4c, 0x6f, 0x73,
	0x73, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x12, 0x18, 0x2e, 0x68, 0x61, 0x6c, 0x6c,
	0x52, 0x70, 0x63, 0x2e, 0x4c, 0x6f, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x4c, 0x6f,
	0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x52, 0x73, 0x70, 0x12, 0x4b, 0x0a,
	0x0f, 0x53, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74,
	0x12, 0x1b, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x53, 0x65, 0x74, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e,
	0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x53, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x42, 0x0b, 0x5a, 0x09, 0x2e, 0x3b,
	0x68, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hallrpc_hallrpc_proto_rawDescOnce sync.Once
	file_hallrpc_hallrpc_proto_rawDescData = file_hallrpc_hallrpc_proto_rawDesc
)

func file_hallrpc_hallrpc_proto_rawDescGZIP() []byte {
	file_hallrpc_hallrpc_proto_rawDescOnce.Do(func() {
		file_hallrpc_hallrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_hallrpc_hallrpc_proto_rawDescData)
	})
	return file_hallrpc_hallrpc_proto_rawDescData
}

var file_hallrpc_hallrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_hallrpc_hallrpc_proto_goTypes = []interface{}{
	(*OptPlayerItemReq)(nil),       // 0: hallRpc.OptPlayerItemReq
	(*OptPlayerItemRsp)(nil),       // 1: hallRpc.OptPlayerItemRsp
	(*UsePlayerItemReq)(nil),       // 2: hallRpc.UsePlayerItemReq
	(*UsePlayerItemRsp)(nil),       // 3: hallRpc.UsePlayerItemRsp
	(*QueryPlayerBaseInfoReq)(nil), // 4: hallRpc.QueryPlayerBaseInfoReq
	(*QueryPlayerBaseInfoRsp)(nil), // 5: hallRpc.QueryPlayerBaseInfoRsp
	(*GmLoadRuleReq)(nil),          // 6: hallRpc.GmLoadRuleReq
	(*GmLoadRuleRsp)(nil),          // 7: hallRpc.GmLoadRuleRsp
	(*QueryPlayerRodInfoReq)(nil),  // 8: hallRpc.QueryPlayerRodInfoReq
	(*QueryPlayerRodInfoRsp)(nil),  // 9: hallRpc.QueryPlayerRodInfoRsp
	(*QueryHolidayTypeReq)(nil),    // 10: hallRpc.QueryHolidayTypeReq
	(*QueryHolidayTypeRsp)(nil),    // 11: hallRpc.QueryHolidayTypeRsp
	(*LossRodDurabilityReq)(nil),   // 12: hallRpc.LossRodDurabilityReq
	(*LossRodDurabilityRsp)(nil),   // 13: hallRpc.LossRodDurabilityRsp
	(*LossItemHeapReq)(nil),        // 14: hallRpc.LossItemHeapReq
	(*LossItemHeapRsp)(nil),        // 15: hallRpc.LossItemHeapRsp
	(*SetPlayerRedDotReq)(nil),     // 16: hallRpc.SetPlayerRedDotReq
	(*SetPlayerRedDotRes)(nil),     // 17: hallRpc.SetPlayerRedDotRes
	nil,                            // 18: hallRpc.LossRodDurabilityReq.ChangeEntry
	(*common.ItemBase)(nil),        // 19: common.ItemBase
	(common.ITEM_OPERATION)(0),     // 20: common.ITEM_OPERATION
	(common.ITEM_SOURCE_TYPE)(0),   // 21: common.ITEM_SOURCE_TYPE
	(*common.Result)(nil),          // 22: common.Result
	(*common.Reward)(nil),          // 23: common.Reward
	(*common.PlayerBaseInfo)(nil),  // 24: common.PlayerBaseInfo
	(*common.RodBagInfo)(nil),      // 25: common.RodBagInfo
	(common.HOLIDAY_TYPE)(0),       // 26: common.HOLIDAY_TYPE
	(common.USER_MODULE_TYPE)(0),   // 27: common.USER_MODULE_TYPE
}
var file_hallrpc_hallrpc_proto_depIdxs = []int32{
	19, // 0: hallRpc.OptPlayerItemReq.item_list:type_name -> common.ItemBase
	20, // 1: hallRpc.OptPlayerItemReq.item_operation:type_name -> common.ITEM_OPERATION
	21, // 2: hallRpc.OptPlayerItemReq.item_source:type_name -> common.ITEM_SOURCE_TYPE
	22, // 3: hallRpc.OptPlayerItemRsp.ret:type_name -> common.Result
	23, // 4: hallRpc.OptPlayerItemRsp.reward_info:type_name -> common.Reward
	19, // 5: hallRpc.UsePlayerItemReq.item_list:type_name -> common.ItemBase
	20, // 6: hallRpc.UsePlayerItemReq.item_operation:type_name -> common.ITEM_OPERATION
	22, // 7: hallRpc.UsePlayerItemRsp.ret:type_name -> common.Result
	24, // 8: hallRpc.QueryPlayerBaseInfoRsp.player_info:type_name -> common.PlayerBaseInfo
	22, // 9: hallRpc.GmLoadRuleRsp.ret:type_name -> common.Result
	22, // 10: hallRpc.QueryPlayerRodInfoRsp.ret:type_name -> common.Result
	25, // 11: hallRpc.QueryPlayerRodInfoRsp.rod_info:type_name -> common.RodBagInfo
	22, // 12: hallRpc.QueryHolidayTypeRsp.ret:type_name -> common.Result
	26, // 13: hallRpc.QueryHolidayTypeRsp.type:type_name -> common.HOLIDAY_TYPE
	18, // 14: hallRpc.LossRodDurabilityReq.change:type_name -> hallRpc.LossRodDurabilityReq.ChangeEntry
	22, // 15: hallRpc.LossRodDurabilityRsp.ret:type_name -> common.Result
	21, // 16: hallRpc.LossItemHeapReq.source_type:type_name -> common.ITEM_SOURCE_TYPE
	22, // 17: hallRpc.LossItemHeapRsp.ret:type_name -> common.Result
	27, // 18: hallRpc.SetPlayerRedDotReq.module_type:type_name -> common.USER_MODULE_TYPE
	22, // 19: hallRpc.SetPlayerRedDotRes.ret:type_name -> common.Result
	0,  // 20: hallRpc.HallService.OptPlayerItem:input_type -> hallRpc.OptPlayerItemReq
	4,  // 21: hallRpc.HallService.QueryPlayerBaseInfo:input_type -> hallRpc.QueryPlayerBaseInfoReq
	6,  // 22: hallRpc.HallService.GmLoadRule:input_type -> hallRpc.GmLoadRuleReq
	8,  // 23: hallRpc.HallService.QueryPlayerRodInfo:input_type -> hallRpc.QueryPlayerRodInfoReq
	10, // 24: hallRpc.HallService.QueryHolidayType:input_type -> hallRpc.QueryHolidayTypeReq
	12, // 25: hallRpc.HallService.LossRodDurability:input_type -> hallRpc.LossRodDurabilityReq
	14, // 26: hallRpc.HallService.LossItemHeap:input_type -> hallRpc.LossItemHeapReq
	16, // 27: hallRpc.HallService.SetPlayerRedDot:input_type -> hallRpc.SetPlayerRedDotReq
	1,  // 28: hallRpc.HallService.OptPlayerItem:output_type -> hallRpc.OptPlayerItemRsp
	5,  // 29: hallRpc.HallService.QueryPlayerBaseInfo:output_type -> hallRpc.QueryPlayerBaseInfoRsp
	7,  // 30: hallRpc.HallService.GmLoadRule:output_type -> hallRpc.GmLoadRuleRsp
	9,  // 31: hallRpc.HallService.QueryPlayerRodInfo:output_type -> hallRpc.QueryPlayerRodInfoRsp
	11, // 32: hallRpc.HallService.QueryHolidayType:output_type -> hallRpc.QueryHolidayTypeRsp
	13, // 33: hallRpc.HallService.LossRodDurability:output_type -> hallRpc.LossRodDurabilityRsp
	15, // 34: hallRpc.HallService.LossItemHeap:output_type -> hallRpc.LossItemHeapRsp
	17, // 35: hallRpc.HallService.SetPlayerRedDot:output_type -> hallRpc.SetPlayerRedDotRes
	28, // [28:36] is the sub-list for method output_type
	20, // [20:28] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_hallrpc_hallrpc_proto_init() }
func file_hallrpc_hallrpc_proto_init() {
	if File_hallrpc_hallrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_hallrpc_hallrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptPlayerItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptPlayerItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsePlayerItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsePlayerItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPlayerBaseInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPlayerBaseInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmLoadRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmLoadRuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPlayerRodInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPlayerRodInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryHolidayTypeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryHolidayTypeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LossRodDurabilityReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LossRodDurabilityRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LossItemHeapReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LossItemHeapRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPlayerRedDotReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hallrpc_hallrpc_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPlayerRedDotRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hallrpc_hallrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hallrpc_hallrpc_proto_goTypes,
		DependencyIndexes: file_hallrpc_hallrpc_proto_depIdxs,
		MessageInfos:      file_hallrpc_hallrpc_proto_msgTypes,
	}.Build()
	File_hallrpc_hallrpc_proto = out.File
	file_hallrpc_hallrpc_proto_rawDesc = nil
	file_hallrpc_hallrpc_proto_goTypes = nil
	file_hallrpc_hallrpc_proto_depIdxs = nil
}
