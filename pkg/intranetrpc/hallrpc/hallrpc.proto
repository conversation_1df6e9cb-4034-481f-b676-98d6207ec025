syntax = "proto3";

package hallRpc;
option go_package = ".;hallRpc";

import "common.proto";
import "enum.proto";
import "errors.proto";

// OptPlayerItemReq 操作道具请求(+-)
message OptPlayerItemReq {
    uint64 player_id                        = 1; // 玩家id
    repeated common.ItemBase item_list      = 2; // 道具列表
    common.ITEM_OPERATION    item_operation = 3; // 物品操作类型
    common.ITEM_SOURCE_TYPE  item_source    = 4; // 物品来源
    bool                     is_unpack      = 5; // 是否拆包(针对礼包)
}

// OptPlayerItemReq 操作道具响应(+-)
message OptPlayerItemRsp {
    common.Result ret                      = 1; // 返回结果
    common.Reward reward_info              = 2; // 奖励信息
}

// 玩家使用道具 (透传到房间)
message UsePlayerItemReq {
    uint64 player_id                        = 1; // 玩家id
    repeated common.ItemBase item_list      = 2; // 道具列表
    common.ITEM_OPERATION    item_operation = 3; // 物品操作类型
}

// 玩家使用道具返回
message UsePlayerItemRsp {
    common.Result ret                      = 1; // 返回结果
}

// 查询玩家基础信息
message QueryPlayerBaseInfoReq {
    uint64 player_id = 1; // 玩家id
}

// 查询玩家基础信息返回
message QueryPlayerBaseInfoRsp {
    common.PlayerBaseInfo player_info = 1; // 玩家基础信息
}

// gm加载干架规则请求
message GmLoadRuleReq {
    int32 product_id = 1; // 产品id
}

// gm加载干架规则返回
message GmLoadRuleRsp {
    common.Result ret = 1; // 返回结果
}

// 查询玩家钓组信息
message QueryPlayerRodInfoReq {
    uint64 player_id = 1;  // 玩家id
    int32  id        = 2;  // 钓组id
}

// 查询玩家钓组信息
message QueryPlayerRodInfoRsp {
    common.Result     ret      = 1;  // 返回结果
    common.RodBagInfo rod_info = 2;  // 钓组信息
}

// 查询当前节假日类型
message QueryHolidayTypeReq {
}

// 查询当前节假日类型返回
message QueryHolidayTypeRsp {
    common.Result       ret = 1; // 返回结果
    common.HOLIDAY_TYPE type= 2; // 节假日类型
}

// 磨损钓竿耐久
message LossRodDurabilityReq {
    uint64 player_id = 1; // 玩家id
    int32 rod_id = 2; // 钓竿id
    map<int32, int64> change = 3; // key = sitId，value=扣减值
}

message LossRodDurabilityRsp {
    common.Result ret = 1; // 返回结果
}

// 扣除鱼饵耐久
message LossItemHeapReq {
    uint64                  player_id   = 1;
    int64                   item_id     = 2;  // 道具id
    int32                   durability  = 3;  // 耐久度
    common.ITEM_SOURCE_TYPE source_type = 4;  // 来源类型
}

message LossItemHeapRsp {
    common.Result ret = 1; // 返回结果
}

// 设置玩家红点状态
message SetPlayerRedDotReq {
    uint64                  player_id                  = 1; // 玩家id
    common.USER_MODULE_TYPE module_type                = 2;  // 主模块类型
    repeated     int32      sub_module_type            = 3;  // 子模块类型，每个模块有不同的常量定义
    bool                    has_red_dot                = 4;  // 是否有红点
}

// 设置玩家红点状态
message SetPlayerRedDotRes {
    common.Result ret = 1; // 返回结果
}

// RPC服务
service HallService {
    // 操作道具请求
    rpc OptPlayerItem(OptPlayerItemReq) returns (OptPlayerItemRsp);
    // 查询玩家基础信息
    rpc QueryPlayerBaseInfo(QueryPlayerBaseInfoReq) returns (QueryPlayerBaseInfoRsp);
    // 加载竿架规则
    rpc GmLoadRule(GmLoadRuleReq) returns (GmLoadRuleRsp);
    // 查询玩家钓组信息
    rpc QueryPlayerRodInfo(QueryPlayerRodInfoReq) returns (QueryPlayerRodInfoRsp);
    // 查询节假日类型
    rpc QueryHolidayType(QueryHolidayTypeReq) returns (QueryHolidayTypeRsp);
    // 磨损钓竿耐久
    rpc LossRodDurability(LossRodDurabilityReq) returns (LossRodDurabilityRsp);
    // 扣除鱼饵耐久
    rpc LossItemHeap(LossItemHeapReq) returns (LossItemHeapRsp);

    // 设置玩家红点状态
    rpc SetPlayerRedDot(SetPlayerRedDotReq) returns (SetPlayerRedDotRes);
}
