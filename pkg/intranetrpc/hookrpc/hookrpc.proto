syntax = "proto3";

package hookRpc;
option go_package = ".;hookRpc";

import "common.proto";
import "enum.proto";
import "errors.proto";

// 抛竿请求
message ThrowRodRouteReq {
    uint64 player_id                 = 1; // 玩家id
    int64 pond_id                    = 2; // 钓场id
    int32 rig_id                     = 3; // 钓组id
    common.HookBait hook_bait        = 4; // 钩饵组合
    common.ThrowGridInfo grid_info   = 5; // 抛鱼格信息
    common.HookHabitParam hook_habit = 6; // 中鱼习性参数
}

// 抛竿响应
message ThrowRodRouteRsp {
    common.Result ret                   = 1; // 返回结果
    common.FishSyncControl sync_control = 2; // 同步控制
}

// 中鱼请求
message FishHookRouteReq {
    uint64 player_id                 = 1; // 玩家id
    int64  pond_id                   = 2; // 钓场id
    int32  rig_id                    = 3; // 钓组id
    common.ThrowGridInfo grid_info   = 4; // 抛鱼格信息
    common.HookHabitParam hook_habit = 5; // 中鱼习性参数
}

// 中鱼响应
message FishHookRouteRsp {
    common.Result   ret           = 1;  // 返回结果
    int64           next_req_time = 2;  // 下一次请求时间(毫秒)
    int64           fake_fish_id  = 3;  // 假咬口鱼id
    common.FishInfo fish_info     = 4;  // 中鱼的鱼信息
}

// 收竿请求
message CatchRodRouteReq {
    uint64                player_id  = 1;  // 玩家id
    int64                 pond_id    = 2;  // 池塘id
    int32                 rig_id     = 3;  // 钓组id
    common.HookHabitParam hook_habit = 4;  // 中鱼习性参数
}

// 收竿响应
message CatchRodRouteRsp {
    common.Result ret              = 1; // 返回结果
    common.FISH_RESULT fish_result = 2; // 捕获结果
    common.FishInfo fish_info      = 3; // 捕获的鱼信息
    common.HookBait hook_bait      = 4; // 钩饵组合
}

// 搏鱼请求
message FishBattleRouteReq {
    uint64 player_id               = 1; // 玩家id
    int32  rig_id                  = 2; // 钓组id
    common.FISH_RESULT fish_result = 3; // 鱼状态
}

// 搏鱼响应
message FishBattleRouteRsp {
    common.Result ret              = 1; // 返回结果
    common.FishInfo fish_info      = 2; // 捕获的鱼信息
}

// 开始中鱼请求
message HookStartRouteReq {
    uint64                     player_id  = 1;  // 玩家id
    common.HOOK_FISH_CALC_TYPE calc_type  = 2;  // 中鱼计算类型
    int64                      pond_id    = 3;  // 钓场id
    int32                      rig_id     = 4;  // 钓组id
    common.HookBait            hook_bait  = 5;  // 钩饵组合
    common.HookHabitParam      hook_habit = 6;  // 中鱼习性参数
}

// 开始中鱼响应
message HookStartRouteRsp {
    common.Result          ret          = 1; // 结果
    common.FishSyncControl sync_control = 2; // 请求同步控制
}

// RPC服务
service HookService {
    // 抛竿请求
    rpc GetThrowRodReq(ThrowRodRouteReq) returns (ThrowRodRouteRsp) {}
    // 中鱼请求
    rpc GetFishHookReq(FishHookRouteReq) returns (FishHookRouteRsp) {}
    // 收杆请求
    rpc GetCatchRodReq(CatchRodRouteReq) returns (CatchRodRouteRsp) {}
    // 搏鱼请求
    rpc GetFishBattleReq(FishBattleRouteReq) returns (FishBattleRouteRsp) {}
    // 开始中鱼请求
    rpc GetHookStartReq(HookStartRouteReq) returns (HookStartRouteRsp) {}
}