// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: loginrpc/loginrpc.proto

package loginRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 登录请求
type LoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoginType     common.LOGIN_TYPE   `protobuf:"varint,1,opt,name=login_type,json=loginType,proto3,enum=common.LOGIN_TYPE" json:"login_type,omitempty"`   // 登录方式
	ClientVersion string              `protobuf:"bytes,2,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`               // 客户端版本号
	ProductId     int32               `protobuf:"varint,3,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`                          // 产品 ID
	ChannelId     common.CHANNEL_TYPE `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3,enum=common.CHANNEL_TYPE" json:"channel_id,omitempty"` // 渠道 ID
	DeviceInfo    *common.DeviceInfo  `protobuf:"bytes,5,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`                        // 设备信息
	ThirdToken    string              `protobuf:"bytes,6,opt,name=third_token,json=thirdToken,proto3" json:"third_token,omitempty"`                        // 三方Token，根据具体SDK
	// string                adjust_id         = 7;  // adjust id (废弃 device info 重复)
	Network    common.NETWORK_TYPE `protobuf:"varint,8,opt,name=network,proto3,enum=common.NETWORK_TYPE" json:"network,omitempty"` // 网络类型
	BundleName string              `protobuf:"bytes,9,opt,name=bundle_name,json=bundleName,proto3" json:"bundle_name,omitempty"`   // 包名
	// string                ip                = 10; // IP
	Platform    common.PLATFORM_TYPE   `protobuf:"varint,11,opt,name=platform,proto3,enum=common.PLATFORM_TYPE" json:"platform,omitempty"` // 平台
	AccountInfo *common.AccountInfo    `protobuf:"bytes,12,opt,name=account_info,json=accountInfo,proto3" json:"account_info,omitempty"`   // 账号信息(针对账号密码登录)
	IsReg       bool                   `protobuf:"varint,13,opt,name=is_reg,json=isReg,proto3" json:"is_reg,omitempty"`                    // 是否是注册
	ThirdInfo   *common.ThirdLoginInfo `protobuf:"bytes,14,opt,name=third_info,json=thirdInfo,proto3" json:"third_info,omitempty"`         // 三方登录信息(针对三方登录)
}

func (x *LoginReq) Reset() {
	*x = LoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loginrpc_loginrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReq) ProtoMessage() {}

func (x *LoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_loginrpc_loginrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReq.ProtoReflect.Descriptor instead.
func (*LoginReq) Descriptor() ([]byte, []int) {
	return file_loginrpc_loginrpc_proto_rawDescGZIP(), []int{0}
}

func (x *LoginReq) GetLoginType() common.LOGIN_TYPE {
	if x != nil {
		return x.LoginType
	}
	return common.LOGIN_TYPE(0)
}

func (x *LoginReq) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *LoginReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *LoginReq) GetChannelId() common.CHANNEL_TYPE {
	if x != nil {
		return x.ChannelId
	}
	return common.CHANNEL_TYPE(0)
}

func (x *LoginReq) GetDeviceInfo() *common.DeviceInfo {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

func (x *LoginReq) GetThirdToken() string {
	if x != nil {
		return x.ThirdToken
	}
	return ""
}

func (x *LoginReq) GetNetwork() common.NETWORK_TYPE {
	if x != nil {
		return x.Network
	}
	return common.NETWORK_TYPE(0)
}

func (x *LoginReq) GetBundleName() string {
	if x != nil {
		return x.BundleName
	}
	return ""
}

func (x *LoginReq) GetPlatform() common.PLATFORM_TYPE {
	if x != nil {
		return x.Platform
	}
	return common.PLATFORM_TYPE(0)
}

func (x *LoginReq) GetAccountInfo() *common.AccountInfo {
	if x != nil {
		return x.AccountInfo
	}
	return nil
}

func (x *LoginReq) GetIsReg() bool {
	if x != nil {
		return x.IsReg
	}
	return false
}

func (x *LoginReq) GetThirdInfo() *common.ThirdLoginInfo {
	if x != nil {
		return x.ThirdInfo
	}
	return nil
}

type LoginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret               *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	PlayerId          uint64               `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                                // 玩家 ID
	Token             string               `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`                                                       // 服务端返回token
	IsReg             bool                 `protobuf:"varint,4,opt,name=is_reg,json=isReg,proto3" json:"is_reg,omitempty"`                                         // 是否注册
	RichUserInfo      *common.RichUserInfo `protobuf:"bytes,5,opt,name=rich_user_info,json=richUserInfo,proto3" json:"rich_user_info,omitempty"`                   // 全量用户信息
	ForceRealName     bool                 `protobuf:"varint,6,opt,name=force_real_name,json=forceRealName,proto3" json:"force_real_name,omitempty"`               // 是否需要强制实名
	IsInAntiAddiction bool                 `protobuf:"varint,7,opt,name=is_in_anti_addiction,json=isInAntiAddiction,proto3" json:"is_in_anti_addiction,omitempty"` // 是否在防沉迷内
	OriIdentifier     string               `protobuf:"bytes,8,opt,name=ori_identifier,json=oriIdentifier,proto3" json:"ori_identifier,omitempty"`                  // 唯一标识符，标识设备
	WhiteList         bool                 `protobuf:"varint,9,opt,name=white_list,json=whiteList,proto3" json:"white_list,omitempty"`                             // 白名单
	RealNameAuth      bool                 `protobuf:"varint,10,opt,name=real_name_auth,json=realNameAuth,proto3" json:"real_name_auth,omitempty"`                 // 实名状态
}

func (x *LoginRsp) Reset() {
	*x = LoginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loginrpc_loginrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRsp) ProtoMessage() {}

func (x *LoginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_loginrpc_loginrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRsp.ProtoReflect.Descriptor instead.
func (*LoginRsp) Descriptor() ([]byte, []int) {
	return file_loginrpc_loginrpc_proto_rawDescGZIP(), []int{1}
}

func (x *LoginRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *LoginRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *LoginRsp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *LoginRsp) GetIsReg() bool {
	if x != nil {
		return x.IsReg
	}
	return false
}

func (x *LoginRsp) GetRichUserInfo() *common.RichUserInfo {
	if x != nil {
		return x.RichUserInfo
	}
	return nil
}

func (x *LoginRsp) GetForceRealName() bool {
	if x != nil {
		return x.ForceRealName
	}
	return false
}

func (x *LoginRsp) GetIsInAntiAddiction() bool {
	if x != nil {
		return x.IsInAntiAddiction
	}
	return false
}

func (x *LoginRsp) GetOriIdentifier() string {
	if x != nil {
		return x.OriIdentifier
	}
	return ""
}

func (x *LoginRsp) GetWhiteList() bool {
	if x != nil {
		return x.WhiteList
	}
	return false
}

func (x *LoginRsp) GetRealNameAuth() bool {
	if x != nil {
		return x.RealNameAuth
	}
	return false
}

// 登出请求
type LogoutReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`
}

func (x *LogoutReq) Reset() {
	*x = LogoutReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loginrpc_loginrpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogoutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutReq) ProtoMessage() {}

func (x *LogoutReq) ProtoReflect() protoreflect.Message {
	mi := &file_loginrpc_loginrpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutReq.ProtoReflect.Descriptor instead.
func (*LogoutReq) Descriptor() ([]byte, []int) {
	return file_loginrpc_loginrpc_proto_rawDescGZIP(), []int{2}
}

func (x *LogoutReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 登出回应
type LogoutRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
}

func (x *LogoutRsp) Reset() {
	*x = LogoutRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loginrpc_loginrpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogoutRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutRsp) ProtoMessage() {}

func (x *LogoutRsp) ProtoReflect() protoreflect.Message {
	mi := &file_loginrpc_loginrpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutRsp.ProtoReflect.Descriptor instead.
func (*LogoutRsp) Descriptor() ([]byte, []int) {
	return file_loginrpc_loginrpc_proto_rawDescGZIP(), []int{3}
}

func (x *LogoutRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

var File_loginrpc_loginrpc_proto protoreflect.FileDescriptor

var file_loginrpc_loginrpc_proto_rawDesc = []byte{
	0x0a, 0x17, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x72, 0x70, 0x63, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e,
	0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x6c, 0x6f, 0x67, 0x69, 0x6e,
	0x52, 0x70, 0x63, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x98, 0x04, 0x0a, 0x08,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43,
	0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x09, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x68, 0x69, 0x72, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x74, 0x68, 0x69, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x12, 0x36, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x72,
	0x65, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x52, 0x65, 0x67, 0x12,
	0x35, 0x0a, 0x0a, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x68, 0x69,
	0x72, 0x64, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x68, 0x69,
	0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xf7, 0x02, 0x0a, 0x08, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x72,
	0x65, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x52, 0x65, 0x67, 0x12,
	0x3a, 0x0a, 0x0e, 0x72, 0x69, 0x63, 0x68, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x69, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x72,
	0x69, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x52, 0x65, 0x61, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x61, 0x6e, 0x74,
	0x69, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x69, 0x73, 0x49, 0x6e, 0x41, 0x6e, 0x74, 0x69, 0x41, 0x64, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x72, 0x69, 0x5f, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x72,
	0x69, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x77,
	0x68, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65,
	0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x22, 0x28, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x2d, 0x0a, 0x09, 0x4c, 0x6f,
	0x67, 0x6f, 0x75, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x32, 0x77, 0x0a, 0x0c, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x12, 0x12, 0x2e, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x70, 0x63, 0x2e, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x70,
	0x63, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x34, 0x0a, 0x06,
	0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x12, 0x13, 0x2e, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x70,
	0x63, 0x2e, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x6c, 0x6f,
	0x67, 0x69, 0x6e, 0x52, 0x70, 0x63, 0x2e, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x3b, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x70, 0x63,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_loginrpc_loginrpc_proto_rawDescOnce sync.Once
	file_loginrpc_loginrpc_proto_rawDescData = file_loginrpc_loginrpc_proto_rawDesc
)

func file_loginrpc_loginrpc_proto_rawDescGZIP() []byte {
	file_loginrpc_loginrpc_proto_rawDescOnce.Do(func() {
		file_loginrpc_loginrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_loginrpc_loginrpc_proto_rawDescData)
	})
	return file_loginrpc_loginrpc_proto_rawDescData
}

var file_loginrpc_loginrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_loginrpc_loginrpc_proto_goTypes = []interface{}{
	(*LoginReq)(nil),              // 0: loginRpc.LoginReq
	(*LoginRsp)(nil),              // 1: loginRpc.LoginRsp
	(*LogoutReq)(nil),             // 2: loginRpc.LogoutReq
	(*LogoutRsp)(nil),             // 3: loginRpc.LogoutRsp
	(common.LOGIN_TYPE)(0),        // 4: common.LOGIN_TYPE
	(common.CHANNEL_TYPE)(0),      // 5: common.CHANNEL_TYPE
	(*common.DeviceInfo)(nil),     // 6: common.DeviceInfo
	(common.NETWORK_TYPE)(0),      // 7: common.NETWORK_TYPE
	(common.PLATFORM_TYPE)(0),     // 8: common.PLATFORM_TYPE
	(*common.AccountInfo)(nil),    // 9: common.AccountInfo
	(*common.ThirdLoginInfo)(nil), // 10: common.ThirdLoginInfo
	(*common.Result)(nil),         // 11: common.Result
	(*common.RichUserInfo)(nil),   // 12: common.RichUserInfo
}
var file_loginrpc_loginrpc_proto_depIdxs = []int32{
	4,  // 0: loginRpc.LoginReq.login_type:type_name -> common.LOGIN_TYPE
	5,  // 1: loginRpc.LoginReq.channel_id:type_name -> common.CHANNEL_TYPE
	6,  // 2: loginRpc.LoginReq.device_info:type_name -> common.DeviceInfo
	7,  // 3: loginRpc.LoginReq.network:type_name -> common.NETWORK_TYPE
	8,  // 4: loginRpc.LoginReq.platform:type_name -> common.PLATFORM_TYPE
	9,  // 5: loginRpc.LoginReq.account_info:type_name -> common.AccountInfo
	10, // 6: loginRpc.LoginReq.third_info:type_name -> common.ThirdLoginInfo
	11, // 7: loginRpc.LoginRsp.ret:type_name -> common.Result
	12, // 8: loginRpc.LoginRsp.rich_user_info:type_name -> common.RichUserInfo
	11, // 9: loginRpc.LogoutRsp.ret:type_name -> common.Result
	0,  // 10: loginRpc.LoginService.Login:input_type -> loginRpc.LoginReq
	2,  // 11: loginRpc.LoginService.Logout:input_type -> loginRpc.LogoutReq
	1,  // 12: loginRpc.LoginService.Login:output_type -> loginRpc.LoginRsp
	3,  // 13: loginRpc.LoginService.Logout:output_type -> loginRpc.LogoutRsp
	12, // [12:14] is the sub-list for method output_type
	10, // [10:12] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_loginrpc_loginrpc_proto_init() }
func file_loginrpc_loginrpc_proto_init() {
	if File_loginrpc_loginrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_loginrpc_loginrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loginrpc_loginrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loginrpc_loginrpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogoutReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loginrpc_loginrpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogoutRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_loginrpc_loginrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_loginrpc_loginrpc_proto_goTypes,
		DependencyIndexes: file_loginrpc_loginrpc_proto_depIdxs,
		MessageInfos:      file_loginrpc_loginrpc_proto_msgTypes,
	}.Build()
	File_loginrpc_loginrpc_proto = out.File
	file_loginrpc_loginrpc_proto_rawDesc = nil
	file_loginrpc_loginrpc_proto_goTypes = nil
	file_loginrpc_loginrpc_proto_depIdxs = nil
}
