// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: msgrpc/msgrpc.proto

package mailRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 发送邮件
type SendMailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// From
	Sender string `protobuf:"bytes,1,opt,name=sender,proto3" json:"sender,omitempty"` // 发件人
	// to
	ProductId common.PRODUCT_ID   `protobuf:"varint,2,opt,name=product_id,json=productId,proto3,enum=common.PRODUCT_ID" json:"product_id,omitempty"` // 产品
	Channel   common.CHANNEL_TYPE `protobuf:"varint,3,opt,name=channel,proto3,enum=common.CHANNEL_TYPE" json:"channel,omitempty"`                    // 渠道
	PlayerIds []uint64            `protobuf:"varint,4,rep,packed,name=player_ids,json=playerIds,proto3" json:"player_ids,omitempty"`                 // 玩家id
	// msg
	Assembly *common.MailAssembly `protobuf:"bytes,6,opt,name=assembly,proto3" json:"assembly,omitempty"` // 邮件装配
}

func (x *SendMailReq) Reset() {
	*x = SendMailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msgrpc_msgrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMailReq) ProtoMessage() {}

func (x *SendMailReq) ProtoReflect() protoreflect.Message {
	mi := &file_msgrpc_msgrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMailReq.ProtoReflect.Descriptor instead.
func (*SendMailReq) Descriptor() ([]byte, []int) {
	return file_msgrpc_msgrpc_proto_rawDescGZIP(), []int{0}
}

func (x *SendMailReq) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *SendMailReq) GetProductId() common.PRODUCT_ID {
	if x != nil {
		return x.ProductId
	}
	return common.PRODUCT_ID(0)
}

func (x *SendMailReq) GetChannel() common.CHANNEL_TYPE {
	if x != nil {
		return x.Channel
	}
	return common.CHANNEL_TYPE(0)
}

func (x *SendMailReq) GetPlayerIds() []uint64 {
	if x != nil {
		return x.PlayerIds
	}
	return nil
}

func (x *SendMailReq) GetAssembly() *common.MailAssembly {
	if x != nil {
		return x.Assembly
	}
	return nil
}

type SendMailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *SendMailRsp) Reset() {
	*x = SendMailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msgrpc_msgrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMailRsp) ProtoMessage() {}

func (x *SendMailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_msgrpc_msgrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMailRsp.ProtoReflect.Descriptor instead.
func (*SendMailRsp) Descriptor() ([]byte, []int) {
	return file_msgrpc_msgrpc_proto_rawDescGZIP(), []int{1}
}

func (x *SendMailRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 发送系统邮件
type SendSystemMailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// From
	Sender string `protobuf:"bytes,1,opt,name=sender,proto3" json:"sender,omitempty"` // 发件人
	// to
	ProductId common.PRODUCT_ID   `protobuf:"varint,2,opt,name=product_id,json=productId,proto3,enum=common.PRODUCT_ID" json:"product_id,omitempty"` // 产品
	Channel   common.CHANNEL_TYPE `protobuf:"varint,3,opt,name=channel,proto3,enum=common.CHANNEL_TYPE" json:"channel,omitempty"`                    // 渠道
	UserLabel string              `protobuf:"bytes,5,opt,name=user_label,json=userLabel,proto3" json:"user_label,omitempty"`                         // 用户标签
	// msg
	Assembly *common.MailAssembly `protobuf:"bytes,6,opt,name=assembly,proto3" json:"assembly,omitempty"` // 邮件装配
}

func (x *SendSystemMailReq) Reset() {
	*x = SendSystemMailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msgrpc_msgrpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSystemMailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSystemMailReq) ProtoMessage() {}

func (x *SendSystemMailReq) ProtoReflect() protoreflect.Message {
	mi := &file_msgrpc_msgrpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSystemMailReq.ProtoReflect.Descriptor instead.
func (*SendSystemMailReq) Descriptor() ([]byte, []int) {
	return file_msgrpc_msgrpc_proto_rawDescGZIP(), []int{2}
}

func (x *SendSystemMailReq) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *SendSystemMailReq) GetProductId() common.PRODUCT_ID {
	if x != nil {
		return x.ProductId
	}
	return common.PRODUCT_ID(0)
}

func (x *SendSystemMailReq) GetChannel() common.CHANNEL_TYPE {
	if x != nil {
		return x.Channel
	}
	return common.CHANNEL_TYPE(0)
}

func (x *SendSystemMailReq) GetUserLabel() string {
	if x != nil {
		return x.UserLabel
	}
	return ""
}

func (x *SendSystemMailReq) GetAssembly() *common.MailAssembly {
	if x != nil {
		return x.Assembly
	}
	return nil
}

type SendSystemMailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *SendSystemMailRsp) Reset() {
	*x = SendSystemMailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msgrpc_msgrpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSystemMailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSystemMailRsp) ProtoMessage() {}

func (x *SendSystemMailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_msgrpc_msgrpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSystemMailRsp.ProtoReflect.Descriptor instead.
func (*SendSystemMailRsp) Descriptor() ([]byte, []int) {
	return file_msgrpc_msgrpc_proto_rawDescGZIP(), []int{3}
}

func (x *SendSystemMailRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 发送广播
type SendMsgBroadcastReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId common.PRODUCT_ID              `protobuf:"varint,1,opt,name=product_id,json=productId,proto3,enum=common.PRODUCT_ID" json:"product_id,omitempty"` // 产品
	Channel   common.CHANNEL_TYPE            `protobuf:"varint,2,opt,name=channel,proto3,enum=common.CHANNEL_TYPE" json:"channel,omitempty"`                    // 渠道
	Detail    *common.MsgBroadcastDetailInfo `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail,omitempty"`                                                // 广播消息
}

func (x *SendMsgBroadcastReq) Reset() {
	*x = SendMsgBroadcastReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msgrpc_msgrpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMsgBroadcastReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMsgBroadcastReq) ProtoMessage() {}

func (x *SendMsgBroadcastReq) ProtoReflect() protoreflect.Message {
	mi := &file_msgrpc_msgrpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMsgBroadcastReq.ProtoReflect.Descriptor instead.
func (*SendMsgBroadcastReq) Descriptor() ([]byte, []int) {
	return file_msgrpc_msgrpc_proto_rawDescGZIP(), []int{4}
}

func (x *SendMsgBroadcastReq) GetProductId() common.PRODUCT_ID {
	if x != nil {
		return x.ProductId
	}
	return common.PRODUCT_ID(0)
}

func (x *SendMsgBroadcastReq) GetChannel() common.CHANNEL_TYPE {
	if x != nil {
		return x.Channel
	}
	return common.CHANNEL_TYPE(0)
}

func (x *SendMsgBroadcastReq) GetDetail() *common.MsgBroadcastDetailInfo {
	if x != nil {
		return x.Detail
	}
	return nil
}

type SendMsgBroadcastRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *SendMsgBroadcastRsp) Reset() {
	*x = SendMsgBroadcastRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msgrpc_msgrpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMsgBroadcastRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMsgBroadcastRsp) ProtoMessage() {}

func (x *SendMsgBroadcastRsp) ProtoReflect() protoreflect.Message {
	mi := &file_msgrpc_msgrpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMsgBroadcastRsp.ProtoReflect.Descriptor instead.
func (*SendMsgBroadcastRsp) Descriptor() ([]byte, []int) {
	return file_msgrpc_msgrpc_proto_rawDescGZIP(), []int{5}
}

func (x *SendMsgBroadcastRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

var File_msgrpc_msgrpc_proto protoreflect.FileDescriptor

var file_msgrpc_msgrpc_proto_rawDesc = []byte{
	0x0a, 0x13, 0x6d, 0x73, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x73, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x70, 0x63, 0x1a, 0x0a,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd9, 0x01, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x4d,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x31,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x52, 0x4f, 0x44,
	0x55, 0x43, 0x54, 0x5f, 0x49, 0x44, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x2e, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x48, 0x41, 0x4e,
	0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x04, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73,
	0x12, 0x30, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x69, 0x6c,
	0x41, 0x73, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x79, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x6d, 0x62,
	0x6c, 0x79, 0x22, 0x2f, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03,
	0x72, 0x65, 0x74, 0x22, 0xdf, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x12, 0x31, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x49, 0x44, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43,
	0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x30, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d,
	0x61, 0x69, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x79, 0x52, 0x08, 0x61, 0x73, 0x73,
	0x65, 0x6d, 0x62, 0x6c, 0x79, 0x22, 0x35, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x22, 0xb0, 0x01, 0x0a,
	0x13, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x49, 0x44, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x36, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4d, 0x73, 0x67, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22,
	0x37, 0x0a, 0x13, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x32, 0xe4, 0x01, 0x0a, 0x0a, 0x4d, 0x73, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x53, 0x65, 0x6e, 0x64, 0x4d,
	0x61, 0x69, 0x6c, 0x12, 0x14, 0x2e, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x6d, 0x61, 0x69, 0x6c,
	0x52, 0x70, 0x63, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x4a, 0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d,
	0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a,
	0x1a, 0x2e, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x50, 0x0a,
	0x10, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73,
	0x74, 0x12, 0x1c, 0x2e, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x53, 0x65, 0x6e, 0x64,
	0x4d, 0x73, 0x67, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x1c, 0x2e, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73,
	0x67, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42,
	0x0b, 0x5a, 0x09, 0x2e, 0x3b, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_msgrpc_msgrpc_proto_rawDescOnce sync.Once
	file_msgrpc_msgrpc_proto_rawDescData = file_msgrpc_msgrpc_proto_rawDesc
)

func file_msgrpc_msgrpc_proto_rawDescGZIP() []byte {
	file_msgrpc_msgrpc_proto_rawDescOnce.Do(func() {
		file_msgrpc_msgrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_msgrpc_msgrpc_proto_rawDescData)
	})
	return file_msgrpc_msgrpc_proto_rawDescData
}

var file_msgrpc_msgrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_msgrpc_msgrpc_proto_goTypes = []interface{}{
	(*SendMailReq)(nil),                   // 0: mailRpc.SendMailReq
	(*SendMailRsp)(nil),                   // 1: mailRpc.SendMailRsp
	(*SendSystemMailReq)(nil),             // 2: mailRpc.SendSystemMailReq
	(*SendSystemMailRsp)(nil),             // 3: mailRpc.SendSystemMailRsp
	(*SendMsgBroadcastReq)(nil),           // 4: mailRpc.SendMsgBroadcastReq
	(*SendMsgBroadcastRsp)(nil),           // 5: mailRpc.SendMsgBroadcastRsp
	(common.PRODUCT_ID)(0),                // 6: common.PRODUCT_ID
	(common.CHANNEL_TYPE)(0),              // 7: common.CHANNEL_TYPE
	(*common.MailAssembly)(nil),           // 8: common.MailAssembly
	(*common.Result)(nil),                 // 9: common.Result
	(*common.MsgBroadcastDetailInfo)(nil), // 10: common.MsgBroadcastDetailInfo
}
var file_msgrpc_msgrpc_proto_depIdxs = []int32{
	6,  // 0: mailRpc.SendMailReq.product_id:type_name -> common.PRODUCT_ID
	7,  // 1: mailRpc.SendMailReq.channel:type_name -> common.CHANNEL_TYPE
	8,  // 2: mailRpc.SendMailReq.assembly:type_name -> common.MailAssembly
	9,  // 3: mailRpc.SendMailRsp.ret:type_name -> common.Result
	6,  // 4: mailRpc.SendSystemMailReq.product_id:type_name -> common.PRODUCT_ID
	7,  // 5: mailRpc.SendSystemMailReq.channel:type_name -> common.CHANNEL_TYPE
	8,  // 6: mailRpc.SendSystemMailReq.assembly:type_name -> common.MailAssembly
	9,  // 7: mailRpc.SendSystemMailRsp.ret:type_name -> common.Result
	6,  // 8: mailRpc.SendMsgBroadcastReq.product_id:type_name -> common.PRODUCT_ID
	7,  // 9: mailRpc.SendMsgBroadcastReq.channel:type_name -> common.CHANNEL_TYPE
	10, // 10: mailRpc.SendMsgBroadcastReq.detail:type_name -> common.MsgBroadcastDetailInfo
	9,  // 11: mailRpc.SendMsgBroadcastRsp.ret:type_name -> common.Result
	0,  // 12: mailRpc.MsgService.SendMail:input_type -> mailRpc.SendMailReq
	2,  // 13: mailRpc.MsgService.SendSystemMail:input_type -> mailRpc.SendSystemMailReq
	4,  // 14: mailRpc.MsgService.SendMsgBroadcast:input_type -> mailRpc.SendMsgBroadcastReq
	1,  // 15: mailRpc.MsgService.SendMail:output_type -> mailRpc.SendMailRsp
	3,  // 16: mailRpc.MsgService.SendSystemMail:output_type -> mailRpc.SendSystemMailRsp
	5,  // 17: mailRpc.MsgService.SendMsgBroadcast:output_type -> mailRpc.SendMsgBroadcastRsp
	15, // [15:18] is the sub-list for method output_type
	12, // [12:15] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_msgrpc_msgrpc_proto_init() }
func file_msgrpc_msgrpc_proto_init() {
	if File_msgrpc_msgrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_msgrpc_msgrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msgrpc_msgrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msgrpc_msgrpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSystemMailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msgrpc_msgrpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSystemMailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msgrpc_msgrpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMsgBroadcastReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msgrpc_msgrpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMsgBroadcastRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_msgrpc_msgrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_msgrpc_msgrpc_proto_goTypes,
		DependencyIndexes: file_msgrpc_msgrpc_proto_depIdxs,
		MessageInfos:      file_msgrpc_msgrpc_proto_msgTypes,
	}.Build()
	File_msgrpc_msgrpc_proto = out.File
	file_msgrpc_msgrpc_proto_rawDesc = nil
	file_msgrpc_msgrpc_proto_goTypes = nil
	file_msgrpc_msgrpc_proto_depIdxs = nil
}
