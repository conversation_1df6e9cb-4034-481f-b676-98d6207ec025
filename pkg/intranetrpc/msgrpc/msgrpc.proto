syntax = "proto3";

package mailRpc;
option go_package = ".;mailRpc";


import "enum.proto";
import "errors.proto";
import "common.proto";


// 发送邮件
message SendMailReq {
    // From
    string              sender                         = 1;  // 发件人
    // to
    common.PRODUCT_ID   product_id                     = 2;  // 产品
    common.CHANNEL_TYPE channel                        = 3;  // 渠道
    repeated            uint64              player_ids = 4;  // 玩家id
    // msg
    common.MailAssembly assembly                       = 6;  // 邮件装配
}

message SendMailRsp {
    common.Result ret = 1; // 返回结果
}

// 发送系统邮件
message SendSystemMailReq {
    // From
    string              sender                         = 1;  // 发件人
    // to
    common.PRODUCT_ID   product_id                     = 2;  // 产品
    common.CHANNEL_TYPE channel                        = 3;  // 渠道
    string              user_label                     = 5;  // 用户标签 
    // msg
    common.MailAssembly assembly                       = 6;  // 邮件装配
}

message SendSystemMailRsp {
    common.Result ret = 1; // 返回结果
}

// 发送广播
message SendMsgBroadcastReq {
    common.PRODUCT_ID             product_id = 1;  // 产品
    common.CHANNEL_TYPE           channel    = 2;  // 渠道
    common.MsgBroadcastDetailInfo detail     = 3;  // 广播消息
}

message SendMsgBroadcastRsp {
    common.Result ret = 1; // 返回结果
}

// RPC服务
service MsgService {
    // 发送邮件
    rpc SendMail(SendMailReq) returns (SendMailRsp) {}
    rpc SendSystemMail(SendSystemMailReq) returns (SendSystemMailRsp) {}
    // 发送广播
    rpc SendMsgBroadcast(SendMsgBroadcastReq) returns (SendMsgBroadcastRsp) {}
}
