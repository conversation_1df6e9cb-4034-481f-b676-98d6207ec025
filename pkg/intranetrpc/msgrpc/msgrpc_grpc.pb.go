// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: msgrpc/msgrpc.proto

package mailRpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MsgService_SendMail_FullMethodName         = "/mailRpc.MsgService/SendMail"
	MsgService_SendSystemMail_FullMethodName   = "/mailRpc.MsgService/SendSystemMail"
	MsgService_SendMsgBroadcast_FullMethodName = "/mailRpc.MsgService/SendMsgBroadcast"
)

// MsgServiceClient is the client API for MsgService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MsgServiceClient interface {
	// 发送邮件
	SendMail(ctx context.Context, in *SendMailReq, opts ...grpc.CallOption) (*SendMailRsp, error)
	SendSystemMail(ctx context.Context, in *SendSystemMailReq, opts ...grpc.CallOption) (*SendSystemMailRsp, error)
	// 发送广播
	SendMsgBroadcast(ctx context.Context, in *SendMsgBroadcastReq, opts ...grpc.CallOption) (*SendMsgBroadcastRsp, error)
}

type msgServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMsgServiceClient(cc grpc.ClientConnInterface) MsgServiceClient {
	return &msgServiceClient{cc}
}

func (c *msgServiceClient) SendMail(ctx context.Context, in *SendMailReq, opts ...grpc.CallOption) (*SendMailRsp, error) {
	out := new(SendMailRsp)
	err := c.cc.Invoke(ctx, MsgService_SendMail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *msgServiceClient) SendSystemMail(ctx context.Context, in *SendSystemMailReq, opts ...grpc.CallOption) (*SendSystemMailRsp, error) {
	out := new(SendSystemMailRsp)
	err := c.cc.Invoke(ctx, MsgService_SendSystemMail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *msgServiceClient) SendMsgBroadcast(ctx context.Context, in *SendMsgBroadcastReq, opts ...grpc.CallOption) (*SendMsgBroadcastRsp, error) {
	out := new(SendMsgBroadcastRsp)
	err := c.cc.Invoke(ctx, MsgService_SendMsgBroadcast_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MsgServiceServer is the server API for MsgService service.
// All implementations should embed UnimplementedMsgServiceServer
// for forward compatibility
type MsgServiceServer interface {
	// 发送邮件
	SendMail(context.Context, *SendMailReq) (*SendMailRsp, error)
	SendSystemMail(context.Context, *SendSystemMailReq) (*SendSystemMailRsp, error)
	// 发送广播
	SendMsgBroadcast(context.Context, *SendMsgBroadcastReq) (*SendMsgBroadcastRsp, error)
}

// UnimplementedMsgServiceServer should be embedded to have forward compatible implementations.
type UnimplementedMsgServiceServer struct {
}

func (UnimplementedMsgServiceServer) SendMail(context.Context, *SendMailReq) (*SendMailRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMail not implemented")
}
func (UnimplementedMsgServiceServer) SendSystemMail(context.Context, *SendSystemMailReq) (*SendSystemMailRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSystemMail not implemented")
}
func (UnimplementedMsgServiceServer) SendMsgBroadcast(context.Context, *SendMsgBroadcastReq) (*SendMsgBroadcastRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMsgBroadcast not implemented")
}

// UnsafeMsgServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MsgServiceServer will
// result in compilation errors.
type UnsafeMsgServiceServer interface {
	mustEmbedUnimplementedMsgServiceServer()
}

func RegisterMsgServiceServer(s grpc.ServiceRegistrar, srv MsgServiceServer) {
	s.RegisterService(&MsgService_ServiceDesc, srv)
}

func _MsgService_SendMail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MsgServiceServer).SendMail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MsgService_SendMail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MsgServiceServer).SendMail(ctx, req.(*SendMailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MsgService_SendSystemMail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSystemMailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MsgServiceServer).SendSystemMail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MsgService_SendSystemMail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MsgServiceServer).SendSystemMail(ctx, req.(*SendSystemMailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MsgService_SendMsgBroadcast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMsgBroadcastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MsgServiceServer).SendMsgBroadcast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MsgService_SendMsgBroadcast_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MsgServiceServer).SendMsgBroadcast(ctx, req.(*SendMsgBroadcastReq))
	}
	return interceptor(ctx, in, info, handler)
}

// MsgService_ServiceDesc is the grpc.ServiceDesc for MsgService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MsgService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mailRpc.MsgService",
	HandlerType: (*MsgServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendMail",
			Handler:    _MsgService_SendMail_Handler,
		},
		{
			MethodName: "SendSystemMail",
			Handler:    _MsgService_SendSystemMail_Handler,
		},
		{
			MethodName: "SendMsgBroadcast",
			Handler:    _MsgService_SendMsgBroadcast_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "msgrpc/msgrpc.proto",
}
