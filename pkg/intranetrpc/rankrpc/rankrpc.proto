syntax = "proto3";

package rankRpc;
option go_package = ".;rankRpc";

import "common.proto";
import "errors.proto";

// 提交钓场结算数据
message SubmitTripSettleReq {
    uint64 player_id = 1; // 玩家id
    int64 pond_id = 2; // 钓场id
    common.FishDetailInfo val_fish = 3; // 最有价值的鱼
    common.FishDetailInfo weight_fish = 4; // 最重的鱼
}

message  SubmitTipSettleRsp {
    common.Result ret                      = 1; // 返回结果
}

service RankService {
    // 钓场结算数据
    rpc SubmitTripSettle(SubmitTripSettleReq) returns (SubmitTipSettleRsp);
}