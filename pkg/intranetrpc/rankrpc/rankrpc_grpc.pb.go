// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: rankrpc/rankrpc.proto

package rankRpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RankService_SubmitTripSettle_FullMethodName = "/rankRpc.RankService/SubmitTripSettle"
)

// RankServiceClient is the client API for RankService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RankServiceClient interface {
	// 钓场结算数据
	SubmitTripSettle(ctx context.Context, in *SubmitTripSettleReq, opts ...grpc.CallOption) (*SubmitTipSettleRsp, error)
}

type rankServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRankServiceClient(cc grpc.ClientConnInterface) RankServiceClient {
	return &rankServiceClient{cc}
}

func (c *rankServiceClient) SubmitTripSettle(ctx context.Context, in *SubmitTripSettleReq, opts ...grpc.CallOption) (*SubmitTipSettleRsp, error) {
	out := new(SubmitTipSettleRsp)
	err := c.cc.Invoke(ctx, RankService_SubmitTripSettle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RankServiceServer is the server API for RankService service.
// All implementations should embed UnimplementedRankServiceServer
// for forward compatibility
type RankServiceServer interface {
	// 钓场结算数据
	SubmitTripSettle(context.Context, *SubmitTripSettleReq) (*SubmitTipSettleRsp, error)
}

// UnimplementedRankServiceServer should be embedded to have forward compatible implementations.
type UnimplementedRankServiceServer struct {
}

func (UnimplementedRankServiceServer) SubmitTripSettle(context.Context, *SubmitTripSettleReq) (*SubmitTipSettleRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitTripSettle not implemented")
}

// UnsafeRankServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RankServiceServer will
// result in compilation errors.
type UnsafeRankServiceServer interface {
	mustEmbedUnimplementedRankServiceServer()
}

func RegisterRankServiceServer(s grpc.ServiceRegistrar, srv RankServiceServer) {
	s.RegisterService(&RankService_ServiceDesc, srv)
}

func _RankService_SubmitTripSettle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitTripSettleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).SubmitTripSettle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_SubmitTripSettle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).SubmitTripSettle(ctx, req.(*SubmitTripSettleReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RankService_ServiceDesc is the grpc.ServiceDesc for RankService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RankService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "rankRpc.RankService",
	HandlerType: (*RankServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubmitTripSettle",
			Handler:    _RankService_SubmitTripSettle_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rankrpc/rankrpc.proto",
}
