// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: spotrpc/spotrpc.proto

package spotRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PondChangeEventNtf 钓场事件变化通知
type PondChangeEventNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId  uint64                        `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`   // 玩家id
	RoomInfo  *common.RoomInfo              `protobuf:"bytes,2,opt,name=room_info,json=roomInfo,proto3" json:"room_info,omitempty"`    // 房间信息
	EventList []*common.PondEventChangeInfo `protobuf:"bytes,3,rep,name=event_list,json=eventList,proto3" json:"event_list,omitempty"` // 事件列表
}

func (x *PondChangeEventNtf) Reset() {
	*x = PondChangeEventNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spotrpc_spotrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PondChangeEventNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PondChangeEventNtf) ProtoMessage() {}

func (x *PondChangeEventNtf) ProtoReflect() protoreflect.Message {
	mi := &file_spotrpc_spotrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PondChangeEventNtf.ProtoReflect.Descriptor instead.
func (*PondChangeEventNtf) Descriptor() ([]byte, []int) {
	return file_spotrpc_spotrpc_proto_rawDescGZIP(), []int{0}
}

func (x *PondChangeEventNtf) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PondChangeEventNtf) GetRoomInfo() *common.RoomInfo {
	if x != nil {
		return x.RoomInfo
	}
	return nil
}

func (x *PondChangeEventNtf) GetEventList() []*common.PondEventChangeInfo {
	if x != nil {
		return x.EventList
	}
	return nil
}

var File_spotrpc_spotrpc_proto protoreflect.FileDescriptor

var file_spotrpc_spotrpc_proto_rawDesc = []byte{
	0x0a, 0x15, 0x73, 0x70, 0x6f, 0x74, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x70, 0x6f, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x70, 0x63,
	0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9c, 0x01, 0x0a,
	0x12, 0x50, 0x6f, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x4e, 0x74, 0x66, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x3a, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x6e,
	0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x32, 0x53, 0x0a, 0x0b, 0x53,
	0x70, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x15, 0x50, 0x6f,
	0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x12, 0x1b, 0x2e, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x50, 0x6f,
	0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x74, 0x66,
	0x1a, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x42, 0x0b, 0x5a, 0x09, 0x2e, 0x3b, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x70, 0x63, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_spotrpc_spotrpc_proto_rawDescOnce sync.Once
	file_spotrpc_spotrpc_proto_rawDescData = file_spotrpc_spotrpc_proto_rawDesc
)

func file_spotrpc_spotrpc_proto_rawDescGZIP() []byte {
	file_spotrpc_spotrpc_proto_rawDescOnce.Do(func() {
		file_spotrpc_spotrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_spotrpc_spotrpc_proto_rawDescData)
	})
	return file_spotrpc_spotrpc_proto_rawDescData
}

var file_spotrpc_spotrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_spotrpc_spotrpc_proto_goTypes = []interface{}{
	(*PondChangeEventNtf)(nil),         // 0: spotRpc.PondChangeEventNtf
	(*common.RoomInfo)(nil),            // 1: common.RoomInfo
	(*common.PondEventChangeInfo)(nil), // 2: common.PondEventChangeInfo
	(*common.Result)(nil),              // 3: common.Result
}
var file_spotrpc_spotrpc_proto_depIdxs = []int32{
	1, // 0: spotRpc.PondChangeEventNtf.room_info:type_name -> common.RoomInfo
	2, // 1: spotRpc.PondChangeEventNtf.event_list:type_name -> common.PondEventChangeInfo
	0, // 2: spotRpc.SpotService.PondChangeEventNotify:input_type -> spotRpc.PondChangeEventNtf
	3, // 3: spotRpc.SpotService.PondChangeEventNotify:output_type -> common.Result
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_spotrpc_spotrpc_proto_init() }
func file_spotrpc_spotrpc_proto_init() {
	if File_spotrpc_spotrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_spotrpc_spotrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PondChangeEventNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_spotrpc_spotrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_spotrpc_spotrpc_proto_goTypes,
		DependencyIndexes: file_spotrpc_spotrpc_proto_depIdxs,
		MessageInfos:      file_spotrpc_spotrpc_proto_msgTypes,
	}.Build()
	File_spotrpc_spotrpc_proto = out.File
	file_spotrpc_spotrpc_proto_rawDesc = nil
	file_spotrpc_spotrpc_proto_goTypes = nil
	file_spotrpc_spotrpc_proto_depIdxs = nil
}
