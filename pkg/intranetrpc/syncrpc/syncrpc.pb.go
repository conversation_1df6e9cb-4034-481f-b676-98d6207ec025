// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: syncrpc/syncrpc.proto

package syncRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BroadcastEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventInfo *common.EventCommon `protobuf:"bytes,1,opt,name=event_info,json=eventInfo,proto3" json:"event_info,omitempty"` // 事件信息
}

func (x *BroadcastEvent) Reset() {
	*x = BroadcastEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_syncrpc_syncrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BroadcastEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastEvent) ProtoMessage() {}

func (x *BroadcastEvent) ProtoReflect() protoreflect.Message {
	mi := &file_syncrpc_syncrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastEvent.ProtoReflect.Descriptor instead.
func (*BroadcastEvent) Descriptor() ([]byte, []int) {
	return file_syncrpc_syncrpc_proto_rawDescGZIP(), []int{0}
}

func (x *BroadcastEvent) GetEventInfo() *common.EventCommon {
	if x != nil {
		return x.EventInfo
	}
	return nil
}

var File_syncrpc_syncrpc_proto protoreflect.FileDescriptor

var file_syncrpc_syncrpc_proto_rawDesc = []byte{
	0x0a, 0x15, 0x73, 0x79, 0x6e, 0x63, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x72, 0x70,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x70, 0x63,
	0x1a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x44,
	0x0a, 0x0e, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x32, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x32, 0x4c, 0x0a, 0x0b, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x12, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x2e, 0x73, 0x79, 0x6e, 0x63,
	0x52, 0x70, 0x63, 0x2e, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x1a, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x0b, 0x5a, 0x09, 0x2e, 0x3b, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x70, 0x63, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_syncrpc_syncrpc_proto_rawDescOnce sync.Once
	file_syncrpc_syncrpc_proto_rawDescData = file_syncrpc_syncrpc_proto_rawDesc
)

func file_syncrpc_syncrpc_proto_rawDescGZIP() []byte {
	file_syncrpc_syncrpc_proto_rawDescOnce.Do(func() {
		file_syncrpc_syncrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_syncrpc_syncrpc_proto_rawDescData)
	})
	return file_syncrpc_syncrpc_proto_rawDescData
}

var file_syncrpc_syncrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_syncrpc_syncrpc_proto_goTypes = []interface{}{
	(*BroadcastEvent)(nil),     // 0: syncRpc.BroadcastEvent
	(*common.EventCommon)(nil), // 1: common.EventCommon
	(*common.Result)(nil),      // 2: common.Result
}
var file_syncrpc_syncrpc_proto_depIdxs = []int32{
	1, // 0: syncRpc.BroadcastEvent.event_info:type_name -> common.EventCommon
	0, // 1: syncRpc.SyncService.BroadcastEventInfo:input_type -> syncRpc.BroadcastEvent
	2, // 2: syncRpc.SyncService.BroadcastEventInfo:output_type -> common.Result
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_syncrpc_syncrpc_proto_init() }
func file_syncrpc_syncrpc_proto_init() {
	if File_syncrpc_syncrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_syncrpc_syncrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BroadcastEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_syncrpc_syncrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_syncrpc_syncrpc_proto_goTypes,
		DependencyIndexes: file_syncrpc_syncrpc_proto_depIdxs,
		MessageInfos:      file_syncrpc_syncrpc_proto_msgTypes,
	}.Build()
	File_syncrpc_syncrpc_proto = out.File
	file_syncrpc_syncrpc_proto_rawDesc = nil
	file_syncrpc_syncrpc_proto_goTypes = nil
	file_syncrpc_syncrpc_proto_depIdxs = nil
}
