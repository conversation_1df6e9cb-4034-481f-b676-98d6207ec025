syntax = "proto3";

package userRpc;
option go_package = ".;tripRpc";

import "common.proto";
import "enum.proto";
import "errors.proto";

// Join Trip
message JoinTripReq {
    int32                   product_id      = 1;  // 产品 ID
    int64                   pond_id         = 2;  // mapID
    int32                   spot_id         = 3;  // spotID
    common.GAME_TYPE        game_type       = 4;  // game type
    repeated common.Fisher  fishers         = 5;  // 钓鱼玩家
}

message JoinRoomRet {
    uint64                  uid             = 1;   // Uid
    common.RoomInfo         room_info       = 2;   // 房间信息
}

// Join Trip Rsp
message JoinTripRsp {
    common.Result         ret               = 1;
    repeated JoinRoomRet  join_infos        = 2;
}

// Exit Trip
message ExitTripReq {
    int32                   product_id      = 1;  // 产品 ID
    int64                   pond_id         = 2;  // 钓场id
    common.GAME_TYPE        game_type       = 3;  // game type
    repeated common.Fisher  fishers         = 4;  // 钓鱼玩家
}

// Exit Trip
message ExitTripRsp {
    common.Result         ret               = 1;
}

// Query Trip
message QueryTripReq {
    uint64                  player_id       = 1;  // 玩家id
}

// Query Trip
message QueryTripRsp {
    common.Result         ret               = 1;
    common.RoomInfo       room_info         = 2;   // 房间信息
}

// Query Players
message QueryRoomPlayersReq {
    string                room_id           = 1; // 房间id
}

// Query Players
message QueryRoomPlayersRsp {
    common.Result          ret              = 1;
    repeated common.Fisher fishers          = 2;  // 钓鱼玩家
}

// 玩家断线离开(只删除房间中玩家数据 保留玩家房间数据)
message OfflineExitReq {
    uint64                  player_id        = 1;  // 玩家id
}

// 玩家断线离开(只删除房间中玩家数据 保留玩家房间数据)
message OfflineExitRsp {
    common.Result         ret                = 1;
}

// RPC服务
service TripService {
    // 开始Trip，加入房间
    rpc JoinTripRoom(JoinTripReq) returns (JoinTripRsp) {}

    // 退出Trip，退出房间
    rpc ExitTripRoom(ExitTripReq) returns (ExitTripRsp) {}

    // 查询玩家所在房间
    rpc QueryTrip(QueryTripReq) returns (QueryTripRsp) {}

    // QueryRoomPlayers
    rpc QueryRoomPlayers(QueryRoomPlayersReq) returns (QueryRoomPlayersRsp) {}

    // 玩家断线离开
    rpc OfflineExit(OfflineExitReq) returns (OfflineExitRsp) {}
}