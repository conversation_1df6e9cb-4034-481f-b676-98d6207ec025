// 天气系统模块

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: worldrpc/worldrpc.proto

package worldRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WeatherReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId int64 `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"` // 渔场id
}

func (x *WeatherReq) Reset() {
	*x = WeatherReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_worldrpc_worldrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeatherReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeatherReq) ProtoMessage() {}

func (x *WeatherReq) ProtoReflect() protoreflect.Message {
	mi := &file_worldrpc_worldrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeatherReq.ProtoReflect.Descriptor instead.
func (*WeatherReq) Descriptor() ([]byte, []int) {
	return file_worldrpc_worldrpc_proto_rawDescGZIP(), []int{0}
}

func (x *WeatherReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

type WeatherRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result        `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Data *common.WeatherPeriod `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"` // 查询列表
	Time *common.GameTime      `protobuf:"bytes,3,opt,name=time,proto3" json:"time,omitempty"`
}

func (x *WeatherRsp) Reset() {
	*x = WeatherRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_worldrpc_worldrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeatherRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeatherRsp) ProtoMessage() {}

func (x *WeatherRsp) ProtoReflect() protoreflect.Message {
	mi := &file_worldrpc_worldrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeatherRsp.ProtoReflect.Descriptor instead.
func (*WeatherRsp) Descriptor() ([]byte, []int) {
	return file_worldrpc_worldrpc_proto_rawDescGZIP(), []int{1}
}

func (x *WeatherRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *WeatherRsp) GetData() *common.WeatherPeriod {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *WeatherRsp) GetTime() *common.GameTime {
	if x != nil {
		return x.Time
	}
	return nil
}

var File_worldrpc_worldrpc_proto protoreflect.FileDescriptor

var file_worldrpc_worldrpc_proto_rawDesc = []byte{
	0x0a, 0x17, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x72, 0x70, 0x63, 0x2f, 0x77, 0x6f, 0x72, 0x6c, 0x64,
	0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x77, 0x6f, 0x72, 0x6c, 0x64,
	0x52, 0x70, 0x63, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x25, 0x0a, 0x0a, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a,
	0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x0a, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65,
	0x72, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x29, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x57, 0x65,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x24, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x32, 0x4a, 0x0a, 0x0c, 0x57, 0x6f, 0x72, 0x6c, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x57, 0x65,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x12, 0x14, 0x2e, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x52, 0x70, 0x63,
	0x2e, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x77, 0x6f,
	0x72, 0x6c, 0x64, 0x52, 0x70, 0x63, 0x2e, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x3b, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x52, 0x70,
	0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_worldrpc_worldrpc_proto_rawDescOnce sync.Once
	file_worldrpc_worldrpc_proto_rawDescData = file_worldrpc_worldrpc_proto_rawDesc
)

func file_worldrpc_worldrpc_proto_rawDescGZIP() []byte {
	file_worldrpc_worldrpc_proto_rawDescOnce.Do(func() {
		file_worldrpc_worldrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_worldrpc_worldrpc_proto_rawDescData)
	})
	return file_worldrpc_worldrpc_proto_rawDescData
}

var file_worldrpc_worldrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_worldrpc_worldrpc_proto_goTypes = []interface{}{
	(*WeatherReq)(nil),           // 0: worldRpc.WeatherReq
	(*WeatherRsp)(nil),           // 1: worldRpc.WeatherRsp
	(*common.Result)(nil),        // 2: common.Result
	(*common.WeatherPeriod)(nil), // 3: common.WeatherPeriod
	(*common.GameTime)(nil),      // 4: common.GameTime
}
var file_worldrpc_worldrpc_proto_depIdxs = []int32{
	2, // 0: worldRpc.WeatherRsp.ret:type_name -> common.Result
	3, // 1: worldRpc.WeatherRsp.data:type_name -> common.WeatherPeriod
	4, // 2: worldRpc.WeatherRsp.time:type_name -> common.GameTime
	0, // 3: worldRpc.WorldService.GetWeather:input_type -> worldRpc.WeatherReq
	1, // 4: worldRpc.WorldService.GetWeather:output_type -> worldRpc.WeatherRsp
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_worldrpc_worldrpc_proto_init() }
func file_worldrpc_worldrpc_proto_init() {
	if File_worldrpc_worldrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_worldrpc_worldrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeatherReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_worldrpc_worldrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeatherRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_worldrpc_worldrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_worldrpc_worldrpc_proto_goTypes,
		DependencyIndexes: file_worldrpc_worldrpc_proto_depIdxs,
		MessageInfos:      file_worldrpc_worldrpc_proto_msgTypes,
	}.Build()
	File_worldrpc_worldrpc_proto = out.File
	file_worldrpc_worldrpc_proto_rawDesc = nil
	file_worldrpc_worldrpc_proto_goTypes = nil
	file_worldrpc_worldrpc_proto_depIdxs = nil
}
