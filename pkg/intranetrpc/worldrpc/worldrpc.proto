// 天气系统模块
syntax = "proto3";

package worldRpc;
option go_package = ".;worldRpc";

import "common.proto";
import "errors.proto";

message WeatherReq {
    int64 pond_id         = 1; // 渔场id
}

message WeatherRsp {
    common.Result        ret  = 1;
    common.WeatherPeriod data = 2;  // 查询列表
    common.GameTime      time = 3;
}

service WorldService {
    // 查询天气
    rpc GetWeather(WeatherReq) returns (WeatherRsp){}
}