package logx

import (
	"context"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
)

func NewLogEntry(ctx context.Context) *logrus.Entry {
	opt := interceptor.GetRPCOptions(ctx)
	return logrus.WithFields(logrus.Fields{
		dict.SysWordTraceID:   opt.TraceId,
		dict.SysWordPlayerID:  opt.PlayerId,
		dict.SysWordProductID: opt.ProductId,
		dict.SysWordChannelID: opt.ChannelType,
	})
}
