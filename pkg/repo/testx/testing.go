package testx

import (
	"context"
	"fmt"
	"path"
	"runtime"
	"strings"
	"sync"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/nsqx"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func Init() {
	// severHost := "localhost"
	severHost := "************"
	InitLog()
	InitRedis(severHost)
	InitSql(severHost)
	InitConsul(severHost)
	InitNsq(severHost)
}

// TestCtx 获取测试用context
func TestCtx(playerId uint64, channelType int32) context.Context {
	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithPlayerId(playerId),
		interceptor.WithProductId(1),
		interceptor.WithChannelType(channelType),
	)
	return ctx
}

func InitRedis(server string) {
	addr := server + ":6379"
	passwd := "8888"
	conf := map[string]string{
		"addr":   server + ":6379",
		"passwd": "8888",
	}
	viper.Set(dict.ConfigRedisAddr, addr)
	viper.Set(dict.ConfigRedisPwd, passwd)
	viper.Set("redis_list", map[string]interface{}{
		dict_redis.RDBGeneral: conf,
		dict_redis.RDBLock:    conf,
		dict_redis.RDBGame:    conf,
	})
}

func InitConsul(server string) {
	viper.SetDefault(dict.ConfigConsulAddr, server+":8500")
}

func InitLog() {
	logrus.SetLevel(logrus.DebugLevel)
	logrus.SetReportCaller(true)
	logrus.SetFormatter(&logrus.JSONFormatter{
		DisableTimestamp: false,
		// TimestampFormat:  "2006-01-02 15:04:05",
		TimestampFormat: time.RFC3339Nano,
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			s := strings.Split(f.Function, ".")
			funcName := s[len(s)-1]
			filepath, filename := path.Split(f.File)
			return funcName, filepath + filename + fmt.Sprintf(":%d", f.Line)
		},
		PrettyPrint: false,
	})
}

func InitSql(server string) {
	db := dict_mysql.MysqlDBGeneral
	conf := map[string]interface{}{
		"addr":   server + ":3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     db,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		db: conf,
	})
}

func InitNsq(server string) {
	viper.SetDefault(dict.ConfigNsqDAddr, server+":4150")
	viper.SetDefault(dict.ConfigNsqHttpAddr, server+":4151")
	viper.SetDefault(dict.ConfigNsqLookUpdAddress, server+":4161")
	nsqx.Setup()
}

// 并发测试接口
// ImmediateMultiGo(1000, func() {})
func ImmediateMultiGo(num int, f func()) {
	// 准备进程管理
	ctx, cancel := context.WithCancel(context.TODO())
	// 等待管理
	wg := sync.WaitGroup{}
	ready := sync.WaitGroup{}
	ready.Add(num)

	for i := 0; i < num; i++ {
		wg.Add(1)
		// n := i
		go func() {
			defer wg.Done()
			// 进程准备就绪
			ready.Done()
			select {
			case <-ctx.Done():
				f()
			}
		}()
	}
	ready.Wait()
	cancel()
	wg.Wait()
}