# 基础组件compose

name: baser

services:

  redis:
      image: 'bitnami/redis:latest'
      ports:
        - '6379:6379'
      environment:
        - REDIS_PASSWORD=8888

  kafka:
    image: 'bitnami/kafka:latest'
    ports:
      - '9092:9092'
    environment:
      - KAFKA_CFG_NODE_ID=0
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka:9093
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER

# consul 最好不用docker启动，有点问题
  # consul:
  #   image: bitnami/consul:latest
  #   ports:
  #     - '8300:8300'
  #     - '8301:8301'
  #     - '8301:8301/udp'
  #     - '8500:8500'
  #     - '8600:8600'
  #     - '8600:8600/udp'
  #   ## 持久化
  #   volumes:
  #     - /data/consul:/bitnami
  #   # command: agent -server -bootstrap-expect=1 -node=consul -client=0.0.0.0
  #   environment:
  #     - CONSUL_AGENT_MODE=server
  #     - CONSUL_CLIENT_LAN_ADDRESS=0.0.0.0
  #     - CONSUL_BOOTSTRAP_EXPECT=1
  #     - CONSUL_NODE_NAME=consul

  nsqlookupd:
    image: nsqio/nsq
    command: /nsqlookupd
    ports:
      - "4160:4160"
      - "4161:4161"

  nsqd:
    image: nsqio/nsq
    command: /nsqd --lookupd-tcp-address=nsqlookupd:4160
    depends_on:
      - nsqlookupd
    ports:
      - "4150:4150"
      - "4151:4151"

  nsqadmin:
    image: nsqio/nsq
    command: /nsqadmin --lookupd-http-address=nsqlookupd:4161
    depends_on:
      - nsqlookupd
    ports:
      - "4171:4171"

  mysql:
    image: 'bitnami/mysql:latest'
    environment:
      - MYSQL_ROOT_PASSWORD=fancydb2024#
    ports:
      - "3306:3306"
    volumes:
      - /data/mysql:/var/lib/mysql
