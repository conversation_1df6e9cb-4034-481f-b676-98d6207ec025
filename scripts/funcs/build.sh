#!/bin/bash

. ./funcs/common.sh

#build_srv_list=(${SRV_LIST[*]})
build_srv_list=$1

updateServer() {
  # shellcheck disable=SC2164
  git fetch --all && git reset --hard  && git pull
}

updateCommon() {
  echo "update fancy-common"
  # shellcheck disable=SC2164
  cd "${COMMON_PATH}"
  pwd
  git fetch --all && git reset --hard  && git pull
  # shellcheck disable=SC2164
  # shellcheck disable=SC2103
  cd -
}

build() {
  echo "Start build"

  if [ "${build_srv_list}" == "all" ]; then
       # shellcheck disable=SC2206
       build_srv_list=(${SRV_LIST[@]})
  fi

  # shellcheck disable=SC2128
  echo target srv : "$build_srv_list"

#  srv_num=${#build_srv_list[@]}
#  if [ "$srv_num" -eq 1 ]; then
#      # shellcheck disable=SC2164
#      # shellcheck disable=SC2128
#      cd "$SERVER_PATH"/"$build_srv_list"
#      pwd
#
#      updateServer
#
#      go mod tidy
#
#      # shellcheck disable=SC2128
#      make "$build_srv_list"
#  else
#      echo sum srv num : "$srv_num"
##    make
#  fi

  # shellcheck disable=SC2068
  for srvname in ${build_srv_list[@]}
      do
        #要有可执行权限
        srv_bin=${SERVER_BIN}/${srvname}/${srvname}
        if [ ! -f "${srv_bin}" -o ! -x "${srv_bin}" ]; then
            echo "${srv_bin} 权限检查：不存在Bin或者没有可执行权限"
#            exit
        fi

        # 配置文件检查
        srv_cfg_path=${SERVER_BIN}/${srvname}/config.yml
        if [ ! -f "${srv_cfg_path}" ]; then
            echo "${srv_cfg_path} 检查：配置文件不存在"
#            exit
        fi

        # shellcheck disable=SC2115
        rm -rf "$srv_bin"

        # shellcheck disable=SC2164
        cd "$SERVER_PATH"/"${srvname}"
        pwd
        updateServer

        go mod tidy

        make "${srvname}"
  done
  echo ">>>>>>>> build end >>>>>>>>"
}

main() {
  pathCheck
  # shellcheck disable=SC2128
  srvCheck "$build_srv_list"
  updateCommon
  build
}

############## PROG #######################
main