#!/bin/bash

. ./funcs/common.sh

build_srv_list=$1

function dockerfile() {
  echo "make docker file start"

  ## 控制执行范围
  if [ "${build_srv_list}" == "all" ]; then
       # shellcheck disable=SC2206
       build_srv_list=(${SRV_LIST[@]})
  fi

  # shellcheck disable=SC2128
  echo target srv : "$build_srv_list"

  TPL_PATH=${COMMON_PATH}/tools/tc_lucky/templates/Dockerfile.tpl

  for srvname in ${build_srv_list[@]}
      do
        # shellcheck disable=SC2164
        cd "$SERVER_PATH"/"${srvname}"

        sed -e "s/{{.SrvName}}/$srvname/g" $TPL_PATH > "${SERVER_PATH}/${srvname}/Dockerfile"
  done
  echo ">>>>>>>> make dockerfile end >>>>>>>>"
}

dockerfile