#!/bin/bash
# 跳板机分发操作程序
APP_NAME=FishGame
WORKER_LIST=(worker1 worker2)
# 更新包路径
pack_path=$1
shift
update_list=$@

# 检查更新列表是否在服务列表中
contains_element () {
    local e match="$1"
    shift
    for e; do [[ "$e" == "$match" ]] && return 0;
    done
    return 1
}

if [[ $update_list == "" ]]; then
    echo "更新列表不能为空"
    exit 1
fi

if [[ $update_list == "all" ]]; then
    update_list=${WORKER_LIST[@]}
else
    for srvname in ${update_list[@]}
    do
        if ! contains_element "$srvname" "${WORKER_LIST[@]}"; then
            echo "不在列表中的服务: $srvname"
            check_flag=1
        fi
    done

    if [[ $check_flag -eq 1 ]]; then
        echo "更新列表中存在不在服务列表中的服务"
        exit 1
    fi
fi

# 同步更新
function update() {
    # 默认能使用ssh操作对应服务器
    workerName=$1
    scp ~/$APP_NAME/packs/$filename  $workerName:~/$APP_NAME/packs/
    scp ~/$APP_NAME/dev.sh $workerName:~/$APP_NAME/dev.sh
    ssh $workerName "~/$APP_NAME/dev.sh upgrade ~/$APP_NAME/packs/$filename"                    
}

for srvname in ${update_list[@]}
do
    filename=$(basename "$pack_path")

    update $srvname
done

