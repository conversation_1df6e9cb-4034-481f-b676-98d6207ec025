-- ����UID�ۺ���Ҹ��Ѻ�
SELECT
	tpo.Uid,
	sum(tpo.PriceInCent) Total
FROM
	t_payment_v2_ods tpo
WHERE TimeStampValue >= '2022-09-30'
group by
	tpo.Uid
order BY 
	Total 
desc


SELECT count(*) as t1 from (
SELECT
	tpo.Uid,
	sum(tpo.PriceInCent) Total
FROM
	t_payment_v2_ods tpo
WHERE TimeStampValue >= '2022-10-04'
group by
	tpo.Uid
order BY 
	Total 
desc
)
WHERE Total >= 9384;

SELECT count(*) as t2 from (
SELECT
	tpo.Uid,
	sum(tpo.PriceInCent) Total
FROM
	t_payment_v2_ods tpo
WHERE TimeStampValue >= '2022-10-04'
group by
	tpo.Uid
order BY 
	Total 
desc
)
WHERE Total >= 3750
and Total < 9384;

SELECT count(*) as t3 from (
SELECT
	tpo.Uid,
	sum(tpo.PriceInCent) Total
FROM
	t_payment_v2_ods tpo
WHERE NowDate >= '2022-10-05'
group by
	tpo.Uid
order BY 
	Total 
desc
)
WHERE Total >= 0
and Total < 3750;


SELECT count(*) as all from (
SELECT
	tpo.Uid,
	sum(tpo.PriceInCent) Total
FROM
	t_payment_v2_ods tpo
WHERE NowDate >= '2022-10-05'
group by
	tpo.Uid
order BY 
	Total 
desc
);