import request from '@/utils/request'

// 上传图片
export function uploadImage(data) {
  return request({
    url: '/api/v1/s3/image/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除图片
export function deleteImage(key) {
  return request({
    url: '/api/v1/s3/image/delete',
    method: 'delete',
    params: { key }
  })
}

// 图片列表
export function getImageList(query) {
  return request({
    url: '/api/v1/s3/image/list',
    method: 'get',
    params: query
  })
}
