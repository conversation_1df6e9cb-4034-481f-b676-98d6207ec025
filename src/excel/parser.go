package excel

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"tcloud/wolong/cmn"
	"tcloud/wolong/conf"
	"tcloud/wolong/ftpx"
	"tcloud/wolong/model"
	"tcloud/wolong/utils"
	"tcloud/wolong/utils/plog"
)

type Parser struct {
	// 数据
	Enum        *XlsxInfo
	Locale      *XlsxInfo                      // 多语言
	Xlsx        map[string]*XlsxInfo           // 表代号->xlsx数据
	ChannelXlsx map[int32]map[string]*XlsxInfo // 渠道配置数据

	// 中间数据
	EnumSwapDict    map[string]map[string]string            // 枚举替换表 子表->field->内容
	ParamUnfoldDict map[string]map[string]string            // 参数展开表 子表->替换项->内容
	LocaleSwapDict  map[string]map[string]map[string]string // 多语言替换表 子表->语言->field->内容
	Output          map[string]map[int]interface{}          // 待输出的数据 子表->id->内容
	ConstantDict    map[string]bool                         // const表，生成常量配置

	// 内部使用变量
	outputDir string // 输出目录
	stubDir   string // 桩代码目录
	localeDir string // 多语言目录
	genGolang bool   // 是否生成golang桩代码
	genCsharp bool   // 是否生成csharp桩代码
	snakeCase bool   // 会否生成snake_case的字段名
	consul    bool   // 发布到Consul
	plt       bool   // 发布到plt
	ftp       bool   // ftp上传文件

	// 生成特殊标记
	MissChkRepSheet map[string]bool // 去除重复项校验的表

	// 配置检查
	CheckInfo map[int32]model.ConfigCheck

	// 表类型 key:table_name value:table_type
	TableType map[string]string
}

func NewParser(outputDir string, stubDir string, localeDir string, genGolang, genCsharp, snakeCase, consul, ftp, plt bool) *Parser {
	p := new(Parser)
	p.Xlsx = make(map[string]*XlsxInfo)

	p.ChannelXlsx = make(map[int32]map[string]*XlsxInfo)

	p.EnumSwapDict = make(map[string]map[string]string)
	p.ParamUnfoldDict = make(map[string]map[string]string)
	p.LocaleSwapDict = make(map[string]map[string]map[string]string)
	p.Output = make(map[string]map[int]interface{})
	p.ConstantDict = make(map[string]bool)
	p.MissChkRepSheet = make(map[string]bool)
	p.TableType = make(map[string]string)

	p.outputDir = outputDir
	p.stubDir = stubDir
	p.localeDir = localeDir
	p.genGolang = genGolang
	p.genCsharp = genCsharp
	p.snakeCase = snakeCase
	p.consul = consul
	p.plt = plt
	p.ftp = ftp
	return p
}

func (p *Parser) Start() {
	// 加载文件
	p.loadFiles()

	// 准备中间数据
	p.prepare()

	// 解析数据文件
	err := p.parse()
	if err != nil {
		plog.ShowErrorF("解析失败 %v", err)
		return
	}

	plog.Info("解析Excel文件成功。。。")

	// 导出为json
	p.output("json")

	// deprecated 输出多语言文件, 这个部分好像不怎么样
	p.outputLocale()

	// 是否生成桩文件
	p.genStubCode()

	if p.ftp {
		plog.Infof("ftp folder : %s", p.outputDir)
		p.ftpUpload()
	}

	plog.Infof("success！")
}

// 加载文件
func (p *Parser) loadFiles() {
	// 加载枚举文件
	reader := NewXlsxReader(false, false)
	// enumInfo, err := reader.Read("enum", conf.SysCfg.EnumFile, nil, nil)
	enumInfo, err := reader.ReadEnumPublic("enum", conf.SysCfg.EnumFile, nil, nil)
	if err != nil {
		plog.ShowErrorF("读取枚举表失败!! %v", err)
	}
	p.Enum = enumInfo

	tPath := conf.SysCfg.Template.Path

	// 加载xlsx文件
	for tblName, tbl := range conf.Cfg.Tables {
		if tbl.Duplicate { // 对于同一张表的重复映射，不再重复解析
			continue
		}
		// 检查参数
		switch tbl.Type {
		case "", conf.TableTypeClientOnly, conf.TableTypeServerOnly:
		default:
			panic("tableType 不合法")
		}

		plog.Infof("loadFiles table:%s, tbl:%+v, MissRepeat:%+v", tblName, tbl, tbl.MissRepeat)

		reader := NewXlsxReader(tbl.AutoId, tbl.Horizontal)
		info, err := reader.Read(tblName, tPath+"/"+tbl.Workbook, tbl.Sheet, tbl.Enums)
		if err != nil {
			plog.ShowErrorF("读取%s.%s失败！错误码:%v\n", tbl.Workbook, tbl.Sheet, err)
		}
		p.Xlsx[tblName] = info
		p.TableType[tblName] = tbl.Type

		// 读取渠道差异配置
		for _, cInfo := range conf.SysCfg.Channel {
			channelFilePath := fmt.Sprintf("%s/%s", cInfo.Path, tbl.Workbook)

			if utils.IsFileExist(channelFilePath) {
				readerSub := NewXlsxReader(tbl.AutoId, tbl.Horizontal)
				infoSub, errSub := readerSub.Read(tblName, cInfo.Path+"/"+tbl.Workbook, tbl.Sheet, tbl.Enums)
				if errSub != nil {
					plog.ShowErrorF("读取%s.%s失败！错误码:%v\n", tbl.Workbook, tbl.Sheet, err)
				}

				if p.ChannelXlsx[cInfo.Id] == nil {
					p.ChannelXlsx[cInfo.Id] = make(map[string]*XlsxInfo)
				}
				p.ChannelXlsx[cInfo.Id][tblName] = infoSub
			}
		}

		if tbl.Constant { // 记录常量表
			p.ConstantDict[tblName] = true
		}

		if tbl.MissRepeat { // 记录去除重复项校验的表
			p.MissChkRepSheet[tblName] = true
		}
	}

	// 加载多语言文件
	reader = NewXlsxReader(false, false)
	localeInfo, err := reader.Read("locale", conf.SysCfg.LocaleFile, nil, nil)
	if err != nil {
		plog.Info("无多语言表，不进行多语言处理")
	}
	p.Locale = localeInfo
}

func (p *Parser) prepare() {
	// 构建系统枚举替换结构
	p.structEnumSwapMap()

	// 构建参数展开结构
	p.structParamUnfoldMap()

	// 构建多语言替换结构
	p.structLocaleSwapMap()

	// 构建配置检查结构
	if p.plt {
		p.structConfigCheck()
	}
}

// 解析
func (p *Parser) parse() error {

	// 替换系统枚举先
	replaceEnums(p.Xlsx, p.EnumSwapDict, conf.SysCfg.IgnoreLine)

	// 如果有渠道数据也进行替换
	for _, channelInfo := range p.ChannelXlsx {
		replaceEnums(channelInfo, p.EnumSwapDict, conf.SysCfg.IgnoreLine)
	}

	for tableName, info := range p.Xlsx {
		errParse, totalStructs := p.parseXlsx(conf.DefaultChannel, tableName, info)
		if errParse != nil {
			return errParse
		}
		p.Output[tableName] = totalStructs
	}
	return nil
}

// 替换枚举
func replaceEnums(info map[string]*XlsxInfo, enumSwapDict map[string]map[string]string, ignoreLine int) {
	for tableName, table := range info {
		for k, row := range table.Rows {
			for rowIdx := ignoreLine; rowIdx < len(row); rowIdx++ {
				rowNow := row[rowIdx]
				valueNew := table.Rows[k][rowIdx]

				for cellIndex, cellValue := range rowNow {
					// segment 替换
					if row[3][cellIndex] == "segment" && row[0][cellIndex] != "string" {
						split := strings.Split(cellValue, SEGMENT_SPILT)
						newValues := make([]string, 0)
						for _, v := range split {
							findStr := enumSwapDict["enum"][v]
							if findStr != "" {
								newValues = append(newValues, findStr)
							} else {
								newValues = append(newValues, v)
							}
						}
						fndStr := strings.Join(newValues, SEGMENT_SPILT)
						valueNew[cellIndex] = fndStr
						continue
					}

					fndStr := enumSwapDict["enum"][cellValue]
					if fndStr != "" {
						valueNew[cellIndex] = fndStr
						plog.Infof("replace sys enum %s cellValue : %v - %v", tableName, cellValue, fndStr)
					}
				}
			}
		}
	}
}

// 导出
func (p *Parser) output(fmt string) {

	errFold := utils.PathCheckDone(p.outputDir)
	if errFold != nil {
		plog.ShowErrorF("outputDir create fail : %v", errFold)
		return
	}

	fmt = strings.ToLower(fmt)
	switch fmt {
	case "json":
		p.outputJson()
	default:
		plog.ShowErrorF("Invalid output fmt : %v", fmt)
	}
}

func (p *Parser) ftpUpload() {
	localDir := p.outputDir
	// baseDir := "configs/"
	ftpServer := &ftpx.FtpServer{}
	err := ftpServer.Login(
		ftpx.WithHost(conf.SysCfg.FtpInfo.Address),
		ftpx.WithUserName(conf.SysCfg.FtpInfo.User),
		ftpx.WithPassword(conf.SysCfg.FtpInfo.Pwd),
		ftpx.WithBaseDir(conf.SysCfg.FtpInfo.Path))
	if err != nil {
		plog.Error(err)
	}
	defer ftpServer.Close()

	basePath, err := ftpServer.GetRemoteBasePath()
	if err != nil {
		plog.Errorf("get ftp base path fail, err:%v", err)
		return
	}

	ftpServer.UploadDir(conf.SysCfg.FtpInfo.Path, localDir, basePath)
}

func (p *Parser) outputLocale() {
	// 校验localeDir是否存在
	if err := os.MkdirAll(p.localeDir, 0777); err != nil {
		plog.Errorf("创建目录%v失败%v\n", p.localeDir, err)
		return
	}

	// 导出locale文件
	for tableName, localeSwapDict := range p.LocaleSwapDict {
		for locale, swapTable := range localeSwapDict {
			if err := os.MkdirAll(p.localeDir+"/"+locale, 0777); err != nil {
				plog.Errorf("创建多语言目录%v失败\n", p.localeDir)
				continue
			}

			localeFile, err := os.Create(p.localeDir + "/" + locale + "/" + tableName + ".json")
			if err != nil {
				plog.Error(tableName, "生成多语言文件失败", err)
				continue
			}

			encoder := json.NewEncoder(localeFile)
			if err = encoder.Encode(swapTable); err != nil {
				plog.Error(tableName, "导出多语言文件json失败", err)
				continue
			}
		}
	}
}

// 生成桩文件
func (p *Parser) genStubCode() {
	if p.genGolang {
		var goDir = p.stubDir + "/" + cmn.FileGoPackage + "/"
		if err := os.MkdirAll(goDir, 0777); err != nil {
			plog.Errorf("创建目录%v失败%v\n", p.stubDir, err)
			return
		}

		p.genGolangStub(goDir)
		plog.Infof("生成golang桩文件成功！ 保存在%v\n", goDir)
	}

	if p.genCsharp {
		var csDir = p.stubDir + "/cs/"
		if err := os.MkdirAll(csDir, 0777); err != nil {
			plog.Errorf("创建目录%v失败%v\n", p.stubDir, err)
			return
		}

		p.genCsharpStub(csDir)
		plog.Infof("生成csharp桩文件成功！ 保存在%v\n", csDir)
	}
}

func (p *Parser) filterGen(tableName, typ string) bool {
	if p.TableType == nil {
		return false
	}

	if p.TableType[tableName] == "" {
		return true
	}

	return typ == p.TableType[tableName]
}
