<template>
  <el-dialog
    title="图片预览"
    :visible.sync="dialogVisible"
    width="900px"
    class="preview-dialog"
    @close="handleClose"
  >
    <div v-if="image" class="preview-content">
      <div class="image-container">
        <img :src="image.url" :alt="image.file_name" class="preview-image">
      </div>

      <div class="image-details">
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="detail-item">
              <label>文件名:</label>
              <span>{{ image.file_name }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>文件大小:</label>
              <span>{{ formatFileSize(image.size) }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="detail-item">
              <label>存储路径:</label>
              <span>{{ image.key }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>上传时间:</label>
              <span>{{ formatTime(image.last_modified) }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="detail-item full-width">
          <label>访问链接:</label>
          <div class="url-container">
            <el-input
              :value="image.url"
              readonly
              class="url-input"
            />
            <el-button
              type="primary"
              size="small"
              @click="copyUrl"
            >
              复制链接
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button
        type="danger"
        icon="el-icon-delete"
        @click="handleDelete"
      >
        删除图片
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { formatTime, formatFileSize } from '@/utils'

export default {
  name: 'PreviewDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    image: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false
    },

    /** 删除图片 */
    handleDelete() {
      this.$emit('delete', this.image)
    },

    /** 复制链接 */
    async copyUrl() {
      if (!this.image) return

      try {
        await navigator.clipboard.writeText(this.image.url)
        this.$message.success('图片链接已复制到剪贴板')
      } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = this.image.url
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('图片链接已复制到剪贴板')
      }
    },

    /** 格式化时间 */
    formatTime(timestamp) {
      return formatTime(timestamp * 1000) // 转换为毫秒
    },

    /** 格式化文件大小 */
    formatFileSize(size) {
      return formatFileSize(size)
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }
}

.preview-content {
  .image-container {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 8px;

    .preview-image {
      max-width: 100%;
      max-height: 400px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .image-details {
    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      &.full-width {
        flex-direction: column;
        align-items: stretch;
      }

      label {
        font-weight: 500;
        color: #606266;
        min-width: 80px;
        margin-right: 10px;
      }

      span {
        color: #303133;
        word-break: break-all;
      }

      .url-container {
        display: flex;
        gap: 10px;
        margin-top: 5px;

        .url-input {
          flex: 1;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .preview-dialog {
    ::v-deep .el-dialog {
      width: 95% !important;
      margin: 5vh auto;
    }
  }

  .preview-content {
    .image-container {
      padding: 10px;

      .preview-image {
        max-height: 250px;
      }
    }

    .image-details {
      .detail-item {
        flex-direction: column;
        align-items: flex-start;

        label {
          margin-bottom: 5px;
          margin-right: 0;
        }

        .url-container {
          flex-direction: column;
          width: 100%;

          .url-input {
            margin-bottom: 10px;
          }
        }
      }
    }
  }
}
</style>
