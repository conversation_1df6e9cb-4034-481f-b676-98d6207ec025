<template>
  <el-dialog
    title="上传图片"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
  >
    <el-form ref="uploadForm" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="选择文件夹" prop="folder">
        <el-select
          v-model="form.folder"
          placeholder="请选择文件夹"
          style="width: 100%"
        >
          <el-option
            v-for="folder in folderOptions"
            :key="folder.value"
            :label="folder.label"
            :value="folder.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="自定义文件名" prop="file_name">
        <el-input
          v-model="form.file_name"
          placeholder="请输入文件名"
          clearable
        />
        <div class="form-tip">
          文件名仅支持字母、数字、下划线和中划线，扩展名会自动添加
        </div>
      </el-form-item>

      <el-form-item label="选择图片" prop="file" class="upload-item">
        <el-upload
          ref="upload"
          :file-list="fileList"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          :accept="acceptTypes"
          drag
          multiple
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            将图片拖到此处，或<em>点击上传</em>
          </div>
          <div slot="tip" class="el-upload__tip">
            支持 jpg、jpeg、png、gif、webp 格式，单个文件不超过 10MB
          </div>
        </el-upload>
      </el-form-item>

      <!-- 上传进度 -->
      <el-form-item v-if="uploading" label="上传进度">
        <div class="upload-progress">
          <div v-for="(progress, index) in uploadProgress" :key="index" class="progress-item">
            <div class="file-info">
              <span class="file-name">{{ progress.name }}</span>
              <span class="file-status" :class="progress.status">
                {{ getStatusText(progress.status) }}
              </span>
            </div>
            <el-progress
              :percentage="progress.percentage"
              :status="progress.status === 'error' ? 'exception' : (progress.status === 'success' ? 'success' : null)"
              :show-text="false"
            />
          </div>
        </div>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        :loading="uploading"
        :disabled="fileList.length === 0"
        @click="handleUpload"
      >
        {{ uploading ? '上传中...' : '开始上传' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { uploadImage } from '@/api/operate/s3'

export default {
  name: 'UploadDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    folderOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      uploading: false,
      form: {
        folder: '',
        file_name: ''
      },
      fileList: [],
      uploadProgress: [],
      acceptTypes: '.jpg,.jpeg,.png,.gif,.webp',
      maxSize: 10 * 1024 * 1024, // 10MB
      rules: {
        folder: [
          { required: true, message: '请选择文件夹', trigger: 'change' }
        ],
        file_name: [
          { required: true, message: '请输入自定义文件名', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    /** 文件选择改变 */
    handleFileChange(file, fileList) {
      this.fileList = fileList
    },

    /** 文件移除 */
    handleFileRemove(file, fileList) {
      this.fileList = fileList
    },

    /** 上传前检查 */
    beforeUpload(file) {
      // 检查文件类型
      const isImage = /^image\/(jpeg|jpg|png|gif|webp)$/i.test(file.type)
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }

      // 检查文件大小
      if (file.size > this.maxSize) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }

      return true
    },

    /** 开始上传 */
    async handleUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择要上传的图片')
        return
      }

      try {
        await this.$refs.uploadForm.validate()
      } catch (error) {
        return
      }

      this.uploading = true
      this.uploadProgress = this.fileList.map(file => ({
        name: file.name,
        percentage: 0,
        status: 'uploading'
      }))

      let successCount = 0
      let errorCount = 0

      // 并发上传所有文件
      const uploadPromises = this.fileList.map(async(file, index) => {
        try {
          const formData = new FormData()
          formData.append('file', file.raw)
          formData.append('folder', this.form.folder)

          if (this.form.file_name && this.fileList.length === 1) {
            formData.append('file_name', this.form.file_name)
          }

          const response = await uploadImage(formData)

          if (response.code === 200) {
            this.uploadProgress[index].percentage = 100
            this.uploadProgress[index].status = 'success'
            successCount++
          } else {
            throw new Error(response.msg || '上传失败')
          }
        } catch (error) {
          console.error('上传失败:', error)
          this.uploadProgress[index].percentage = 0
          this.uploadProgress[index].status = 'error'
          errorCount++
        }
      })

      await Promise.all(uploadPromises)

      this.uploading = false

      // 显示结果
      if (successCount > 0) {
        this.$message.success(`成功上传 ${successCount} 张图片`)
        this.$emit('upload-success')
      }

      if (errorCount > 0) {
        this.$message.error(`${errorCount} 张图片上传失败`)
      }

      // 如果全部成功，关闭对话框
      if (errorCount === 0) {
        setTimeout(() => {
          this.handleClose()
        }, 1000)
      }
    },

    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },

    /** 重置表单 */
    resetForm() {
      this.form = {
        folder: '',
        file_name: ''
      }
      this.fileList = []
      this.uploadProgress = []
      this.uploading = false
      this.$refs.uploadForm?.resetFields()
      this.$refs.upload?.clearFiles()
    },

    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        uploading: '上传中',
        success: '成功',
        error: '失败'
      }
      return statusMap[status] || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-item {
  ::v-deep .el-form-item__content {
    line-height: normal;
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.upload-progress {
  .progress-item {
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }

    .file-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;

      .file-name {
        font-size: 14px;
        color: #303133;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        margin-right: 10px;
      }

      .file-status {
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 3px;

        &.uploading {
          background: #e6f7ff;
          color: #1890ff;
        }

        &.success {
          background: #f6ffed;
          color: #52c41a;
        }

        &.error {
          background: #fff2f0;
          color: #ff4d4f;
        }
      }
    }
  }
}

::v-deep .el-upload-dragger {
  width: 100%;
}

::v-deep .el-upload__tip {
  margin-top: 7px;
}
</style>
