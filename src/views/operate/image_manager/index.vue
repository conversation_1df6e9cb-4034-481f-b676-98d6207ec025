<template>
  <div class="s3-image-manager">
    <el-card class="box-card">
      <!-- 操作栏 -->
      <div class="operations-bar">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form ref="queryForm" :model="queryParams" :inline="true" size="small">
              <el-form-item label="文件夹">
                <el-select
                  v-model="queryParams.folder"
                  clearable
                  placeholder="选择文件夹"
                  style="width: 200px"
                  @change="handleQuery"
                >
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="folder in folderOptions"
                    :key="folder.value"
                    :label="folder.label"
                    :value="folder.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="12" class="text-right">
            <el-button
              type="primary"
              icon="el-icon-upload"
              @click="showUploadDialog"
            >
              上传图片
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 图片网格 -->
      <div v-loading="loading" class="image-grid">
        <div
          v-for="image in imageList"
          :key="image.key"
          class="image-item"
          @click="previewImage(image)"
        >
          <div class="image-wrapper">
            <img :src="getImageDisplayUrl(image.url)" :alt="image.file_name">
            <div class="image-overlay">
              <div class="image-actions">
                <el-button
                  type="text"
                  icon="el-icon-view"
                  size="small"
                  @click.stop="previewImage(image)"
                >
                  预览
                </el-button>
                <el-button
                  type="text"
                  icon="el-icon-copy-document"
                  size="small"
                  @click.stop="copyImageUrl(image)"
                >
                  复制链接
                </el-button>
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  size="small"
                  class="delete-btn"
                  @click.stop="confirmDeleteImage(image)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
          <div class="image-info">
            <div class="file-name" :title="image.file_name">{{ image.file_name }}</div>
            <div class="file-size">{{ formatFileSize(image.size) }}</div>
            <div class="upload-time">{{ formatTime(image.last_modified) }}</div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && imageList.length === 0" class="empty-state">
          <i class="el-icon-picture-outline" />
          <p>暂无图片</p>
          <el-button type="primary" @click="showUploadDialog">上传第一张图片</el-button>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination-wrapper">
        <el-pagination
          :current-page="queryParams.page_index"
          :page-size="queryParams.page_size"
          :page-sizes="[20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 上传对话框 -->
    <UploadDialog
      :visible.sync="uploadDialogVisible"
      :folder-options="folderOptions"
      @upload-success="handleUploadSuccess"
    />

    <!-- 图片预览对话框 -->
    <PreviewDialog
      :visible.sync="previewDialogVisible"
      :image="currentPreviewImage"
      @delete="confirmDeleteImage"
    />
  </div>
</template>

<script>
import { getImageList, deleteImage } from '@/api/operate/s3'
import UploadDialog from './components/UploadDialog'
import PreviewDialog from './components/PreviewDialog'
import { formatTime, formatFileSize } from '@/utils'

export default {
  name: 'S3ImageManager',
  components: {
    UploadDialog,
    PreviewDialog
  },
  data() {
    return {
      loading: false,
      imageList: [],
      total: 0,
      queryParams: {
        folder: '',
        page_index: 1,
        page_size: 20
      },
      folderOptions: [
        { label: '公告', value: 'ann' },
        { label: '其他', value: 'other' }
      ],
      uploadDialogVisible: false,
      previewDialogVisible: false,
      currentPreviewImage: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 获取图片列表 */
    async getList() {
      this.loading = true
      try {
        const response = await getImageList(this.queryParams)
        if (response.code === 200) {
          this.imageList = response.data.list || []
          this.total = response.data.count || 0
        } else {
          this.$message.error(response.msg || '获取图片列表失败')
        }
      } catch (error) {
        console.error('获取图片列表错误:', error)
        this.$message.error('获取图片列表失败')
      } finally {
        this.loading = false
      }
    },

    /** 搜索按钮 */
    handleQuery() {
      this.queryParams.page_index = 1
      this.getList()
    },

    /** 重置搜索 */
    resetQuery() {
      this.queryParams = {
        folder: '',
        page_index: 1,
        page_size: 20
      }
      this.getList()
    },

    /** 分页大小改变 */
    handleSizeChange(val) {
      this.queryParams.page_size = val
      this.queryParams.page_index = 1
      this.getList()
    },

    /** 当前页改变 */
    handleCurrentChange(val) {
      this.queryParams.page_index = val
      this.getList()
    },

    /** 显示上传对话框 */
    showUploadDialog() {
      this.uploadDialogVisible = true
    },

    /** 上传成功回调 */
    handleUploadSuccess() {
      this.getList()
      this.$message.success('图片上传成功')
    },

    /** 预览图片 */
    previewImage(image) {
      this.currentPreviewImage = image
      this.previewDialogVisible = true
    },

    /** 复制图片链接 */
    async copyImageUrl(image) {
      try {
        await navigator.clipboard.writeText(image.url)
        this.$message.success('图片链接已复制到剪贴板')
      } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = image.url
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('图片链接已复制到剪贴板')
      }
    },

    /** 确认删除图片 */
    confirmDeleteImage(image) {
      this.$confirm(
        `确定要删除图片 "${image.file_name}" 吗？删除后不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.deleteImageItem(image)
      })
    },

    /** 删除图片 */
    async deleteImageItem(image) {
      try {
        const response = await deleteImage(image.key)
        if (response.code === 200) {
          this.$message.success('图片删除成功')
          this.getList()
          // 如果正在预览该图片，关闭预览
          if (this.currentPreviewImage && this.currentPreviewImage.key === image.key) {
            this.previewDialogVisible = false
          }
        } else {
          this.$message.error(response.msg || '删除图片失败')
        }
      } catch (error) {
        console.error('删除图片错误:', error)
        this.$message.error('删除图片失败')
      }
    },

    /** 获取图片显示URL */
    getImageDisplayUrl(url) {
      // 这里可以根据实际情况添加缩略图参数或CDN参数
      return url
    },

    /** 格式化时间 */
    formatTime(timestamp) {
      return formatTime(timestamp * 1000) // 转换为毫秒
    },

    /** 格式化文件大小 */
    formatFileSize(size) {
      return formatFileSize(size)
    }
  }
}
</script>

<style lang="scss" scoped>
.s3-image-manager {
  .box-card {
    .operations-bar {
      margin-bottom: 20px;

      .text-right {
        text-align: right;
      }
    }

    .image-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      min-height: 400px;

      .image-item {
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);

          .image-overlay {
            opacity: 1;
          }
        }

        .image-wrapper {
          position: relative;
          width: 100%;
          height: 180px;
          overflow: hidden;
          background: #f5f7fa;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }

          .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            .image-actions {
              display: flex;
              gap: 8px;

              .el-button {
                color: white;
                border-color: rgba(255, 255, 255, 0.3);

                &:hover {
                  background: rgba(255, 255, 255, 0.1);
                  border-color: rgba(255, 255, 255, 0.5);
                }

                &.delete-btn:hover {
                  background: rgba(245, 108, 108, 0.8);
                  border-color: #f56c6c;
                }
              }
            }
          }
        }

        .image-info {
          padding: 12px;

          .file-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .file-size {
            font-size: 12px;
            color: #909399;
            margin-bottom: 2px;
          }

          .upload-time {
            font-size: 12px;
            color: #c0c4cc;
          }
        }
      }

      .empty-state {
        grid-column: 1 / -1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        color: #909399;

        .el-icon-picture-outline {
          font-size: 64px;
          margin-bottom: 16px;
        }

        p {
          margin: 0 0 20px;
          font-size: 16px;
        }
      }
    }

    .pagination-wrapper {
      margin-top: 30px;
      text-align: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .s3-image-manager {
    .box-card {
      .operations-bar {
        .el-row {
          .el-col {
            &:last-child {
              margin-top: 10px;
              text-align: left;
            }
          }
        }
      }

      .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;

        .image-item {
          .image-wrapper {
            height: 150px;
          }
        }
      }
    }
  }
}
</style>
