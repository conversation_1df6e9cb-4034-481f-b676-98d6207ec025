
// 数据分析
syntax = "proto3";
package taPB;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/ta;taPB";

import "enum.proto";
import "errors.proto";

// 数据上报请求
message DataReportReq {
    common.DATA_REPORT_TYPE report_type = 1;  // 上报类型
    string                  report_data = 2;  // 上报内容
  }
  
  message DataReportRsp {
    common.Result ret = 1;
  }
  
  // 批量数据上报请求
  message BatchDataReportReq {
   repeated DataReportReq reports = 1; // 上报列表
  }

  message BatchDataReportRsp {
    common.Result ret = 1;
  }
  