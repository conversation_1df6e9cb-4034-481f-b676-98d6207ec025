// 登录模块协议
syntax = "proto3";
package taskPB;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task;taskPB";

import "common.proto";
import "errors.proto";
import "enum.proto";


// 获取任务列表
message GetTaskListReq {
    common.TASK_CATEGORY category = 1;
}

message GetTaskListRsp {
    common.Result ret                       = 1;
    repeated      common.TaskInfo task_list = 2;
    common.TASK_CATEGORY category = 3;
}

// 下发推送更新
message UpdateTaskNTF {
    common.Result ret                  = 1;
    repeated      common.TaskInfo info = 2;  // 任务信息
}

// 领取奖励
message RewardTaskReq {
    int64 task_id = 1;  // 任务id
    common.TASK_CATEGORY category = 2;
}

message RewardTaskRsp {
    common.Result   ret                         = 1;
    common.TaskInfo info                        = 2;  // 任务信息
    common.Reward   reward                      = 3;
}

// 获取奖励进度请求
message TaskProgressReq {
    common.TASK_CATEGORY category = 1;  // 任务类型
}

// 获取奖励进度响应
message TaskProgressRsp {
    common.Result        ret               = 1;
    common.TASK_CATEGORY category          = 2;  // 任务类型
    repeated             common.TaskProgress list = 3;  // 进度数据
}

// 广播协议
message TaskProgressNTF {
    common.TASK_CATEGORY category          = 2;  // 任务类型
    repeated             common.TaskProgress list = 3;  // 有变化的进度
}

// 领取奖励进度请求
message TaskProgressRewardReq {
    common.TASK_CATEGORY category = 1;  // 任务类型
    int64                sub_id   = 2;  // 子类型
    int64                index    = 3;  // 领取索引
}

// 领取奖励进度返回
message TaskProgressRewardRsp {
    common.Result       ret    = 1;
    common.TaskProgress info   = 2;  // 进度信息
    common.Reward       reward = 3;  // 奖励信息
}
