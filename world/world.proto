// 天气系统模块
syntax = "proto3";
package worldPB;
//option go_package = "./;weatherPb;";
option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/world;worldPB";

import "common.proto";
import "errors.proto";

message GetWeatherReq {
    int64 pond_id = 1;  // 渔场id
    int32 count   = 2;  // 天数长度 limit<30
}

message GetWeatherRsp {
    common.Result ret                    = 1;
    repeated      common.WeatherDay data = 2;  // 查询列表
    int32         start                  = 3;  // 开始索引
    int32         end                    = 4;  // 下次索引
    int64         pond_id                = 5;  // 渔场id
}

message GetGameTimeReq {
}

message GetGameTimeRsp {
    common.Result   ret  = 1;
    common.GameTime data = 2;
}

message GetWorldTimeReq {
    int64 client_ts = 1; // 客户端时间戳
}

message GetWorldTimeRsp {
    common.Result ret       = 1;
    int64         server_ts = 2;  // 服务器时间戳
    int64         client_ts = 3;  // 客户端时间戳
}

// 同步更新服务器时间
message UpdateServerTimeNtf {
    int64 server_ts = 1; // 服务器时间戳
}